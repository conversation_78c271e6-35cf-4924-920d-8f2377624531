package com.kanzhun.marry.lib_share.entity;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.kanzhun.foundation.constant.ShareKey;
import com.kanzhun.marry.lib_share.core.IShare;
import com.kanzhun.marry.lib_share.core.SharePlatForm;
import com.kanzhun.utils.T;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import org.jetbrains.annotations.Nullable;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

public class ShareWechatMiniProgram implements IShare {

    private Activity activity;

    private IWXAPI api;
    private SharePlatForm type;

    @Nullable
    private Callback callback;

    private static final long SIZE_128K = 128 * 1024L;

    public ShareWechatMiniProgram(Activity activity, @Nullable Callback callback) {
        this.activity = activity;
        this.callback = callback;
    }

    @Override
    public void share(ShareEntity shareInfo) {
        if (shareInfo == null) {
            if(callback != null)callback.dealFail(SharePlatForm.WECHAT, "分享失败");
            return;
        }
        type = shareInfo.getType();
        if (type == null) {
            if(callback != null)callback.dealFail(SharePlatForm.WECHAT, "分享失败");
            return;
        }
        api = WXAPIFactory.createWXAPI(activity, ShareKey.getWxAppId(), true);
        api.registerApp(ShareKey.getWxAppId());
        if (!api.isWXAppInstalled()) {
            T.ss("未检测到您的微信客户端");
            if(callback != null)callback.dealFail(type, "未检测到您的微信客户端");
            return;
        }

        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
        miniProgramObj.webpageUrl = shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_URL);// 兼容低版本的网页链接
        miniProgramObj.miniprogramType = shareInfo.getParams().getInt(WXMiniProgramEntity.KEY_MINI_PROGRAM_TYPE,0);// 正式版:0，测试版:1，体验版:2
        miniProgramObj.userName = shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_ORIGINID); // 小程序原始id
        miniProgramObj.path = shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_PATH); // 小程序路径
        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        msg.title = shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_TITLE);// 小程序消息title
        msg.description = Utils.Companion.getWxDescription(shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_DESCRIPTION));// 小程序消息desc


        String imgUrl = shareInfo.getParams().getString(WXMiniProgramEntity.KEY_MINI_IMAGEURL);
        if (!notFoundFile(imgUrl)) {
            Bitmap bitmap = BitmapFactory.decodeFile(imgUrl);
            if (bitmap != null) {
                msg.thumbData = compressBitmap(bitmap); // 小程序消息封面图片，小于128k
            }
        }

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("miniprogram");
        req.message = msg;
        req.scene = SendMessageToWX.Req.WXSceneSession;// 目前只支持会话
        boolean result = api.sendReq(req);
        if (result) {
            if(callback != null)callback.onSuccess(type);
        }else {
            if(callback != null)callback.dealFail(type, "分享失败");
        }
    }

    private static byte[] compressBitmap(@NonNull Bitmap bitmap) {
        // 检测图片尺寸，需要小于128K
        long streamLength = SIZE_128K;
        int compressQuality = 105;
        ByteArrayOutputStream bmpStream = new ByteArrayOutputStream();
        while (streamLength >= SIZE_128K && compressQuality > 5) {
            try {
                bmpStream.flush();
                bmpStream.reset();
            } catch (IOException e) {
                e.printStackTrace();
            }
            compressQuality -= 5;
            bitmap.compress(Bitmap.CompressFormat.JPEG, compressQuality, bmpStream);
            byte[] bmpPicByteArray = bmpStream.toByteArray();
            streamLength = bmpPicByteArray.length;
        }
        return bmpStream.toByteArray();
    }

    static String buildTransaction(String type) {
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }

    private boolean notFoundFile(String filePath) {
        if (!TextUtils.isEmpty(filePath)) {
            File file = new File(filePath);
            if (!file.exists()) {
                return true;
            }
        } else {
            return true;
        }
        return false;
    }

    static byte[] bmpToByteArray(final Bitmap bmp, boolean needThumb) {
        Bitmap newBmp;
        if (needThumb) {
            int width = bmp.getWidth();
            int height = bmp.getHeight();
            if (width > height) {
                height = height * 150 / width;
                width = 150;
            } else {
                width = width * 150 / height;
                height = 150;
            }
            newBmp = Bitmap.createScaledBitmap(bmp, width, height, true);
        } else {
            newBmp = bmp;
        }
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        newBmp.compress(Bitmap.CompressFormat.JPEG, 100, output);

        byte[] result = output.toByteArray();
        try {
            output.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (!bmp.isRecycled()) {
                bmp.recycle();
            }
            if (!newBmp.isRecycled()) {
                newBmp.recycle();
            }
        }

        return result;
    }

}
