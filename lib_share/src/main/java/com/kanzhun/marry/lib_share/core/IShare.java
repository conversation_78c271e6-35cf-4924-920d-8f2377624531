package com.kanzhun.marry.lib_share.core;

import com.kanzhun.marry.lib_share.entity.ShareEntity;


/**
 * Created by <PERSON>hangxiangdong on 2018/5/15 10:48.
 */
public interface IShare {

    void share(ShareEntity shareEntity);

    interface Callback {
        void onStart(SharePlatForm type,  String desc);

        void onSuccess(SharePlatForm type);
        void dealFail(SharePlatForm type, String desc);
    }

}
