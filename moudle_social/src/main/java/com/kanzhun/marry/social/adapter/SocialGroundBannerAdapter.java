package com.kanzhun.marry.social.adapter;

import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;

import com.kanzhun.common.views.image.OImageView;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.api.bean.SquareBannerBean;
import com.kanzhun.marry.social.viewholder.SocialBannerViewHolder;
import com.youth.banner.adapter.BannerAdapter;

import java.util.List;

public class SocialGroundBannerAdapter extends BannerAdapter<SquareBannerBean, SocialBannerViewHolder> {

    public SocialGroundBannerAdapter(List<SquareBannerBean> bannerList) {
        super(bannerList);
    }

    @Override
    public SocialBannerViewHolder onCreateHolder(ViewGroup parent, int viewType) {
        OImageView imageView = new OImageView(parent.getContext());
        // 注意，必须设置为match_parent，这个是viewpager2强制要求的
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        imageView.setLayoutParams(params);
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);
        imageView.setBackgroundColor(ContextCompat.getColor(parent.getContext(), R.color.common_color_F5F5F5));
        return new SocialBannerViewHolder(imageView);
    }

    @Override
    public void onBindView(SocialBannerViewHolder holder, SquareBannerBean data, int position, int size) {
        holder.imageView.load(data.imgUrl);
    }

}
