package com.kanzhun.marry.social.api.model;

import com.kanzhun.foundation.api.bean.MomentListItemBean;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng
 * Date: 2022/7/13
 */
public class UserDynamicModel {
    /**
     * "moments":[
     * {
     * "momentId": "zzzzzzz", // 动态id
     * "content": "走着走着", // 内容
     * "pictures": [{ // 图片列表
     * "url": "https://orange-qa.weizhipin.com/api/media/download/TsH7Q5gxELKzib1FaJ__S3_zPKZIGPksyGkh.jpeg",
     * "width": 1920,
     * "height": 1080
     * }],
     * "replyCount": 1123, // 评论数量
     * "thumbCount": 122, // 点赞数量
     * "createTime": 1642374343634, // 创建时间
     * }
     * ],
     * "offsetId": "xxx",
     * "hasMore":1 // 是否有下一页, 1-是，0-否
     */
    public List<MomentListItemBean> moments;
    public String offsetId;
    public int hasMore;
}
