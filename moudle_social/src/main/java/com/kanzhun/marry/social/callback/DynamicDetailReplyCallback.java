package com.kanzhun.marry.social.callback;

import com.kanzhun.marry.social.api.model.DynamicReplyBean;

public interface DynamicDetailReplyCallback {
    void jumpToUserDynamicActivity(String userId);

    void clickInput();

    void clickReply(DynamicReplyBean dynamicReplyBean);

    /**
     * 点赞/取消点赞
     */
    void toggleCommentApplaud(DynamicReplyBean replyBean, boolean add);


    void commentLongClick(DynamicReplyBean replyBean, int position);

    boolean canToggleApplaud();
}