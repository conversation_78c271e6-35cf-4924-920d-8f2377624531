package com.kanzhun.marry.social.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.model.SocialUnreadResponse;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.views.ViewPager2Helper;
import com.kanzhun.marry.social.BR;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.callback.SocialNotifyCallback;
import com.kanzhun.marry.social.databinding.SocialActivityNotifyBinding;
import com.kanzhun.marry.social.databinding.SocialNotifyActivityItemTabBinding;
import com.kanzhun.marry.social.viewmodel.SocialNotifyViewModel;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

public class SocialNotifyActivity extends FoundationVMActivity<SocialActivityNotifyBinding, SocialNotifyViewModel> implements SocialNotifyCallback {

    public static void jumpToSocialNotifyActivity(Context context, int index) {
        Intent intent = new Intent(context, SocialNotifyActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA_INT, index);
        AppUtil.startActivity(context, intent);
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.social_activity_notify;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0);
        SocialUnreadResponse unreadResponse = ServiceManager.getInstance().getSocialService().getSocialUnreadCount().getValue();
        ViewPagerFragmentAdapter adapter = new ViewPagerFragmentAdapter(this);
        adapter.setData(getViewModel().getFragments());
        getDataBinding().viewpager.setAdapter(adapter);
        getDataBinding().viewpager.setOffscreenPageLimit(1);
        getDataBinding().viewpager.setCurrentItem(index);
        initMagicIndicator(unreadResponse, index);
        ServiceManager.getInstance().getSocialService().getSocialUnreadCount().observe(this, unRead -> {
            if (unRead != null) {
                getViewModel().setUnreadCountVisible(0, unRead.interactUnreadCount);
                getViewModel().setUnreadCountVisible(1, unRead.interestUnreadCount);
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    private void initMagicIndicator(SocialUnreadResponse unreadResponse, int originIndex) {
        CommonNavigator commonNavigator = new CommonNavigator(this);
        commonNavigator.setFollowTouch(true);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {

            @Override
            public int getCount() {
                return getViewModel().getFragments().size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                CommonPagerTitleView commonPagerTitleView = new CommonPagerTitleView(context);
                SocialNotifyActivityItemTabBinding tabBinding = SocialNotifyActivityItemTabBinding.inflate(LayoutInflater.from(context));
                tabBinding.tvViewPagerTitle.setText(getViewModel().getFragmentTitles().get(index));
                commonPagerTitleView.setContentView(tabBinding.getRoot());
                tabBinding.setVisible(getViewModel().getUnreadCountVisible()[index]);
                commonPagerTitleView.setPadding(QMUIDisplayHelper.dp2px(SocialNotifyActivity.this, 16), 0, QMUIDisplayHelper.dp2px(SocialNotifyActivity.this, 16), 0);
                commonPagerTitleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {

                    @Override
                    public void onSelected(int index, int totalCount) {
                        tabBinding.tvViewPagerTitle.setTextColor(getResources().getColor(R.color.common_black));
                        tabBinding.tvViewPagerTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        tabBinding.tvViewPagerTitle.setTextColor(getResources().getColor(R.color.common_color_929292));
                        tabBinding.tvViewPagerTitle.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {
                    }
                });

                commonPagerTitleView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        getDataBinding().viewpager.setCurrentItem(index, true);
                    }
                });

                return commonPagerTitleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                LinePagerIndicator indicator = new LinePagerIndicator(context);
                indicator.setMode(LinePagerIndicator.MODE_EXACTLY);//MODE_WRAP_CONTENT
                indicator.setLineWidth(QMUIDisplayHelper.dp2px(SocialNotifyActivity.this, 64));
                indicator.setColors(SocialNotifyActivity.this.getResources().getColor(R.color.common_color_7171F6));
                indicator.setRoundRadius(QMUIDisplayHelper.dp2px(SocialNotifyActivity.this, 2));
                return indicator;
            }
        });
        getDataBinding().magicIndicator.setNavigator(commonNavigator);
        ViewPager2Helper.bind(getDataBinding().magicIndicator, getDataBinding().viewpager);
    }
}