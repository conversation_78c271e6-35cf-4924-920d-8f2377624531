package com.kanzhun.marry.social.api.model;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/16.
 */
public class CircleInfoModel {
    public static final int JOINED = 2;
    public static final int UN_JOINED = 1;

    public static final String CHARACTER_BREAK = "- ";// 接口返回的content字段，以"- "分隔，客户端需要在每一行前面显示点

    public String name;// 圈子名称

    public String intro;
    public IntroJsonBean introJson;

    public String avatar;// 圈子头像
    public String memberTitle;// 圈友称呼
    public int userNum;// 圈子人数
    public int joinStatus;// 加入状态 1 未加入 2 已加入

    public static class IntroJsonBean {
        public List<IntroBean> intro;
    }

    public static class IntroBean {
        public String title;// 标题
        public String content;// 正文
    }
}
