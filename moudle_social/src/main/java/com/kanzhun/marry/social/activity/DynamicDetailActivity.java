package com.kanzhun.marry.social.activity;

import static com.kanzhun.foundation.kotlin.ktx.StringExtKt.getIpLocationName;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.animator.interpolator.OvershootInterpolator;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.common.dialog.CommonBindingDialog;
import com.kanzhun.common.dialog.CommonSystemBottomDialog;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.SocialConstants;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.bean.PictureListItemBean;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.CommentDraft;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.router.SocialPageRouter;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.marry.social.BR;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.api.model.DynamicDetailCommentHeaderBean;
import com.kanzhun.marry.social.api.model.DynamicReplyBean;
import com.kanzhun.marry.social.api.model.DynamicThumbUpBean;
import com.kanzhun.marry.social.api.model.UserDynamicDetailModel;
import com.kanzhun.marry.social.callback.CommentDraftCallback;
import com.kanzhun.marry.social.callback.DynamicDetailCallback;
import com.kanzhun.marry.social.callback.ReplyDialogCallback;
import com.kanzhun.marry.social.databinding.SocialActivityDynamicDetailBinding;
import com.kanzhun.marry.social.databinding.SocialDialogAddCircleCanPublishBinding;
import com.kanzhun.marry.social.databinding.SocialDialogSendCommentBinding;
import com.kanzhun.marry.social.databinding.SocialImageScaleBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailCommentBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailCommentHeaderBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailCommentReplyBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailHeaderBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailPicBinding;
import com.kanzhun.marry.social.databinding.SocialItemDynamicDetailThumbUpBinding;
import com.kanzhun.marry.social.fragment.DynamicDetailReplyFragment;
import com.kanzhun.marry.social.model.CircleStatusBean;
import com.kanzhun.marry.social.util.SocialUtil;
import com.kanzhun.marry.social.viewmodel.DynamicDetailViewModel;
import com.kanzhun.marry.social.views.GesturesScaleView;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.kanzhun.utils.views.OnDoubleClickListener;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态详情页
 */
@RouterUri(path = SocialPageRouter.DYNAMIC_DETAIL_ACTIVITY)
public class DynamicDetailActivity extends FoundationVMActivity<SocialActivityDynamicDetailBinding, DynamicDetailViewModel> implements DynamicDetailCallback, OnLoadMoreListener {
    private static final int MAX_SCROLL_HEIGHT = 250;// 滑动最大距离，用于修改顶部titlebar

    private BaseBinderAdapter adapter;
    private CommonBindingDialog inputDialog;
    private EditText etText;
    private CommonBindingDialog.Builder<SocialDialogSendCommentBinding> inputBuilder;
    private long mEnterTime;
    private int totalScrollDy = 0;

    /**
     * 最终的缩放大小
     */
    private float finalScale = 1.0f;

    /**
     * touch 结束的 恢复动画
     */
    private ValueAnimator animator;
    private float downX;
    private float downY;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.social_activity_dynamic_detail;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mEnterTime = System.currentTimeMillis();
        initView();
        initData();
        initListener();
    }

    @Override
    public boolean isShowEmptyViewForRoot() {
        return true;
    }

    @Override
    public void onLoadRetry() {
        getViewModel().requestMomentInfo();
    }

    private void initData() {
        getViewModel().getInfoDeletedLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                getViewModel().setDelete(true);
                getDataBinding().smartRefreshLayout.setVisibility(View.GONE);
                getDataBinding().clInput.setVisibility(View.GONE);
                if (getViewModel().getListItemBean() != null) {
                    RxBus.getInstance().post(getViewModel().getListItemBean(), Constants.SOCIAL_MOMENT_DELETED);
                }
            }
        });

        getViewModel().getHeaderLiveData().observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> objects) {
                getDataBinding().clInput.setVisibility(View.VISIBLE);
                adapter.setList(objects);
                getViewModel().setHeaderPosition(objects.size());
//                getViewModel().refreshReplyList();
                getViewModel().refreshList();
            }
        });

        getViewModel().getTitleBarLiveData().observe(this, new Observer<UserDynamicDetailModel>() {
            @Override
            public void onChanged(UserDynamicDetailModel detailModel) {
                // 用于显示title bar中的头像等信息
                getDataBinding().setItem(detailModel);
            }
        });

        getViewModel().getReplyListLiveData().observe(this, dynamicReplyBeans -> {
            if (!getViewModel().isThumbList()) {
                if (getViewModel().isRefresh()) {
                    getDataBinding().smartRefreshLayout.setNoMoreData(!getViewModel().isHasMore());
                    getDataBinding().smartRefreshLayout.setEnableLoadMore(getViewModel().isHasMore());

                    List<Object> newInstance = new ArrayList<>(getViewModel().getHeaderList());
                    if (!LList.isEmpty(dynamicReplyBeans)) {
                        newInstance.addAll(dynamicReplyBeans);
                    }
                    adapter.setNewInstance(newInstance);

                    if (getViewModel().isShowComment()) {
                        ExecutorFactory.execMainTaskDelay(() -> {
                            getDataBinding().recyclerView.smoothScrollToPosition(getViewModel().getHeaderPosition() - 1);
                            if (getViewModel().isShowInput()) {
                                if (canToggleApplaud()) {
                                    showInputCommentDialog(DynamicDetailActivity.this, null, -1, false);
                                }

                            }
                            if (!TextUtils.isEmpty(getViewModel().getHighlightTopReplyId())) {
                                autoOpenDetailReply(dynamicReplyBeans);
                            }
                        }, 300);
                    }
                } else {
                    if (!LList.isEmpty(dynamicReplyBeans)) {
                        adapter.addData(dynamicReplyBeans);
                    }
                    if (getViewModel().isHasMore()) {
                        getDataBinding().smartRefreshLayout.finishLoadMore();
                    } else {
                        getDataBinding().smartRefreshLayout.finishLoadMoreWithNoMoreData();
                    }
                }
            }
        });

        getViewModel().getDynamicThumbUpListLiveData().observe(this, dynamicThumbUpBeans -> {
            if (getViewModel().isThumbList()) {
                if (getViewModel().isThumbRefresh()) {
                    getDataBinding().smartRefreshLayout.setNoMoreData(!getViewModel().isHasMoreThumb());
                    getDataBinding().smartRefreshLayout.setEnableLoadMore(getViewModel().isHasMoreThumb());

                    List<Object> newInstance = new ArrayList<>(getViewModel().getHeaderList());
                    if (!LList.isEmpty(dynamicThumbUpBeans)) {
                        newInstance.addAll(dynamicThumbUpBeans);
                    }
                    adapter.setNewInstance(newInstance);
                } else {
                    if (!LList.isEmpty(dynamicThumbUpBeans)) {
                        adapter.addData(dynamicThumbUpBeans);
                    }
                    if (getViewModel().isHasMoreThumb()) {
                        getDataBinding().smartRefreshLayout.finishLoadMore();
                    } else {
                        getDataBinding().smartRefreshLayout.finishLoadMoreWithNoMoreData();
                    }
                }
            }
        });

        getViewModel().getDeleteLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (getViewModel().getListItemBean() != null) {
                    RxBus.getInstance().post(getViewModel().getListItemBean(), Constants.SOCIAL_MOMENT_DELETED);
                }
                AppUtil.finishActivity(DynamicDetailActivity.this);
            }
        });

        getViewModel().getInputSendLiveData().observe(this, new Observer<DynamicReplyBean>() {
            @Override
            public void onChanged(DynamicReplyBean dynamicReplyBean) {
                if (etText != null) {
                    etText.setText("");
                }
                if (inputDialog != null) {
                    inputDialog.dismiss();
                }
                if (dynamicReplyBean != null) {
                    if (!getViewModel().isThumbList()) {
                        adapter.addData(getViewModel().getHeaderPosition(), dynamicReplyBean);
                    }
                    // 添加到缓存的数据源
                    getViewModel().getReplyList().add(0, dynamicReplyBean);

                    updateCommentHeader(1);
                }
            }
        });

        getViewModel().getInputReplySendLiveData().observe(this, new Observer<DynamicReplyBean>() {
            @Override
            public void onChanged(DynamicReplyBean replyBean) {
                if (etText != null) {
                    etText.setText("");
                }
                if (inputDialog != null) {
                    inputDialog.dismiss();
                }
                Object item = adapter.getItem(getViewModel().getSendReplyPosition());
                if (item instanceof DynamicReplyBean) {
                    DynamicReplyBean dynamicReplyBean = (DynamicReplyBean) item;
                    dynamicReplyBean.subCount++;
                    if (LList.isEmpty(dynamicReplyBean.subList)) {
                        dynamicReplyBean.subList = new ArrayList<>();
                        dynamicReplyBean.subList.add(replyBean);
                    } else {
                        if (dynamicReplyBean.subList.size() >= 3) {
                            dynamicReplyBean.subList.remove(dynamicReplyBean.subList.size() - 1);
                        }
                        dynamicReplyBean.subList.add(0, replyBean);
                    }
                    adapter.notifyItemChanged(getViewModel().getSendReplyPosition());
                }
                updateCommentHeader(1);
            }
        });

        getViewModel().getDeleteCommentLiveData().observe(this, new Observer<DynamicReplyBean>() {
            @Override
            public void onChanged(DynamicReplyBean replyBean) {
                Object item = adapter.getItem(getViewModel().getDeleteCommentPosition());
                if (item instanceof DynamicReplyBean) {
                    DynamicReplyBean dynamicReplyBean = (DynamicReplyBean) item;
                    if (TextUtils.equals(replyBean.replyId, dynamicReplyBean.replyId)) {
                        //表示删除的是一级评论
                        adapter.removeAt(getViewModel().getDeleteCommentPosition());
                        updateCommentHeader(-(1 + replyBean.subCount));
                    } else {
                        List<DynamicReplyBean> subList = dynamicReplyBean.subList;
                        if (!LList.isEmpty(subList)) {
                            subList.remove(replyBean);
                            dynamicReplyBean.subCount--;
                            adapter.notifyItemChanged(getViewModel().getDeleteCommentPosition());
                            updateCommentHeader(-1);
                        }
                    }
                }
            }
        });

        getViewModel().getShowReplyFragmentLiveData().observe(this, new Observer<DynamicReplyBean>() {
            @Override
            public void onChanged(DynamicReplyBean data) {
                showReplyFragment(data);
            }
        });
        RxBus.getInstance().subscribe(this, Constants.SOCIAL_REFRESH_CIRCLE_JOIN_STATUS, new RxBus.Callback<CircleStatusBean>() {
            @Override
            public void onEvent(CircleStatusBean circleStatusBean) {
                if (circleStatusBean != null && getViewModel().getDetailModel() != null) {
                    if (TextUtils.equals(circleStatusBean.getCircleId(), getViewModel().getDetailModel().circleId)) {
                        getViewModel().getDetailModel().joinStatus = circleStatusBean.getJoinStatus();
                    }
                }
            }
        });
    }

    private void initListener() {
        getDataBinding().recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                totalScrollDy += dy;
                if (totalScrollDy >= MAX_SCROLL_HEIGHT) {
                    getDataBinding().titleBar.tvTitle.setVisibility(View.GONE);
                    getDataBinding().titleBar.llTitleBarName.setVisibility(View.VISIBLE);
                    getDataBinding().titleBar.viewTitleBarBlank.setVisibility(View.VISIBLE);
                } else {
                    getDataBinding().titleBar.tvTitle.setVisibility(View.VISIBLE);
                    getDataBinding().titleBar.llTitleBarName.setVisibility(View.GONE);
                    getDataBinding().titleBar.viewTitleBarBlank.setVisibility(View.GONE);
                }
            }
        });
    }

    private void updateCommentHeader(int changReplyCount) {
        int headerPosition = getViewModel().getHeaderPosition() - 1;
        DynamicDetailCommentHeaderBean headerBean = (DynamicDetailCommentHeaderBean) adapter.getItem(headerPosition);
        headerBean.replyCount = headerBean.replyCount + changReplyCount;
        UserDynamicDetailModel detailModel = getViewModel().getDetailModel();
        detailModel.replyCount = headerBean.replyCount;
        adapter.notifyItemChanged(headerPosition);
    }

    private void initView() {
        String momentId = getIntent().getStringExtra(BundleConstants.BUNDLE_DATA);
        getViewModel().setMomentId(momentId);
        boolean booleanExtra = getIntent().getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN, false);
        getViewModel().setShowComment(booleanExtra);
        boolean booleanExtra1 = getIntent().getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN_1, false);
        getViewModel().setShowInput(booleanExtra1);
        int source = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0);
        getViewModel().setHighlightReplyTd(getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_2));
        getViewModel().setHighlightTopReplyId(getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_3));
        getViewModel().setSource(source);
        getViewModel().requestMomentInfo();
        ((SimpleItemAnimator) getDataBinding().recyclerView.getItemAnimator()).setSupportsChangeAnimations(false);

        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(UserDynamicDetailModel.class, new DynamicDetailHeaderItemBinder(this));
        adapter.addItemBinder(PictureListItemBean.class, new DynamicDetailPicItemBinder(this));
        adapter.addItemBinder(DynamicDetailCommentHeaderBean.class, new DynamicDetailCommentHeaderItemBinder(this));
        if (TextUtils.isEmpty(getViewModel().getHighlightReplyTd()) || !TextUtils.isEmpty(getViewModel().getHighlightTopReplyId())) {
            adapter.addItemBinder(DynamicReplyBean.class, new DynamicDetailCommentItemBinder(this, null, getViewModel()));
        } else {
            adapter.addItemBinder(DynamicReplyBean.class, new DynamicDetailCommentItemBinder(this, getViewModel().getHighlightReplyTd(), getViewModel()));
        }
        adapter.addItemBinder(DynamicThumbUpBean.class, new DynamicDetailThumbUpItemBinder(this));

        getDataBinding().recyclerView.setAdapter(adapter);
        getDataBinding().smartRefreshLayout.setOnLoadMoreListener(this);
        getDataBinding().smartRefreshLayout.setEnableLoadMore(false);
        getDataBinding().smartRefreshLayout.setEnableRefresh(false);

        getDataBinding().titleBar.viewTitleBarBlank.setOnClickListener(new OnDoubleClickListener() {
            @Override
            public void OnDoubleClick(View v) {
                getDataBinding().recyclerView.smoothScrollToPosition(0);
            }
        });
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(DynamicDetailActivity.this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
//        getViewModel().getMoreReplyList();
        getViewModel().getMoreList();
    }

    @Override
    public void jumpToUserDynamicActivity(String userId) {
        if (TextUtils.isEmpty(userId)) return;
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (!checkFormalUser()) {
            return;
        }
        SocialPageRouter.jumpToUserDynamicActivity(DynamicDetailActivity.this, userId);
    }

    @Override
    public void jumpToUserInfoPreviewActivity(DynamicThumbUpBean data) {
        MePageRouter.jumpToInfoPreviewActivity(this, data.userId, null, PageSource.DYNAMIC_ACTIVITY, "", "", "", data.securityId, "",false);
    }

    @Override
    public void clickHeaderMore(UserDynamicDetailModel detailModel) {
        if (getViewModel().isSelf()) {
            showVisibleDialog(DynamicDetailActivity.this, detailModel);
        } else {
            showReportDialog(DynamicDetailActivity.this, getViewModel().getUserId(), getViewModel().getMomentId(), UserReportSource.SOURCE_SOCIAL_DYNAMIC, false, null, -1);
        }
    }

    @Override
    public void clickInput() {
        if (!canToggleApplaud()) return;
        showInputCommentDialog(DynamicDetailActivity.this, null, -1, getViewModel().getSource() == SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF || getViewModel().getSource() == SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
    }

    private DynamicDetailReplyFragment detailReplyFragment;

    /**
     * 自动打开评论回复弹框
     */
    private void autoOpenDetailReply(List<DynamicReplyBean> dynamicReplyBeans) {
        getViewModel().autoOpenDetailReply(dynamicReplyBeans);
    }

    private void showReplyFragment(DynamicReplyBean data) {
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        if (data != null) {
            getDataBinding().bottomSheetParent.setVisibility(View.VISIBLE);
            Bundle bundle = new Bundle();
            bundle.putSerializable(BundleConstants.BUNDLE_DATA, data);
            if (!TextUtils.isEmpty(getViewModel().getHighlightTopReplyId())) {
                bundle.putString(BundleConstants.BUNDLE_DATA_STRING, getViewModel().getHighlightReplyTd());
                getViewModel().setHighlightReplyTd(null);
                getViewModel().setHighlightTopReplyId(null);
            }
            detailReplyFragment = DynamicDetailReplyFragment.getInstance(bundle);
            detailReplyFragment.setReplyDialogCallback(new ReplyDialogCallback() {
                @Override
                public void change(DynamicReplyBean dynamicReplyBean, boolean delete, int changReplyCount) {
                    if (delete) {
                        int itemPosition = adapter.getItemPosition(dynamicReplyBean);
                        if (itemPosition > -1) {
                            adapter.removeAt(itemPosition);
                        }
                    } else {
                        int itemPosition = adapter.getItemPosition(dynamicReplyBean);
                        if (itemPosition > -1) {
                            adapter.notifyItemChanged(itemPosition);
                            if (changReplyCount != 0) {
                                updateCommentHeader(changReplyCount);
                            }
                        }
                    }

                }
            });
            fragmentTransaction
                .replace(R.id.bottom_sheet_parent, detailReplyFragment, DynamicDetailReplyFragment.TAG)
                .commitAllowingStateLoss();
        } else {
            if (detailReplyFragment != null) {
                fragmentTransaction.remove(detailReplyFragment)
                    .commitAllowingStateLoss();
                detailReplyFragment = null;
            }
            getDataBinding().bottomSheetParent.postDelayed(new Runnable() {
                @Override
                public void run() {
                    getDataBinding().bottomSheetParent.setVisibility(View.GONE);
                }
            }, 200);
        }
    }

    @Override
    public void clickMoreReply(DynamicReplyBean data) {
        getViewModel().getShowReplyFragmentLiveData().setValue(data);
    }

    @Override
    public void toggleApplaud(boolean add) {
        UserDynamicDetailModel detailModel = getViewModel().getDetailModel();
        if (add) {
            detailModel.thumbCount++;
            detailModel.thumbStatus = 1;
            getViewModel().momentThumbAdd(getViewModel().getMomentId(), new ICommonCallback() {
                @Override
                public void onSuccess() {
                    // 点赞，刷新点赞列表
                    getViewModel().refreshThumbList();
                }

                @Override
                public void onFail(int code, String msg) {

                }
            });
        } else {
            detailModel.thumbCount--;
            detailModel.thumbStatus = 0;
            getViewModel().momentThumbCancel(getViewModel().getMomentId(), new ICommonCallback() {
                @Override
                public void onSuccess() {
                    // 取消点赞，刷新点赞列表
                    getViewModel().refreshThumbList();
                }

                @Override
                public void onFail(int code, String msg) {

                }
            });
        }
    }

    @Override
    public void toggleCommentApplaud(DynamicReplyBean data, boolean add) {
        if (add) {
            getViewModel().momentCommentThumbAdd(data.replyId, new ICommonCallback() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFail(int code, String msg) {

                }
            });
        } else {
            getViewModel().momentCommentThumbCancel(data.replyId, new ICommonCallback() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFail(int code, String msg) {

                }
            });
        }
    }

    @Override
    public void showInputCommentDialog(Context context, DynamicReplyBean replyBean, int position, boolean showGoCircleDialog) {
        UserDynamicDetailModel detailModel = getViewModel().getDetailModel();
        if (detailModel != null && detailModel.joinStatus == 1) {
            if (showGoCircleDialog) {
                showGoCircleDialog(detailModel);
            } else {
                T.ss(R.string.social_add_circle_can_pubish);
            }
            return;
        }
        if (inputDialog == null) {
            inputBuilder = new CommonBindingDialog.Builder<>(context, R.layout.social_dialog_send_comment);
            inputBuilder.getBinding().etText.setHint(getViewModel().getHint().get());

            InputFilter[] inputFilters = new InputFilter[1];
            LengthNoticeFilter filter = new LengthNoticeFilter(500);
            inputFilters[0] = filter;
            inputBuilder.getBinding().etText.setFilters(inputFilters);

            inputBuilder.getBinding().setViewModel(getViewModel());
            etText = inputBuilder.getBinding().etText;
            inputBuilder.setGravity(Gravity.BOTTOM);
            inputBuilder.setCanceledOnTouchOutside(true);
            inputBuilder.setPadding(0, 0);
            inputBuilder.setAnimationStyle(R.style.common_window_bottom_to_top_anim);
            inputDialog = inputBuilder.createDialog();
            inputBuilder.getBinding().tvSend.setOnClickListener(new OnMultiClickListener() {
                @Override
                public void OnNoMultiClick(View v) {
                    if (TextUtils.isEmpty(getViewModel().getSendReplyId())) {
                        getViewModel().sendComment();
                    } else {
                        getViewModel().sendCommentReply();
                    }
                }
            });
            inputDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            String id;
                            if (TextUtils.isEmpty(getViewModel().getSendReplyId())) {
                                id = "d_" + getViewModel().getMomentId();
                            } else {
                                id = "c_" + getViewModel().getSendReplyId();
                            }
                            if (TextUtils.isEmpty(etText.getText().toString().trim())) {
                                ServiceManager.getInstance().getSocialService().deleteCommentDraftById(id);
                            } else {
                                CommentDraft commentDraft = new CommentDraft();
                                commentDraft.setId(id);
                                commentDraft.setContent(etText.getText().toString().trim());
                                ServiceManager.getInstance().getSocialService().insertCommentDraft(commentDraft);
                            }
                        }
                    });
                }
            });
        }

        String id;
        if (replyBean != null) {
            id = "c_" + replyBean.replyId;
        } else {
            id = "d_" + getViewModel().getMomentId();
        }

        SocialUtil.findCommentDraft(id, new CommentDraftCallback() {
            @Override
            public void content(String content) {
                if (TextUtils.isEmpty(content)) {
                    getViewModel().inputContent.set("");
                    etText.setText("");
                } else {
                    getViewModel().inputContent.set(content);
                    etText.setText(content);
                    etText.setSelection(content.length());
                }
                if (replyBean != null) {
                    getViewModel().setSendReplyId(replyBean.replyId);
                    getViewModel().setSendReplyPosition(position);
                    inputBuilder.getBinding().etText.setHint(getString(R.string.social_dynamic_detail_reply_comment_hint, replyBean.nickName));
                } else {
                    getViewModel().setSendReplyId("");
                    getViewModel().setSendReplyPosition(-1);
                    inputBuilder.getBinding().etText.setHint(getViewModel().getHint().get());
                }
                QMUIKeyboardHelper.showKeyboard(etText, true);
                inputDialog.show();
            }
        });

    }

    private void showGoCircleDialog(UserDynamicDetailModel detailModel) {
        CommonBindingDialog.Builder<SocialDialogAddCircleCanPublishBinding> builder = new CommonBindingDialog.Builder(this, R.layout.social_dialog_add_circle_can_publish)
            .addVariable(BR.avatar, detailModel.circleAvatar)
            .addVariable(BR.title, detailModel.circleName)
            .addVariable(BR.content, detailModel.circleUserNum > 10 ? getResources().getString(R.string.social_circle_join_num, "" + detailModel.circleUserNum) : detailModel.circleIntro);
        builder.setGravity(Gravity.BOTTOM)
            .setCanceledOnTouchOutside(true)
            .setPadding(0, 0)
            .setAnimationStyle(R.style.common_window_bottom_to_top_anim)
            .add(R.id.tv_negative)
            .add(R.id.tv_positive)
            .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
                @Override
                public void onItemClick(Dialog dialog, View view) {
                    dialog.dismiss();
                    if (view.getId() == R.id.tv_positive) {
                        SocialPageRouter.jumpToCircleActivity(DynamicDetailActivity.this, detailModel.circleId);
                    }
                }
            });
        builder.create().show();
    }

    @Override
    public void commentLongClick(DynamicReplyBean replyBean, int position) {
        boolean isSelf = TextUtils.equals(AccountHelper.getInstance().getUserId(), replyBean.userId);
        if (isSelf) {
            CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(this)
                .setCancelContent(getString(R.string.social_delete_comment))
                .setCancelColorRed(true)
                .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                    @Override
                    public void onBottomItemClick(Dialog dialog, View view, int pos) {
                    }

                    @Override
                    public void onCancelClick(Dialog dialog) {
                        showDeleteCommentDialog(DynamicDetailActivity.this, replyBean, position);
                    }
                });
            builder.create().show();
        } else {
            showReportDialog(DynamicDetailActivity.this, replyBean.userId, replyBean.replyId, UserReportSource.SOURCE_SOCIAL_DYNAMIC_COMMENT, true, replyBean, position);
        }
    }

    @Override
    public boolean canToggleApplaud() {
        boolean canApplaud = checkFormalUser();
        if (checkUserCommunityLocked()) {
            canApplaud = false;
        }
        if (checkUserInfoPerfectLocked()) {
            canApplaud = false;
        }
        return canApplaud;
    }

    private boolean checkFormalUser() {
        if (!AccountHelper.getInstance().isFormalUser()) {
            showUnFormalUserDialog();
            return false;
        }
        return true;
    }

    /**
     * 非正式用户dialog
     */
    private void showUnFormalUserDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
            .setTitle(getString(R.string.social_un_formal_user_title))
            .setPositiveText(getResources().getString(R.string.social_jump_to_finish))
            .setNegativeText(getResources().getString(R.string.social_just_have_a_look))
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    // 跳转到我的tab
                    RxBus.getInstance().post("switch", Constants.POST_TAG_MATCHING_REQUIREMENT);
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {

                }
            });
        builder.create().show();
    }

    private boolean checkUserCommunityLocked() {
        if (AccountHelper.getInstance().isUserCommunityLocked()) {
            T.ss(R.string.social_function_locked);
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否被锁定
     */
    private boolean checkUserInfoPerfectLocked() {
        return getViewModel().userProfileLocked();
    }

    private void showDeleteCommentDialog(Context context, DynamicReplyBean replyBean, int position) {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(context)
            .setTitle(getString(R.string.social_dynamic_delete_comment_content))
            .setPositiveText(getResources().getString(R.string.social_cancel_comment_dialog_confirm))
            .setNegativeText(getResources().getString(R.string.social_cancel_dialog_cancel))
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    getViewModel().deleteComment(replyBean, position);
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {

                }
            });
        builder.create().show();
    }

    private void showVisibleDialog(Context context, UserDynamicDetailModel detailModel) {

        CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(context)
            .setCancelContent(getString(R.string.social_dynamic_delete))
            .setCancelColorRed(true)
            .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                @Override
                public void onBottomItemClick(Dialog dialog, View view, int pos) {
                    dialog.dismiss();
                    switch (pos) {
                        case 0:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_ALL);
                            break;
                        case 1:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_SELF);
                            break;
                        case 2:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_FRIEND);
                            break;
                        default:
                            break;
                    }
                }

                @Override
                public void onCancelClick(Dialog dialog) {
                    showDeleteDialog(context);
                }
            });
        boolean isCircle = !TextUtils.isEmpty(detailModel.circleId);
        if (!isCircle) {
            builder.setData(getString(R.string.social_dynamic_all_visible), getString(R.string.social_dynamic_self_visible), getString(R.string.social_dynamic_friend_visible));
        }
        builder.create().show();
    }

    private void showDeleteDialog(Context context) {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(context)
            .setTitle(getString(R.string.social_dynamic_delete_content))
            .setPositiveText(getResources().getString(R.string.social_cancel_dialog_confirm))
            .setNegativeText(getResources().getString(R.string.social_cancel_dialog_cancel))
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    getViewModel().deleteDynamic();
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {

                }
            });
        builder.create().show();
    }

    private void showReportDialog(Context context, String userId, String reportId, @UserReportSource.State int source, boolean comment, DynamicReplyBean replyBean, int position) {
        CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(this)
            .setData(getString(R.string.social_report))
            .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                @Override
                public void onBottomItemClick(Dialog dialog, View view, int pos) {
                    dialog.dismiss();
                    MePageRouter.jumpToUserReportActivity(context, userId, -1, source, reportId, RequestCodeConstants.REQUEST_CODE_0);
                }

                @Override
                public void onCancelClick(Dialog dialog) {
                    if (comment && getViewModel().isSelf()) {
                        showDeleteCommentDialog(DynamicDetailActivity.this, replyBean, position);

                    }
                }
            });
        if (comment && getViewModel().isSelf()) {
            builder.setCancelContent(getString(R.string.social_delete_comment))
                .setCancelColorRed(true);
        } else {
            builder.setCancelContent(getString(R.string.social_report_dismiss));
        }
        builder.create().show();
    }

    @Override
    public void onScaleSet(GesturesScaleView scaleView, View view, View parentView, String url) {
        // cover view 存贮,方便删除添加操作
        List<View> cover = new ArrayList<View>(1);
        scaleView.setListener(new GesturesScaleView.GesturesScaleViewListener() {
            @Override
            public void move(float dx, float dy, double scale) {
                View cView = cover.size() > 0 ? cover.get(0) : null;
                if (cView != null) {
                    cView.setX(cView.getX() + dx);
                    cView.setY(cView.getY() + dy);
                    finalScale *= scale;
                    finalScale = Math.max(finalScale, 1.0f);
                    cView.setPivotX(downX);
                    cView.setPivotY(downY);
                    cView.setScaleX(finalScale);
                    cView.setScaleY(cView.getScaleX());
                }
            }

            @Override
            public void onViewRemoved() {
                if (animator != null) {
                    animator.end();
                }
            }

            @Override
            public void onTouchEnd() {
                View c = cover.size() > 0 ? cover.get(0) : null;
                if (c != null) {
                    if (animator == null) {
                        animator = ValueAnimator.ofFloat(1.0f, 0.0f);
                    }
                    ValueAnimator tempAM = animator;
                    tempAM.setDuration(250);

                    int[] xy = new int[2];
                    view.getLocationInWindow(xy);

                    float viewX = c.getX();
                    float viewY = c.getY();
                    tempAM.setInterpolator(new OvershootInterpolator(0.5f));
                    tempAM.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                        @Override
                        public void onAnimationUpdate(ValueAnimator animation) {
                            float value = (float) animation.getAnimatedValue();
                            float tempOffsetScale = (finalScale - 1) * value + 1;
                            c.setScaleX(tempOffsetScale);
                            c.setScaleY(c.getScaleX());
                            c.setX((viewX - xy[0]) * value + xy[0]);
                            c.setY((viewY - xy[1]) * value + xy[1]);
                        }
                    });

                    tempAM.addListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            view.setVisibility(View.VISIBLE);
                            finalScale = 1.0f;
                            View decorView = DynamicDetailActivity.this.getWindow().getDecorView();
                            ((ViewGroup) decorView).removeView(cover.size() > 0 ? cover.get(0) : null);
                            cover.clear();
                        }
                    });
                    tempAM.start();
                }
            }

            @Override
            public void onTouchStart(float dx, float dy) {
                if (animator != null) {
                    animator.end();
                    animator = null;
                }
                downX = dx;
                downY = dy;
                SocialImageScaleBinding scaleBinding = SocialImageScaleBinding.inflate(LayoutInflater.from(DynamicDetailActivity.this));
                scaleBinding.setUrl(url);
                cover.add(scaleBinding.getRoot());
                View cView = cover.get(0);
                ViewGroup.MarginLayoutParams llp = new ViewGroup.MarginLayoutParams(parentView.getLayoutParams());
                cView.setLayoutParams(llp);
                int[] xy = new int[2];
                view.getLocationInWindow(xy);
                cView.setX(xy[0]);
                cView.setY(xy[1]);
                View decorView = DynamicDetailActivity.this.getWindow().getDecorView();
                ((ViewGroup) decorView).addView(cView);
                view.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        view.setVisibility(View.GONE);
                    }
                }, 200);
            }
        });
    }

    @Override
    public void onSwitchToComments() {
        // 切换到评论列表，替换点赞数据（如果有）为评论数据
        getViewModel().setIsThumbList(false);
        getDataBinding().smartRefreshLayout.setNoMoreData(!getViewModel().isHasMore());
        getDataBinding().smartRefreshLayout.setEnableLoadMore(getViewModel().isHasMore());
        List<DynamicReplyBean> replyList = getViewModel().getReplyList();
        if (replyList.isEmpty()) {
            getViewModel().refreshList();
        } else {
            // 切换数据
            List<Object> newInstance = new ArrayList<>();
            newInstance.addAll(getViewModel().getHeaderList());
            newInstance.addAll(replyList);
            adapter.setNewInstance(newInstance);
        }
    }

    @Override
    public void onSwitchToThumbs() {
        // 切换到点赞列表，替换评论数据（如果有）为点赞数据
        getViewModel().setIsThumbList(true);
        getDataBinding().smartRefreshLayout.setNoMoreData(!getViewModel().isHasMoreThumb());
        getDataBinding().smartRefreshLayout.setEnableLoadMore(getViewModel().isHasMoreThumb());
        List<DynamicThumbUpBean> dynamicThumbUpList = getViewModel().getDynamicThumbUpList();
        if (dynamicThumbUpList.isEmpty()) {
            getViewModel().refreshList();
        } else {
            // 切换数据
            List<Object> newInstance = new ArrayList<>();
            newInstance.addAll(getViewModel().getHeaderList());
            newInstance.addAll(dynamicThumbUpList);
            adapter.setNewInstance(newInstance);
        }
    }

    @Override
    public boolean inComments() {
        return !getViewModel().isThumbList();
    }

    public static class DynamicDetailHeaderItemBinder extends BaseDataBindingItemBinder<UserDynamicDetailModel, SocialItemDynamicDetailHeaderBinding> {

        private DynamicDetailCallback callback;

        public DynamicDetailHeaderItemBinder(DynamicDetailCallback callback) {
            this.callback = callback;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_header;
        }

        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailHeaderBinding> holder, SocialItemDynamicDetailHeaderBinding binding, UserDynamicDetailModel item) {
            binding.setItem(item);
            binding.setCallback(callback);
            binding.tvIp.setText(getContext().getResources().getString(R.string.social_dynamic_detail_ip, getIpLocationName(item.ipLocation)));
        }
    }

    public static class DynamicDetailPicItemBinder extends BaseDataBindingItemBinder<PictureListItemBean, SocialItemDynamicDetailPicBinding> {
        private DynamicDetailCallback callback;

        public DynamicDetailPicItemBinder(DynamicDetailCallback callback) {
            this.callback = callback;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_pic;
        }

        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailPicBinding> holder, SocialItemDynamicDetailPicBinding binding, PictureListItemBean item) {
            binding.setItem(item);
            binding.ivPic.post(new Runnable() {
                @Override
                public void run() {
                    if (callback != null) {
                        callback.onScaleSet(binding.scaleView, binding.ivPic, binding.clContent, item.url);
                    }
                }
            });
        }
    }

    public static class DynamicDetailCommentHeaderItemBinder extends BaseDataBindingItemBinder<DynamicDetailCommentHeaderBean, SocialItemDynamicDetailCommentHeaderBinding> {
        private final DynamicDetailCallback callback;

        public DynamicDetailCommentHeaderItemBinder(DynamicDetailCallback callback) {
            this.callback = callback;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_comment_header;
        }

        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailCommentHeaderBinding> holder, SocialItemDynamicDetailCommentHeaderBinding binding, DynamicDetailCommentHeaderBean item) {
            binding.setItem(item);
            binding.setCallback(callback);
            Resources resources = getContext().getResources();

            //region 评论/获赞 样式（包括空样式）
            if (callback.inComments()) {
                if (item.replyCount == 0) {
                    binding.emptyView.showNoData(resources.getString(R.string.social_dynamic_detail_empty_comment_content));
                    binding.clEmptyComment.setVisibility(View.VISIBLE);
                } else {
                    binding.clEmptyComment.setVisibility(View.GONE);
                }

                binding.tvCommentTitle.setTextColor(resources.getColor(R.color.common_black, null));
                binding.tvThumbTitle.setTextColor(resources.getColor(R.color.common_color_FFB8B8B8, null));
            } else {
                if (item.thumbCount == 0) {
                    binding.emptyView.showNoData(resources.getString(R.string.social_dynamic_detail_empty_thumb_content));
                    binding.clEmptyComment.setVisibility(View.VISIBLE);
                } else {
                    binding.clEmptyComment.setVisibility(View.GONE);
                }

                binding.tvCommentTitle.setTextColor(resources.getColor(R.color.common_color_FFB8B8B8, null));
                binding.tvThumbTitle.setTextColor(resources.getColor(R.color.common_black, null));
            }
            //endregion

            //region 评论/获赞展示样式（条数）
            if (item.replyCount == 0) {
                binding.tvCommentTitle.setText(resources.getString(R.string.social_dynamic_detail_comment_title));
            } else {
                binding.tvCommentTitle.setText(resources.getString(R.string.social_dynamic_detail_comment_title_count, StringUtil.getDynamicFormatNum(item.replyCount)));
            }

            if (item.thumbCount == 0) {
                binding.tvThumbTitle.setText(resources.getString(R.string.social_dynamic_detail_thumb_title));
            } else {
                binding.tvThumbTitle.setText(resources.getString(R.string.social_dynamic_detail_thumb_title_count, StringUtil.getDynamicFormatNum(item.thumbCount)));
            }
            //endregion
        }

        @Override
        protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
            super.bindChildClickViewIds(parent, viewType);
            addChildClickViewIds(R.id.tv_applaud, R.id.tv_message, R.id.tv_circle_name);
        }

        @Override
        public void onChildClick(@NonNull BinderDataBindingHolder<SocialItemDynamicDetailCommentHeaderBinding> holder, @NonNull View view, DynamicDetailCommentHeaderBean data, int position) {
            super.onChildClick(holder, view, data, position);
            if (MultiClickUtil.isMultiClick()) {
                return;
            }
            if (view.getId() == R.id.tv_applaud) {
                if (!callback.canToggleApplaud()) {
                    return;
                }
                if (data.thumbStatus == 1) {
                    // 取消点赞
                    data.thumbStatus = 0;
                    data.thumbCount--;
                    callback.toggleApplaud(false);
                } else {
                    // 点赞
                    data.thumbStatus = 1;
                    data.thumbCount++;
                    callback.toggleApplaud(true);
                }
                getAdapter().notifyItemChanged(position);
            } else if (view.getId() == R.id.tv_message) {
                callback.clickInput();
            } else if (view.getId() == R.id.tv_circle_name) {
                SocialPageRouter.jumpToCircleActivity(view.getContext(), data.circleId);
            }
        }
    }

    /**
     * 评论item
     */
    public static class DynamicDetailCommentItemBinder extends BaseDataBindingItemBinder<DynamicReplyBean, SocialItemDynamicDetailCommentBinding> {

        private DynamicDetailCallback callback;
        private String highReplyId;
        private DynamicDetailViewModel viewModel;

        public DynamicDetailCommentItemBinder(DynamicDetailCallback callback, String replyId, DynamicDetailViewModel viewModel) {
            this.callback = callback;
            this.highReplyId = replyId;
            this.viewModel = viewModel;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_comment;
        }

        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailCommentBinding> holder, SocialItemDynamicDetailCommentBinding binding, DynamicReplyBean item) {
            binding.setItem(item);
            binding.setCallback(callback);
            BaseBinderAdapter baseBinderAdapter = new BaseBinderAdapter();
            baseBinderAdapter.addItemBinder(DynamicReplyBean.class, new DynamicDetailCommentReplyItemBinder(item.replyId, callback));
            binding.subRecycler.setAdapter(baseBinderAdapter);
            ((SimpleItemAnimator) binding.subRecycler.getItemAnimator()).setSupportsChangeAnimations(false);
            baseBinderAdapter.setList(item.subList);
            baseBinderAdapter.setOnItemClickListener(new OnItemClickListener() {
                @Override
                public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                    if (MultiClickUtil.isMultiClick()) {
                        return;
                    }
                    callback.clickMoreReply(item);
                }
            });

            if (highReplyId != null && TextUtils.equals(item.replyId, highReplyId)) {
                highReplyId = null;
                ValueAnimator animator = new ValueAnimator();
                animator.setFloatValues(0f, 1f);
                animator.setDuration(5000);
                animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        float value = (float) animation.getAnimatedValue();
                        if (value >= 1) {
                            binding.clContent.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.common_white));
                        } else {
                            binding.clContent.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.common_color_F2F4FB));
                        }
                    }
                });
                animator.start();
            }

            binding.tvIp.setText(getContext().getResources().getString(R.string.social_dynamic_detail_ip, getIpLocationName(item.ipLocation)));
        }

        @Override
        protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
            super.bindChildClickViewIds(parent, viewType);
            addChildClickViewIds(R.id.tv_applaud);
        }

        @Override
        public void onChildClick(@NonNull BinderDataBindingHolder<SocialItemDynamicDetailCommentBinding> holder, @NonNull View view, DynamicReplyBean data, int position) {
            super.onChildClick(holder, view, data, position);
            if (MultiClickUtil.isMultiClick()) {
                return;
            }
            if (view.getId() == R.id.tv_applaud) {
                if (!callback.canToggleApplaud()) {
                    return;
                }
                if (data.thumbStatus == 1) {
                    // 取消点赞
                    data.thumbStatus = 0;
                    data.thumbCount--;
                    callback.toggleCommentApplaud(data, false);
                } else {
                    // 点赞
                    data.thumbStatus = 1;
                    data.thumbCount++;
                    callback.toggleCommentApplaud(data, true);
                }
                getAdapter().notifyItemChanged(position);
            }
        }

        @Override
        public void onClick(@NonNull BinderDataBindingHolder<SocialItemDynamicDetailCommentBinding> holder, @NonNull View view, DynamicReplyBean data, int position) {
            super.onClick(holder, view, data, position);
            if (MultiClickUtil.isMultiClick()) {
                return;
            }
            if (!callback.canToggleApplaud()) return;
            callback.showInputCommentDialog(view.getContext(), data, position, viewModel.getSource() == SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF || viewModel.getSource() == SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
        }

        @Override
        public boolean onLongClick(@NonNull BinderDataBindingHolder<SocialItemDynamicDetailCommentBinding> holder, @NonNull View view, DynamicReplyBean data, int position) {
            callback.commentLongClick(data, position);
            return super.onLongClick(holder, view, data, position);
        }
    }

    /**
     * 回复评论item
     */
    public static class DynamicDetailCommentReplyItemBinder extends BaseDataBindingItemBinder<DynamicReplyBean, SocialItemDynamicDetailCommentReplyBinding> {

        private String replyId;
        private DynamicDetailCallback callback;

        public DynamicDetailCommentReplyItemBinder(String replyId, DynamicDetailCallback callback) {
            this.replyId = replyId;
            this.callback = callback;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_comment_reply;
        }

        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailCommentReplyBinding> holder, SocialItemDynamicDetailCommentReplyBinding binding, DynamicReplyBean item) {
            binding.setItem(item);
            binding.setCallback(callback);

            binding.llReply.setVisibility(!TextUtils.isEmpty(item.replyNickName) && !TextUtils.equals(replyId, item.replyCommentId) ? View.VISIBLE : View.GONE);

        }
    }

    /**
     * 点赞列表 item
     */
    public static class DynamicDetailThumbUpItemBinder extends BaseDataBindingItemBinder<DynamicThumbUpBean, SocialItemDynamicDetailThumbUpBinding> {

        private final DynamicDetailCallback callback;

        public DynamicDetailThumbUpItemBinder(DynamicDetailCallback callback) {
            this.callback = callback;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.social_item_dynamic_detail_thumb_up;
        }

        @SuppressLint("SetTextI18n")
        @Override
        protected void bind(BinderDataBindingHolder<SocialItemDynamicDetailThumbUpBinding> holder, SocialItemDynamicDetailThumbUpBinding binding, DynamicThumbUpBean item) {
            binding.setItem(item);
            binding.setCallback(callback);

            StringBuilder ageAddress = new StringBuilder();
            ageAddress.append(item.age).append("岁");

            if (!TextUtils.isEmpty(item.address)) {
                ageAddress.append("·");
                ageAddress.append(item.address);
            }

            binding.tvAge.setText(ageAddress.toString());
        }
    }

    @Override
    public void onBackPressed() {
        if (getViewModel().getShowReplyFragmentLiveData().getValue() != null) {
            getViewModel().getShowReplyFragmentLiveData().postValue(null);
            return;
        }
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
        if (!getViewModel().isDelete() && getViewModel().getListItemBean() != null) {
            UserDynamicDetailModel detailModel = getViewModel().getDetailModel();
            if (detailModel != null) {
                MomentListItemBean listItemBean = getViewModel().getListItemBean();
                listItemBean.visibleType = detailModel.visibleType;
                listItemBean.replyCount = detailModel.replyCount;
                listItemBean.thumbCount = detailModel.thumbCount;
                listItemBean.thumbStatus = detailModel.thumbStatus;
                RxBus.getInstance().post(listItemBean, Constants.SOCIAL_MOMENT_UPDATE);
            }
        }
    }
}