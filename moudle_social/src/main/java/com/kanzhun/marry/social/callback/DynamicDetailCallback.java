package com.kanzhun.marry.social.callback;

import android.content.Context;
import android.view.View;

import com.kanzhun.common.callback.TitleBarCallback;
import com.kanzhun.marry.social.api.model.DynamicReplyBean;
import com.kanzhun.marry.social.api.model.DynamicThumbUpBean;
import com.kanzhun.marry.social.api.model.UserDynamicDetailModel;
import com.kanzhun.marry.social.views.GesturesScaleView;

public interface DynamicDetailCallback extends TitleBarCallback {
    void jumpToUserDynamicActivity(String userId);

    void jumpToUserInfoPreviewActivity(DynamicThumbUpBean data);

    void clickHeaderMore(UserDynamicDetailModel detailModel);

    void clickInput();

    void clickMoreReply(DynamicReplyBean item);

    /**
     * 点赞/取消点赞
     */
    void toggleApplaud(boolean add);

    /**
     * 点赞/取消点赞
     */
    void toggleCommentApplaud(DynamicReplyBean item, boolean add);

    void showInputCommentDialog(Context context, DynamicReplyBean replyBean, int position,boolean showGoCircleDialog);

    void commentLongClick(DynamicReplyBean replyBean, int position);

    boolean canToggleApplaud();

    void onScaleSet(GesturesScaleView scaleView, View view,View parentView,String url);

    void onSwitchToComments();

    void onSwitchToThumbs();

    boolean inComments();
}