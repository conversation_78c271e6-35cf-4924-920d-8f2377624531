<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.text.TextUtils"/>
        <import type="com.kanzhun.foundation.Constants"/>
        <import type="com.kanzhun.foundation.kernel.account.AccountHelper"/>
        <variable
            name="item"
            type="com.kanzhun.marry.social.api.model.SocialNotifyFriendItemModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.SocialNotifyFriendCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:paddingTop="24dp"
        android:paddingBottom="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.kanzhun.common.views.image.OAvatarImageView
            android:id="@+id/iv_avatar"
            app:imageUrl="@{item.tinyAvatar}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="42dp"
            android:layout_height="42dp"/>
        <TextView
            tools:text="橘子"
            android:textSize="@dimen/common_text_sp_12"
            android:textColor="@color/common_color_333333"
            android:id="@+id/tv_nick_name"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="@+id/iv_avatar"
            app:layout_constraintStart_toEndOf="@+id/iv_avatar"
            android:text="@{item.nickName}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            tools:text="刚刚"
            android:textSize="@dimen/common_text_sp_12"
            android:textColor="@color/common_color_999999"
            android:id="@+id/tv_time"
            app:layout_constraintTop_toBottomOf="@+id/tv_nick_name"
            app:layout_constraintStart_toStartOf="@+id/tv_nick_name"
            app:socialTime="@{item.createTime}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            tools:text="同意"
            android:layout_marginTop="5dp"
            android:textSize="@dimen/common_text_sp_14"
            android:gravity="center"
            android:id="@+id/tv_apply_action"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:onClick="@{()->callback.dealFriendApply(item)}"
            app:socialNotifyFriendApplyAction="@{item.status}"
            android:layout_width="64dp"
            android:layout_height="32dp"/>

        <TextView
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:maxLines="2"
            android:ellipsize="end"
            app:socialNotifyFriendReason="@{item}"
            android:layout_marginTop="15dp"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_333333"
            android:id="@+id/tv_reason"
            app:layout_constraintTop_toBottomOf="@+id/iv_avatar"
            app:layout_constraintStart_toStartOf="parent"
            android:background="@drawable/social_bg_notify_friend_apply_reason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            app:socialNotifyFriendAgreeByOther="@{item}"
            android:maxLines="2"
            android:drawablePadding="4dp"
            android:text="@string/social_notify_friends_agree_replay"
            android:layout_marginTop="11dp"
            android:drawableLeft="@mipmap/social_notify_friend_agree"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_333333"
            android:id="@+id/tv_status_agree"
            app:layout_constraintTop_toBottomOf="@+id/iv_avatar"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
