<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <import type="com.kanzhun.utils.base.LList" />

        <import type="com.kanzhun.marry.social.R" />

        <import type="androidx.core.content.ContextCompat" />

        <import type="com.kanzhun.foundation.utils.StringUtil" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.api.bean.MomentListItemBean" />

        <variable
            name="position"
            type="int" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.SocialGroundItemCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp">

        <com.kanzhun.common.views.image.OAvatarImageView
            android:id="@+id/ov_avatar"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginLeft="16dp"
            android:onClick="@{()->callback.jumpToUserDynamic(bean)}"
            app:common_circle="true"
            app:imageUrl="@{bean.tinyAvatar}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toRightOf="@+id/ov_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ov_avatar"
            android:layout_marginRight="16dp">
            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:onClick="@{()->callback.jumpToUserDynamic(bean)}"
                android:text="@{bean.nickName}"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_18"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_label_like_relationship"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constrainedWidth="true"
                tools:text="名称名称" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_label_like_relationship"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                app:layout_constraintTop_toTopOf="@+id/tv_name"
                app:layout_constraintBottom_toBottomOf="@+id/tv_name"
                app:layout_constraintLeft_toRightOf="@+id/tv_name"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginLeft="8dp"
                android:paddingLeft="3dp"
                android:paddingRight="4.5dp"
                android:drawableLeft="@drawable/social_ic_like_each_other"
                android:gravity="center"
                app:qmui_backgroundColor="@color/social_color_FD6666_10"
                app:qmui_radius="10dp"
                android:textSize="@dimen/common_text_sp_12"
                android:textColor="@color/social_color_FD6666_80"
                android:text="@string/social_like_each_other"
                app:visibleGone="@{bean.likeEachOther()}"
                tools:visibility="visible"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="16dp"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:maxLines="1"
            app:socialTime="@{bean.createTime}"
            android:textColor="@color/common_color_B7B7B7"
            android:textSize="@dimen/common_text_sp_12"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@+id/ov_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/ov_avatar"
            tools:text="名称名称" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{()->callback.jumpToDynamicDetail(bean,false)}"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/ov_avatar">

            <com.kanzhun.common.views.span.ZPUISpanTextView
                android:id="@+id/stv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="72dp"
                android:layout_marginRight="16dp"
                android:lineHeight="24dp"
                android:paddingBottom="7dp"
                android:layout_marginTop="10dp"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{!TextUtils.isEmpty(bean.content)}"
                app:zpui_stv_expand_color="@color/common_color_7171F6"
                app:zpui_stv_expand_text="@string/social_more"
                app:zpui_stv_max_line="5"
                app:zpui_stv_support_custom_expand="true"
                app:zpui_stv_support_expand="true"
                app:zpui_stv_support_real_expand_or_collapse="false"
                tools:text="内容内容内容内容内容内容内容内容内容内容内容内容内容内容"
                tools:visibility="visible" />

            <include
                layout="@layout/social_item_image_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="72dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:paddingBottom="7dp"
                app:layout_constraintTop_toBottomOf="@+id/stv_content"
                app:setBean="@{bean}"
                app:visibleGone="@{!LList.isEmpty(bean.pictures)}" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_applaud"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="72dp"
            android:layout_marginTop="17dp"
            android:drawableLeft="@{ContextCompat.getDrawable(context, bean.thumbStatus == 1 ? R.mipmap.social_ic_dynamic_applaud_sel : R.mipmap.social_ic_dynamic_applaud)}"
            android:drawablePadding="6dp"
            android:gravity="left|center_vertical"
            android:onClick="@{()->callback.toggleApplaud(bean)}"
            android:text="@{StringUtil.getDynamicFormatNum(bean.thumbCount, @string/social_applaud)}"
            android:textColor="@{bean.thumbStatus == 1 ? @color/social_color_EB5D48 : @color/common_color_4D4D4D}"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_content"
            tools:text="224" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/social_ic_dynamic_message"
            android:drawablePadding="6dp"
            android:gravity="left|center_vertical"
            android:onClick="@{()->callback.jumpToDynamicDetail(bean,true)}"
            android:text="@{StringUtil.getDynamicFormatNum(bean.replyCount, @string/social_message)}"
            android:textColor="@color/common_color_4D4D4D"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/tv_applaud"
            app:layout_constraintLeft_toRightOf="@+id/tv_applaud"
            tools:text="46" />

        <View
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="23dp"
            android:background="@color/common_color_FAFAFA"
            app:layout_constraintTop_toBottomOf="@+id/tv_applaud" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
