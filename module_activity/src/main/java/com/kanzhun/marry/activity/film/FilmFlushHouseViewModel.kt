package com.kanzhun.marry.activity.film

import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.toMutableStateList
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.cachedIn
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.bean.FilmWall
import com.kanzhun.foundation.bean.StubFilmWall
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.http.createApi
import com.kanzhun.marry.activity.film.api.ActivityApi
import com.kanzhun.marry.activity.film.api.bean.EmptyFilmPacket
import com.kanzhun.marry.activity.film.api.bean.FilmPacket
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

const val PAGE_SIZE = 20
const val PREFETCH_DISTANCE = 3

private const val TAG = "FilmFlushHouseViewModel"

class FilmFlushHouseViewModel : BaseViewModel() {

    private val _meetUserNumState = mutableIntStateOf(0)

    private var _isFilmwallLoaded by mutableStateOf(false)

    private val filmPacketList: Flow<PagingData<FilmPacket>> = Pager(
        config = PagingConfig(
            pageSize = PAGE_SIZE,
            prefetchDistance = PREFETCH_DISTANCE
        ),
        pagingSourceFactory = {
            FilmPacketPagingSource(
                onFirstPageLoaded = { meetUserNum, _ ->
                    _meetUserNumState.intValue = meetUserNum
                },
                backend = createApi(ActivityApi::class.java),
                isFilmwallLoaded = { _isFilmwallLoaded }
            )
        },
    ).flow.cachedIn(viewModelScope)

    private val _viewModelState: MutableStateFlow<ViewModelState> =
        MutableStateFlow(
            value = ViewModelState(
                filmPacketList = filmPacketList,
                meetUserNum = _meetUserNumState
            )
        )
    val viewModelState = _viewModelState

    val uiState = _viewModelState
        .map(transform = ViewModelState::toUiState)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = _viewModelState.value.toUiState()
        )

    fun getFilmWall() {
        viewModelScope.launch {
            val backend = createApi(ActivityApi::class.java)
            var page = 1

            var filmWallList = try {
                val response = backend.requestFilmWall(page = page).data

                _viewModelState.update { it.copy(canShow = isQaDebugUser() || response.canShow == true) }

                response.filmWallList?.toMutableStateList()
                    ?: mutableStateListOf()
            } catch (e: Throwable) {
                TLog.error(TAG, "getFilmWall error: $e")
                mutableStateListOf()
            }.apply {
                if (isQaDebugUser()) {
                    clear()
                    addAll(mockFilmWallList())
                }
            }

            var hasFilmWallList = filmWallList.isNotEmpty()
            try {
                if (hasFilmWallList) {
                    // 先清空，再添加
                    _viewModelState.update { it.copy(filmWallList = emptyList<FilmWall>().toMutableStateList()) }

                    appendFilmWallList(filmWallList)

                    val size = filmWallList.size
                    if (size < 5) {
                        appendFilmWallList(mutableStateListOf<FilmWall>().apply {
                            for (i in 1..(5 - size)) {
                                add(StubFilmWall)
                                TLog.debug(TAG, "add stub film wall: $i")
                            }
                        })
                    }
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "getFilmWall error: $e")
            }

            // 照片墙有数据
            _isFilmwallLoaded = true

            // 第一次请求有数据，继续准备下一次请求，直到没有数据为止
            while (hasFilmWallList) {
                delay(1000)

                filmWallList =
                    try {
                        backend.requestFilmWall(page = ++page).data.filmWallList?.toMutableStateList()
                            ?: mutableStateListOf()
                    } catch (e: Throwable) {
                        TLog.error(TAG, "getFilmWall error: $e")
                        mutableStateListOf()
                    }.apply {
                        if (isQaDebugUser()) {
                            addAll(mockFilmWallList())
                        }
                    }

                hasFilmWallList = filmWallList.isNotEmpty()
                if (hasFilmWallList) {
                    appendFilmWallList(filmWallList)
                }
            }
        }
    }

    fun mockFilmWallList(): List<FilmWall> {
        return mutableListOf<FilmWall>().apply {
            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试1",
//                    img = "http://gips0.baidu.com/it/u=1690853528,2506870245&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
                    id = "1"
                )
            )

            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试2",
//                    img = "http://gips2.baidu.com/it/u=195724436,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960",
                    id = "2"
                )
            )

            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试3",
//                    img = "http://gips1.baidu.com/it/u=3874647369,3220417986&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
                    id = "3"
                )
            )

            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试4",
//                    img = "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                    id = "4"
                )
            )

            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试5",
//                    img = "http://gips2.baidu.com/it/u=295419831,2920259701&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
                    id = "5"
                )
            )

            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试6",
//                    img = "https://inews.gtimg.com/om_bt/OQDTT-S6-2ldIPahKpMgoT6-UuztR0kwGC7wEt1abCEvIAA/1000",
                    id = "6"
                )
            )

            for (i in 7..18) {
                add(
                    FilmWall(
                        nickName = "测试用户",
                        content = "测试$i",
//                        img = "http://gips0.baidu.com/it/u=1690853528,2506870245&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
                        id = "$i"
                    )
                )
            }
        }
    }

    private fun appendFilmWallList(filmWallList: SnapshotStateList<FilmWall>) {
        _viewModelState.update { it.copy(filmWallList = it.filmWallList.apply { addAll(filmWallList) }) }
    }
}

class FilmPacketPagingSource(
    private val backend: ActivityApi,
    private val onFirstPageLoaded: (meetUserNum: Int, filmExtractNum: Int) -> Unit = { _, _ -> },
    private val isFilmwallLoaded: () -> Boolean = { false }
) : PagingSource<Int, FilmPacket>() {
    private var loadedItemCount = 0

    override suspend fun load(
        params: LoadParams<Int>
    ): LoadResult<Int, FilmPacket> {
        try {
            val nextPageNumber = params.key ?: 1
            val response = backend.requestFilmHouse(nextPageNumber).data
            val meetUserNum = response.meetUserNum ?: 0
            val filmExtractNum = response.filmExtractNum ?: 0
            val packetList: MutableList<FilmPacket> =
                mutableListOf<FilmPacket>().apply { addAll(response.filmPacketList ?: emptyList()) }

            val hasMore = response.hasMore

            loadedItemCount += packetList.size

            if (hasMore == false && (loadedItemCount) % 2 != 0) { // 没有更多数据了，并且所有已加载为奇数个，补位，用于背景绘制
                packetList.add(EmptyFilmPacket)
            }

            if (nextPageNumber == 1) {
                onFirstPageLoaded(meetUserNum, filmExtractNum)

                // 等 orange/film/filmwall 完事
                while (!isFilmwallLoaded()) {
                    delay(100)
                }
            }

            return LoadResult.Page(
                data = packetList.ifEmpty { emptyList() },
                prevKey = if (nextPageNumber > 1) nextPageNumber - 1 else null,
                nextKey = if (isQaDebugUser()) nextPageNumber else {
                    if (hasMore == true) nextPageNumber + 1 else null
                }
            )
        } catch (e: Exception) {
            return LoadResult.Error(e)
        }
    }

    override val keyReuseSupported: Boolean get() = isQaDebugUser()

    override fun getRefreshKey(state: PagingState<Int, FilmPacket>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}

data class ViewModelState(
    val canShow: Boolean = false,
    val filmWallList: SnapshotStateList<FilmWall> = mutableStateListOf(),
    private val meetUserNum: MutableIntState = mutableIntStateOf(0), // 遇到用户人数
    private val filmExtractNum: Int = 0, // 总抽取胶片数
    private val filmPacketList: Flow<PagingData<FilmPacket>> = emptyFlow()
) {
    fun toUiState(): FilmFlushHouseUiState {
        return FilmFlushHouseUiState(
            canShow = canShow,
            filmWallList = filmWallList,
            meetUserNum = meetUserNum,
            filmPacketList = filmPacketList
        )
    }
}

class FilmFlushHouseUiState(
    val canShow: Boolean = true,
    val filmWallList: SnapshotStateList<FilmWall> = mutableStateListOf(),
    val meetUserNum: MutableIntState = mutableIntStateOf(0), // 遇到用户人数
    val filmPacketList: Flow<PagingData<FilmPacket>> = emptyFlow()
) {
    fun hasFilmRecordData(): Boolean {
        return filmWallList.isNotEmpty() || meetUserNum.intValue > 0
    }
}