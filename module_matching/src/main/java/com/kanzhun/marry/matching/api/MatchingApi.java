package com.kanzhun.marry.matching.api;

import android.util.ArrayMap;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.ShakeSearchModel;
import com.kanzhun.foundation.model.matching.MatchingLikeModel;
import com.kanzhun.foundation.model.matching.MatchingUserInfoModel;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.activity.GetAiOpenChatMsgBean;
import com.kanzhun.marry.matching.api.model.FavorFilterModel;
import com.kanzhun.marry.matching.api.model.GuestsRecommendUserData;
import com.kanzhun.marry.matching.api.model.HomeRecommendUpdateData;
import com.kanzhun.marry.matching.api.model.HomeRecommendUserData;
import com.kanzhun.marry.matching.api.model.MatchBlockGuideModel;
import com.kanzhun.marry.matching.api.model.MatchGamesInfoModel;
import com.kanzhun.marry.matching.api.model.MatchMoodsModel;
import com.kanzhun.marry.matching.api.model.MyLikeBean;
import com.kanzhun.marry.matching.api.model.MySkipBean;
import com.kanzhun.marry.matching.api.model.ShakeCheckModel;
import com.kanzhun.marry.matching.bean.LikeMeListResp;
import com.kanzhun.marry.matching.bean.ParentRecommendListResp;
import com.kanzhun.marry.matching.bean.SeeMeListResp;
import com.kanzhun.marry.matching.bean.UserActivityAlbumResponse;
import com.kanzhun.marry.matching.bean.UserAvatarAdviceResponse;

import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/10
 */
public interface MatchingApi {

    @GET(URLConfig.URL_MATCHING_USER_INFO)
    Observable<BaseResponse<MatchingUserInfoModel>> requestMatchUserInfo(@Query("type") int type, @Query("matchId") String matchId);

    @FormUrlEncoded
    @POST(URLConfig.URL_MATCH_LIKE)
    Observable<BaseResponse<MatchingLikeModel>> sendLike(@FieldMap ArrayMap<String, Object> map);

    @GET("orange/match/getAiOpenChatMsg")
    Observable<BaseResponse<GetAiOpenChatMsgBean>>getAiOpenChatMsg(@Query("friendId") String friendId);

    @FormUrlEncoded
    @POST(URLConfig.URL_MATCH_CHAT)
    Observable<BaseResponse<Object>> sendChat(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_MATCH_FAVOR_FILTER)
    Observable<BaseResponse<FavorFilterModel>> getFavorFilter();

    @GET(URLConfig.URL_MOOD_LIST)
    Observable<BaseResponse<MatchMoodsModel>> requestMoodList();

    @FormUrlEncoded
    @POST(URLConfig.URL_PROFILE_REPLY)
    Observable<BaseResponse> profileReply(@FieldMap ArrayMap<String, Object> map);

    @FormUrlEncoded
    @POST(URLConfig.URL_SHAKE_SEARCH)
    Observable<BaseResponse<ShakeSearchModel>> shakeSearch(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_MATCH_GAMES_INFOS)
    Observable<BaseResponse<MatchGamesInfoModel>> getMatchGamesInfos();

    @GET(URLConfig.URL_BLOCK_MATCH_GUIDE)
    Observable<BaseResponse<MatchBlockGuideModel>> requestBlockMatchGuide();

    @POST(URLConfig.URL_SHAKE_END)
    Observable<BaseResponse> shakeEnd();

    @FormUrlEncoded
    @POST(URLConfig.URL_SHAKE_CHECK)
    Observable<BaseResponse<ShakeCheckModel>> shakeCheck(@FieldMap ArrayMap<String, Object> map);

    @FormUrlEncoded
    @POST(URLConfig.URL_SHAKE_PREPARE)
    Observable<BaseResponse> shakePrepare(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_CHAT_LIKE_LIST)
    Observable<BaseResponse<MyLikeBean>> requestMatchingReviewLikeList();

    @GET(URLConfig.URL_MATCH_SKIP_HISTORY)
    Observable<BaseResponse<MySkipBean>> requestMatchingReviewNoLikeList();

    @GET(URLConfig.URL_MATCHING_RECOMMEND_USERS)
    Observable<BaseResponse<HomeRecommendUserData>> requestMatchingRecommendUsers();

    @POST(URLConfig.URL_MATCHING_LIMITED_ACTIVITY_TASK_RCDCARD_EXCHANGE)
    Observable<BaseResponse<Object>> requestLimitedActivityTaskReCardExchange();

    @GET(URLConfig.URL_MATCHING_ACTIVITY_USERS)
    Observable<BaseResponse<GuestsRecommendUserData>> requestMatchingActivityUsers(@QueryMap Map<String, Object> map);

    @GET(URLConfig.URL_MATCHING_RECOMMEND_IS_UPDATE)
    Observable<BaseResponse<HomeRecommendUpdateData>> requestMatchingRecommendIsUpdate();

    @GET(URLConfig.URL_MATCHING_RECOMMEND_SLIDE_EXPOSE)
    Observable<BaseResponse<Object>> requestMatchingRecommendSlidExpose(@Query("encRecommendUserId") String userId);

    //喜欢我首次请求数据
    @GET(URLConfig.URL_MATCHING_LIKE_ME_PAGE)
    Observable<BaseResponse<LikeMeListResp>> requestMatchingLikeMePage(@Query("offsetId") String offsetId);

    //喜欢我分页数据
    @GET(URLConfig.URL_MATCHING_LIKE_ME_LIST)
    Observable<BaseResponse<LikeMeListResp>> requestMatchingLikeMeList(@Query("groupOffsetId") String groupOffsetId, @Query("groupCode") String groupCode);

    //喜欢我首次请求数据
    @GET(URLConfig.URL_MATCHING_VIEW_ME_PAGE)
    Observable<BaseResponse<SeeMeListResp>> requestMatchingViewMePage(@Query("offsetId") String offsetId);

    //喜欢我分页数据
    @GET(URLConfig.URL_MATCHING_VIEW_ME_LIST)
    Observable<BaseResponse<SeeMeListResp>> requestMatchingViewMeList(@Query("groupOffsetId") String groupOffsetId, @Query("groupCode") String groupCode);

    //我喜欢列表数据
    @GET(URLConfig.URL_MATCHING_I_LIKE_PAGE)
    Observable<BaseResponse<LikeMeListResp>> requestMatchingILikeList(@Query("offsetId") String offsetId);

    //看过我列表未读消除
    @POST(URLConfig.URL_MATCHING_VIEW_LOOK_TAB)
    Observable<BaseResponse<Object>> requestMatchingViewLookMeTab();

    //看过我列表内红点消除
    @FormUrlEncoded
    @POST(URLConfig.URL_UPDATE_VIEW_READ)
    Observable<BaseResponse<Object>> requestMatchingViewLookMeStatus(@Field("userId") String userId);

    //父母转发列表数据
    @GET(URLConfig.URL_PARENT_PARENT_SHARE_LIST)
    Observable<BaseResponse<ParentRecommendListResp>> requestMatchingParentRecommendList(@Query("offsetId") String offsetId);

    //查询用户头像建议记录
    @GET(MatchingURLConfig.URL_USER_AVATAR_ADVICE)
    Observable<BaseResponse<UserAvatarAdviceResponse>> requestUserAvatarAdvice();

    @GET(MatchingURLConfig.URL_USER_ACTIVITY_ALBUM_ALERT)
    Observable<BaseResponse<UserActivityAlbumResponse>> requestUserActivityAlbumAlert();

    //同意头像建议
    @FormUrlEncoded
    @POST(MatchingURLConfig.URL_USER_AVATAR_ADVICE_AGREE)
    Observable<BaseResponse<Object>> requestUserAvatarAdviceAgree(@Field(("id")) String id);

    //拒绝头像建议
    @FormUrlEncoded
    @POST(MatchingURLConfig.URL_USER_AVATAR_ADVICE_REJECT)
    Observable<BaseResponse<Object>> requestUserAvatarAdviceReject(@Field(("id")) String id);

}
