package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.MatchingPageMood;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.api.MatchingApi;
import com.kanzhun.marry.matching.api.model.MatchMoodsModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/2
 */
public class MatchMoodSetViewModel extends FoundationViewModel {
    private MatchingPageMood pageMood;
    private MutableLiveData<List<MatchingPageMood>> moodsLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> updateLiveData = new MutableLiveData<>();

    public MatchMoodSetViewModel(Application application) {
        super(application);
    }

    public MatchingPageMood getPageMood() {
        return pageMood;
    }

    public void setPageMood(MatchingPageMood pageMood) {
        this.pageMood = pageMood;
    }

    public void requestMoods() {
        Observable<BaseResponse<MatchMoodsModel>> responseObservable =
                RetrofitManager.getInstance().createApi(MatchingApi.class).requestMoodList();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<MatchMoodsModel>(true) {
            @Override
            public void onSuccess(MatchMoodsModel data) {
                calculateMoods(data.result);
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                getMooks();
            }
        });
    }

    private void getMooks() {
        MatchingPageMood mood = new MatchingPageMood();
        mood.code = "11";
        mood.icon = "https://orange-rd.weizhipin.com/api/media/download/TsH7RZs3E7qziaRPD5WsSxbEZaYZJoEswkMmUugO6cgU3QlhVGwYzfijYKU~.png";
        mood.name = "迷恋型";

        MatchingPageMood mood2 = new MatchingPageMood();
        mood2.code = "12";
        mood2.icon = "https://orange-rd.weizhipin.com/api/media/download/TsH7RZs3E7qziaRPD5WsSxbEZaYZJoEswkMmUugO6cgU3QlhVGwYzfijYKU~.png";
        mood2.name = "迷恋型2";

        MatchingPageMood mood3 = new MatchingPageMood();
        mood3.code = "13";
        mood3.icon = "https://orange-rd.weizhipin.com/api/media/download/TsH7RZs3E7qziaRPD5WsSxbEZaYZJoEswkMmUugO6cgU3QlhVGwYzfijYKU~.png";
        mood3.name = "迷恋型3";

        MatchingPageMood mood4 = new MatchingPageMood();
        mood4.code = "14";
        mood4.icon = "https://orange-rd.weizhipin.com/api/media/download/TsH7RZs3E7qziaRPD5WsSxbEZaYZJoEswkMmUugO6cgU3QlhVGwYzfijYKU~.png";
        mood4.name = "迷恋型4";

        List<MatchingPageMood> matchingPageMoods = new ArrayList<>();
        matchingPageMoods.add(mood);
        matchingPageMoods.add(mood2);
        matchingPageMoods.add(mood3);
        matchingPageMoods.add(mood4);

        calculateMoods(matchingPageMoods);
    }

    private void calculateMoods(List<MatchingPageMood> result) {
        if (pageMood == null) {
            moodsLiveData.setValue(result);
            return;
        }
        for (MatchingPageMood mood : result) {
            if (TextUtils.equals(mood.code, pageMood.code)) {
                mood.selected = true;
                break;
            }
        }
        moodsLiveData.setValue(result);
    }


    public MutableLiveData<List<MatchingPageMood>> getMoodsLiveData() {
        return moodsLiveData;
    }

    public void updateMood() {
        Map<String, Object> map = new HashMap<>();
        map.put("moodCode", pageMood.code);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOOD_UPDATE, map, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                updateLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public MutableLiveData<Boolean> getUpdateLiveData() {
        return updateLiveData;
    }
}
