package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.ConversationTabSummaryInfo;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.api.MatchingApi;
import com.kanzhun.marry.matching.api.model.MatchBlockGuideModel;

import java.util.concurrent.atomic.AtomicInteger;

import io.reactivex.rxjava3.core.Observable;

public class MatchingRecommendFinishViewModel extends FoundationViewModel {

    // batch接口请求成功
    private MutableLiveData<Boolean> batchRequestLiveData = new MutableLiveData<>();

    public MatchBlockGuideModel guideModel;
    public ConversationTabSummaryInfo summaryInfo;
    private AtomicInteger mRequestThreshold = new AtomicInteger(0);

    public MatchingRecommendFinishViewModel(@NonNull Application application) {
        super(application);
        requestBlockMatchGuide();
        getConversationTab();
    }

    public void requestBlockMatchGuide() {
        Observable<BaseResponse<MatchBlockGuideModel>> responseObservable =
                RetrofitManager.getInstance().createApi(MatchingApi.class).requestBlockMatchGuide();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<MatchBlockGuideModel>(false) {
            @Override
            public void onSuccess(MatchBlockGuideModel data) {
                guideModel = data;
            }

            @Override
            public void onComplete() {
                super.onComplete();
                if (mRequestThreshold.incrementAndGet() >= 2) {
                    afterBatchRequest();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public void getConversationTab() {
        Observable<BaseResponse<ConversationTabSummaryInfo>> baseResponseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).getConversationTab();
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<ConversationTabSummaryInfo>(false) {
            @Override
            public void onSuccess(ConversationTabSummaryInfo data) {
                summaryInfo = data;
            }

            @Override
            public void onComplete() {
                super.onComplete();
                if (mRequestThreshold.incrementAndGet() >= 2) {
                    afterBatchRequest();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    private void afterBatchRequest() {
        batchRequestLiveData.postValue(true);
    }

    public LiveData<ConversationTabSummaryInfo> getConversationTabSummaryInfo() {
//        MutableLiveData<ConversationTabSummaryInfo> summaryInfo = ServiceManager.getInstance().getConversationService().getConversationTabSummaryInfo();
//        if (summaryInfo == null) {
//            summaryInfo = new MutableLiveData<>();
//        }
        return new MutableLiveData<>();
    }

    public MutableLiveData<Boolean> getBlockGuideLiveData() {
        return batchRequestLiveData;
    }

}