package com.kanzhun.marry.matching.api.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/*
{
        "planId":"xxxx",//见面计划id
        "meetPlanStatus":1,//见面计划状态：0:未开启 10-等待中；20-排序中；30-匹配中；40-聊天中；50-已结束；
        "descriptionList":[
            {
                "title":"为什么没有匹配上其他嘉宾？",
                "content":"平台根据双方的偏好意向进行匹配，人气越高的嘉宾越难匹配成功哦。",//中间颜色字段，通过特殊符号隔开，参考官方消息里的点击跳转按钮，开始字符："\u200b"，结束字符："\u2060"
            }
        ],
        "userList":[
            {
                "userId":"xxxxx",//加密用户id
                "tinyAvatar": "xxxx", //用户头像缩略图
                "nickName": "张三", // 昵称
                "intro":"自我介绍",
                "matchScore": 90, // 匹配分
                "recommendTag": ["30岁", "195cm"], // 推荐标签
                "sortNum":1,//顺位，不需要展示时返回0
                "inviteStatus":1,//0-未邀请；1-待确认 2-接受 3-拒绝
                "matchStatus":30,//匹配状态，主要用于解除匹配是使用：0 未匹配 10 系统匹配 20 补充邀请匹配  30 解除匹配',
            }
        ]
    }
 */
data class InviteListResponse(
    @SerializedName("planId")
    var planId: String? = null,
    @SerializedName("meetPlanStatus")
    var meetPlanStatus: Int? = null,
    @SerializedName("descriptionList")
    var descriptionList: List<Description>? = null,
    @SerializedName("userList")
    var userList: List<InviteUser>? = null
) : Serializable {
    data class Description(
        @SerializedName("title")
        var title: String? = null,
        @SerializedName("content")
        var content: String? = null
    ) : Serializable

    data class InviteUser(
        @SerializedName("userId")
        var userId: String? = null,
        @SerializedName("tinyAvatar")
        var tinyAvatar: String? = null,
        @SerializedName("nickName")
        var nickName: String? = null,
        @SerializedName("intro")
        var intro: String? = null,
        @SerializedName("matchScore")
        var matchScore: Int? = null,
        @SerializedName("recommendTag")
        var recommendTag: List<String>? = null,
        @SerializedName("sortNum")
        var sortNum: Int? = null,
        @SerializedName("inviteStatus")
        var inviteStatus: Int? = null,
        @SerializedName("matchStatus")
        var matchStatus: Int? = null,

        var userInput: String? = null,
    ) : Serializable
}