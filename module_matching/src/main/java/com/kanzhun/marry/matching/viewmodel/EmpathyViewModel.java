package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;
import android.text.TextUtils;
import android.util.ArrayMap;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.model.MatchingPageMood;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.OrangeObservableString;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.api.MatchingApi;
import com.kanzhun.foundation.api.model.ShakeSearchModel;
import com.kanzhun.marry.matching.api.model.MatchGamesInfoModel;
import com.kanzhun.marry.matching.api.model.ShakeCheckModel;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class EmpathyViewModel extends FoundationViewModel {
    private static final String TAG = "EmpathyViewModel";
    private HashMap<Observable, Disposable> disposableCache = new HashMap<>();
    private ObservableField<MatchingPageMood> pageMode = new ObservableField<MatchingPageMood>();
    private MutableLiveData<ShakeSearchModel> roomIdLiveData = new MutableLiveData<>();
    private MutableLiveData<Integer> remainTimesLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> checkLiveData = new MutableLiveData<>();
    private ObservableBoolean duringShakeSearch = new ObservableBoolean(false);//是否正在匹配中
    private OrangeObservableString count = new OrangeObservableString("0");
    private ObservableInt remainTimes = new ObservableInt(0);
    private String moodIc;
    private long beginTime;


    public EmpathyViewModel(Application application) {
        super(application);
    }

    public ObservableField<MatchingPageMood> getPageMode() {
        return pageMode;
    }

    public void setPageMode(MatchingPageMood pageMode) {
        this.pageMode.set(pageMode);
    }

    public MutableLiveData<ShakeSearchModel> getRoomIdLiveData() {
        return roomIdLiveData;
    }

    public MutableLiveData<Integer> getRemainTimesLiveData() {
        return remainTimesLiveData;
    }

    public MutableLiveData<Boolean> getCheckLiveData() {
        return checkLiveData;
    }

    public ObservableBoolean getDuringShakeSearch() {
        return duringShakeSearch;
    }

    public void setDuringShakeSearch(boolean duringShakeSearch) {
        this.duringShakeSearch.set(duringShakeSearch);
    }

    public OrangeObservableString getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count.setStringContent(count);
    }

    public ObservableInt getRemainTimes() {
        return remainTimes;
    }

    public void setRemainTimes(int remainTimes) {
        if (remainTimes < 0) {
            this.remainTimes.set(0);
        } else {
            this.remainTimes.set(remainTimes);
        }
    }

    public String getMoodIc() {
        return moodIc;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public void shakeSearch() {
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("beginTime", "" + beginTime);
        Observable<BaseResponse<ShakeSearchModel>> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).shakeSearch(map);
        HttpExecutor.execute(observable, new BaseRequestCallback<ShakeSearchModel>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                disposableCache.put(observable, disposable);
            }

            @Override
            public void handleInChildThread(ShakeSearchModel data) {
                super.handleInChildThread(data);
                roomIdLiveData.postValue(data);
            }

            @Override
            public void onSuccess(ShakeSearchModel data) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                disposableCache.remove(observable);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public void shakeEnd() {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).shakeEnd();
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
            }

            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public void getMatchGamesInfos() {
        Observable<BaseResponse<MatchGamesInfoModel>> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).getMatchGamesInfos();
        HttpExecutor.execute(observable, new BaseRequestCallback<MatchGamesInfoModel>() {
            @Override
            public void handleInChildThread(MatchGamesInfoModel data) {
                super.handleInChildThread(data);
                if (data != null && data.shake != null) {
                    remainTimesLiveData.postValue(data.shake.remainTimes);
                }
            }

            @Override
            public void onSuccess(MatchGamesInfoModel data) {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public void shakeCheck(String roomId) {
        if (TextUtils.isEmpty(roomId)) {
            checkLiveData.postValue(false);
            return;
        }
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("roomId", roomId);
        Observable<BaseResponse<ShakeCheckModel>> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).shakeCheck(map);
        HttpExecutor.execute(observable, new BaseRequestCallback<ShakeCheckModel>(true) {
            @Override
            public void onSuccess(ShakeCheckModel data) {
                checkLiveData.postValue(true);
                if (data != null) {
                    moodIc = data.getIcon();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                checkLiveData.postValue(false);
            }

        });
    }

    public void shakePrepare(String roomId) {
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("roomId", roomId);
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).shakePrepare(map);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {

            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public void cancelDisposableCache() {
        Iterator<Map.Entry<Observable, Disposable>> iterator = disposableCache.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Observable, Disposable> entry = iterator.next();
            disposeRequest(entry.getValue());
            iterator.remove();
        }
    }

    private void disposeRequest(Disposable disposable) {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
    }
}