package com.kanzhun.marry.matching.viewmodel

import androidx.lifecycle.MutableLiveData
import com.hpbr.ui.recyclerview.ListData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.sendObjectLiveEvent
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.adapter.provider.InteractListCardType
import com.kanzhun.marry.matching.api.MatchingApi
import com.kanzhun.marry.matching.bean.InteractLoadData
import com.kanzhun.marry.matching.bean.InteractUserBean
import com.kanzhun.marry.matching.bean.LikeMeFooterBean
import com.kanzhun.marry.matching.bean.LikeMeListResp
import com.kanzhun.marry.matching.bean.LikeMeTitleBean
import com.kanzhun.marry.matching.fragment.interact.perform.InteractGuruGuideStatus
import com.kanzhun.marry.matching.fragment.interact.perform.InteractGuruGuideViewModel

class LikeMeListViewModel : BaseViewModel(), InteractGuruGuideViewModel {

    private var isLoadingData = false

    override var hasGuideBlock: Boolean = false

    override val guruGuideStatus: MutableLiveData<InteractGuruGuideStatus> = MutableLiveData()

    override val avatars: MutableList<String> = mutableListOf()

    override var mBlockInfo: UserGuideBlockInfoBean? = null
    override var total: Int = 0

    var listData: MutableLiveData<ListData<BaseListItem>> = MutableLiveData()

    //存每个分组的偏移，加载数据需要
    private val groupOffsetMap: MutableMap<String?, String?> = mutableMapOf()

    private var pageOffsetId: String? = ""

    override var page = 0

    //加载分组数据
    val loadGroupData: MutableLiveData<InteractLoadData<List<InteractUserBean>>> = MutableLiveData()

    fun isLoading() = isLoadingData

    fun loadData(isRefresh: Boolean) {
        if (isLoadingData) {
            return
        }
        if (isRefresh) {
            pageOffsetId = ""
            //清除偏移
            groupOffsetMap.clear()
            sendObjectLiveEvent(
                LivedataKeyMatching.MATCH_INTERACT_LIST_REFRESH_HINT_DISMISS, "LikeMeListFragment"
            )
        }
        isLoadingData = true
        //喜欢我刷新内容
        val observable =
            RetrofitManager.getInstance().createApi(MatchingApi::class.java)
                .requestMatchingLikeMePage(pageOffsetId)
        HttpExecutor.execute(
            observable,
            object : BaseRequestCallback<LikeMeListResp?>(false) {
                override fun onSuccess(data: LikeMeListResp?) {
                    if (data == null) {
                        pageOffsetId = ""
                        listData.value = ListData(
                            isRefresh = isRefresh,
                            isSuccess = true,
                            hasMore = false,
                            data = mutableListOf()
                        )
                        return
                    }
                    //检查新手引导状态
                    checkGuruGuideStatus(data, isRefresh)
                    pageOffsetId = data.offsetId
                    val cardList = data.cardList ?: listOf()
                    if (cardList.isEmpty()) {
                        listData.value = ListData(
                            isRefresh = isRefresh,
                            isSuccess = true,
                            hasMore = false,
                            data = cardList
                        )

                        if (isRefresh) {
                            showEmpty()
                        }
                    } else {
                        increasePage(isRefresh)

                        if (isRefresh) {
                            ServiceManager.getInstance().conversationService?.updateInteractTab()
                        }

                        val tempList = mutableListOf<BaseListItem>()

                        if (isRefresh) {
                            tempList.add(data.also {
                                it.mLocalItemType = InteractListCardType.HEADER_CARD.value
                            })
                        }

                        cardList.forEach { card ->
                            //如果有头，添加头
                            if (!card.title.isNullOrBlank()) {
                                tempList.add(LikeMeTitleBean(card.title ?: "").also { titleBean ->
                                    titleBean.mLocalItemType = InteractListCardType.TITLE_CARD.value
                                })
                            }

                            //添加数据
                            tempList.add(card)

                            //如果有尾，添加尾部
                            if (card.hasMoreBtn) {
                                tempList.add(LikeMeFooterBean(card).also { footer ->
                                    groupOffsetMap[card.groupCode] = card.groupOffsetId
                                    footer.mLocalItemType = InteractListCardType.FOOTER_CARD.value
                                })

                            }
                        }
                        listData.value = ListData(isRefresh, true, data.hasMore ?: false, tempList)
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                    listData.value = ListData(
                        isRefresh = isRefresh,
                        isSuccess = false,
                        hasMore = false,
                        data = emptyList()
                    )

                    if (isRefresh) {
                        showError()
                    }
                }

                override fun onComplete() {
                    super.onComplete()
                    isLoadingData = false
                }
            })
    }

    /**
     * 分组内加载数据
     */
    fun loadGroupNextData(footerBean: LikeMeFooterBean) {
        val groupCode = footerBean.lastBean.groupCode
        if (groupCode.isNullOrBlank()) {
            return
        }
        if (isLoadingData) {
            return
        }
        isLoadingData = true

        val observable =
            RetrofitManager.getInstance().createApi(MatchingApi::class.java)
                .requestMatchingLikeMeList(groupOffsetMap[groupCode], groupCode)
        HttpExecutor.execute(
            observable,
            object : BaseRequestCallback<LikeMeListResp?>(false) {
                override fun onSuccess(data: LikeMeListResp?) {
                    if (data == null) {
                        return
                    }

                    groupOffsetMap[groupCode] = data.groupOffsetId
                    loadGroupData.value = InteractLoadData(
                        groupCode,
                        footerBean,
                        data.hasMore ?: false,
                        data.cardList ?: listOf()
                    )
                }

                override fun dealFail(reason: ErrorReason?) {
                }

                override fun onComplete() {
                    super.onComplete()
                    isLoadingData = false
                }
            })
    }

    fun viewLikeMe(userId: String?) {
        if (userId.isNullOrBlank()) {
            return
        }
        val params: MutableMap<String, Any> = HashMap()
        params["userId"] = userId
        HttpExecutor.requestSimplePost(
            URLConfig.URL_VIEW_LIKE_ME_V2,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {
                    ServiceManager.getInstance().conversationService?.updateInteractTab()
                }

                override fun dealFail(reason: ErrorReason?) {
                }
            })
    }

    fun findUnreadPosition(): Int {
        val list = listData.value?.data ?: listOf()
        for (i in list.indices) {
            val item = list[i]
            // 是否已读 0 未读 1 已读
            if (item is InteractUserBean && item.readStatus == 0) {
                return i
            }
        }
        return -1
    }

}

