package com.kanzhun.marry.matching.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.base.FoundationViewModel
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.service.IMeRouterService
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.api.MatchingApi
import com.kanzhun.marry.matching.bean.UserAvatarAdviceResponse
import com.kanzhun.utils.base.LText
import com.kanzhun.utils.platform.BuildInfoUtils
import com.sankuai.waimai.router.Router

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/24
 */
class MatchingF2ViewModel(application: Application?) : FoundationViewModel(application) {
    @JvmField
    var matchStatus: Int = 0

    @JvmField
    var phase: Int = 0



}
