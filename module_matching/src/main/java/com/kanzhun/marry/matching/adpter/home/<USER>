package com.kanzhun.marry.matching.adpter.home

import android.util.TypedValue
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ui.BlurTextView
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.api.model.CertTag

class HomeIdentityTagAdapter(list: List<CertTag>) : BaseQuickAdapter<CertTag, BaseViewHolder>(R.layout.common_identity_info_item, list.toMutableList()) {
    override fun convert(holder: BaseViewHolder, item: CertTag) {
        val textView = holder.getView<BlurTextView>(R.id.tvTagName)
        textView.binding.tvBlurText.run {
            setTextColor(R.color.common_color_5E5E5E.toResourceColor())
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13F)
        }
        textView.setText(item.isBlur, item.content)
        holder.setGone(R.id.ivTagIcon, !item.hasIcon())
    }


}