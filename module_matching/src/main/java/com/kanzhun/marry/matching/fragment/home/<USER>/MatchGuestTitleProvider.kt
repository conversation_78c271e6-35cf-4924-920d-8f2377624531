package com.kanzhun.marry.matching.fragment.home.performance

import android.content.Context
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.marry.matching.bean.MatchGuestsTitleBean
import com.kanzhun.marry.matching.databinding.MatchingGuideItemUserTitleBinding

class MatchGuestTitleProvider(val content: Context) : BaseItemProvider<BaseListItem, MatchingGuideItemUserTitleBinding>() {
    override fun onBindItem(binding: MatchingGuideItemUserTitleBinding, item: BaseListItem) {
        if(item is MatchGuestsTitleBean){
            binding.apply {
                if(item.activityInfo?.showImages?.isNotEmpty() == true){
                    ivAvatar.load(item.activityInfo.showImages[0])
                }
                idTitle.text = item.activityInfo?.title
                idTextTime.text = "时间："+item.activityInfo?.timeStr
                idTextLocal.text = "地点："+item.activityInfo?.address
//                idCard.onClick {
//                    ProtocolHelper.parseProtocol(item.activityInfo?.jumpUrl)
//                }
            }
        }
    }

}
