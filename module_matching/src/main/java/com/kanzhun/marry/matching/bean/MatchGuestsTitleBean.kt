package com.kanzhun.marry.matching.bean

import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItemBean
import com.kanzhun.foundation.api.model.ThumbBean
import com.kanzhun.foundation.model.profile.UserTabModel
import com.kanzhun.marry.matching.api.model.IPageBean

class MatchGuestsTitleBean(val activityInfo: UserTabModel.ActivityBean? = null) : BaseListItemBean()


class MatchGuestsItemBean(val iPageBean:IPageBean? = null) : BaseListItemBean()