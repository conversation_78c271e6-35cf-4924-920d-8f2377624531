package com.kanzhun.marry.matching.bean

import com.kanzhun.foundation.model.PhotoItem
import java.io.Serializable

class UserAvatarAdviceResponse(
    var id: String? = "",
    var url: String? = "",
    var width: Int? = 198,
    var height: Int? = 249
) : Serializable {
    fun width(): Int {
        return width ?: 198
    }

    fun height(): Int {
        return height ?: 249
    }
}

data class UserActivityAlbumResponse(
    val jumpUrl: String? = "",
    val photoList: MutableList<PhotoItem>
) : Serializable