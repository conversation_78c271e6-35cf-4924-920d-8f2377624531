package com.kanzhun.marry.matching.views;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.activity.fragment.BaseFragment;
import com.kanzhun.common.animator.BaseViewAnimator;
import com.kanzhun.common.animator.YoYo;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;
import com.kanzhun.marry.matching.fragment.MatchingRecommendBlockFragment;
import com.kanzhun.marry.matching.fragment.MatchingUserInfoFragment;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>g<PERSON>g
 * Date: 2022/5/16
 */
public class MatchingSwitcherLayout extends FrameLayout {
    public static final int TYPE_ANIMATOR_PULL_LOAD = 1; //下拉
    public static final int TYPE_ANIMATOR_UP_LOAD = 2; //上拉

    private BaseViewAnimator mInAnimation;
    private BaseViewAnimator mOutAnimation;
    private int moveDistance;
    private int animatorType = 2;

    private FrameLayout flOne;
    private FrameLayout flTwo;
    private int mWhichChild = 0;
    private FragmentManager fragmentManager;
    private FragmentTransaction fragmentTransaction;
    private AnimatorListenerAdapter animatorListenerAdapter;
    private BaseFragment currFragment;
    private BaseFragment aboveFragment;


    public MatchingSwitcherLayout(@NonNull Context context) {
        this(context, null);
    }

    public MatchingSwitcherLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MatchingSwitcherLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        moveDistance = QMUIDisplayHelper.dp2px(context, 400);
        flOne = new FrameLayout(context);
        flOne.setId(View.generateViewId());
        addView(flOne, 0, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        flTwo = new FrameLayout(context);
        flTwo.setId(View.generateViewId());
        addView(flTwo, 1, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        super.addView(child, index, params);
        if (getChildCount() == 1) {
            child.setVisibility(View.VISIBLE);
        } else {
            child.setVisibility(View.GONE);
        }
    }

    public void setFragmentManager(FragmentManager fragmentManager) {
        this.fragmentManager = fragmentManager;

    }

    /**
     * 加载首个fragment
     *
     * @param fragment
     */
    public void setCurrentFragment(BaseFragment fragment, AnimatorListenerAdapter animatorListenerAdapter) {
        changeFragment(fragment);
        FrameLayout currentView = getCurrentView();
        currentView.setVisibility(View.GONE);
        currentView.removeAllViews();
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(currentView.getId(), fragment);
        fragmentTransaction.commit();
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", moveDistance, 0),
                        ObjectAnimator.ofFloat(target, "scaleX", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 0.9f, 1.0f));
            }
        };
        currentView.postDelayed(new Runnable() {
            @Override
            public void run() {
                startAnimator(currentView, mInAnimation, animatorListenerAdapter);
                currentView.setVisibility(View.VISIBLE);
            }
        }, 200);
    }

    public FrameLayout getCurrentView() {
        return (FrameLayout) getChildAt(mWhichChild);
    }

    public FrameLayout getNextView() {
        int which = mWhichChild == 0 ? 1 : 0;
        return (FrameLayout) getChildAt(which);
    }

    /**
     * 下啦加载上一个fragment
     */
    public void setAboveFragment(BaseFragment fragment, AnimatorListenerAdapter animatorListenerAdapter) {
        changeFragment(fragment);
        this.animatorListenerAdapter = animatorListenerAdapter;
        FrameLayout nextView = getNextView();
        nextView.removeAllViews();
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(nextView.getId(), fragment);
        fragmentTransaction.commit();
        setAboveAnimator();
        showNext();
    }

    /**
     * 上拉加载下一个fragment
     */
    public void setNextFragment(BaseFragment fragment, AnimatorListenerAdapter animatorListenerAdapter) {
        changeFragment(fragment);
        this.animatorListenerAdapter = animatorListenerAdapter;
        FrameLayout nextView = getNextView();
        nextView.removeAllViews();
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(nextView.getId(), fragment);
        fragmentTransaction.commit();
        setNextAnimator();
        showNext();
    }

    /**
     * 当前fragment 消失
     */
    public void setCurrentFragmentDismiss() {
        if (currFragment != null) {
            if (currFragment instanceof MatchingUserInfoFragment) {
                MatchingUserInfoFragment infoFragment = (MatchingUserInfoFragment) currFragment;
                if (infoFragment.getPopupCertShow() != null) {
                    infoFragment.getPopupCertShow().dismiss();
                }
            }
        }
        aboveFragment = currFragment;
        FrameLayout currentView = getCurrentView();
        mOutAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", 0, -moveDistance),
                        ObjectAnimator.ofFloat(target, "scaleX", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0));
            }
        };
        startAnimator(currentView, mOutAnimation, new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                currentView.setVisibility(GONE);
                removeInvisibleFragment();
            }
        });
    }

    /**
     * 当前fragment 消失
     */
    public void setCurrentFragmentDismissForNoAnim() {
        if (currFragment != null) {
            if (currFragment instanceof MatchingUserInfoFragment) {
                MatchingUserInfoFragment infoFragment = (MatchingUserInfoFragment) currFragment;
                if (infoFragment.getPopupCertShow() != null) {
                    infoFragment.getPopupCertShow().dismiss();
                }
            }
        }
        aboveFragment = currFragment;
        FrameLayout currentView = getCurrentView();
        currentView.setVisibility(GONE);
        removeInvisibleFragment();
    }

    public void setOnlyNextFragment(BaseFragment fragment, AnimatorListenerAdapter animatorListenerAdapter) {
        changeFragment(fragment);
        setWhichChild(mWhichChild + 1);
        FrameLayout nextView = getCurrentView();
        nextView.removeAllViews();
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(nextView.getId(), fragment);
        fragmentTransaction.commit();
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", moveDistance, 0),
                        ObjectAnimator.ofFloat(target, "scaleX", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        };
        nextView.postDelayed(new Runnable() {
            @Override
            public void run() {
                startAnimator(nextView, mInAnimation, animatorListenerAdapter);
                nextView.setVisibility(View.VISIBLE);
            }
        }, 300);
    }

    /**
     * 点击喜欢进入阻断页动画
     */
    public void setNextBlockFragmentForLike(MatchingRecommendBlockFragment fragment) {
        changeFragment(fragment);
        FrameLayout nextView = getNextView();
        nextView.removeAllViews();
        fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(nextView.getId(), fragment);
        fragmentTransaction.commit();
        setNextBlockForLikeAnimator();
        setWhichChild(mWhichChild + 1);
        boolean hasFocus = getFocusedChild() != null;
        showOnlyForLike(mWhichChild);
        if (hasFocus) {
            requestFocus(FOCUS_FORWARD);
        }
    }

    private void changeFragment(BaseFragment fragment){
        if (currFragment != null) {
            if (currFragment instanceof MatchingUserInfoFragment) {
                MatchingUserInfoFragment infoFragment = (MatchingUserInfoFragment) currFragment;
                if (infoFragment.getPopupCertShow() != null) {
                    infoFragment.getPopupCertShow().dismiss();
                }
            }
        }
        aboveFragment = currFragment;
        currFragment = fragment;
    }

    /**
     * 点击阻断页稍后再说回到用户匹配页
     */
    public void setBackFragmentForBlock(AnimatorListenerAdapter animatorListenerAdapter) {
        setBackForBlockAnimator();
        setWhichChild(mWhichChild + 1);
        boolean hasFocus = getFocusedChild() != null;
        final int count = getChildCount();
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);
            if (i == mWhichChild) {
                child.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        YoYo.with(mInAnimation).duration(300).withListener(animatorListenerAdapter).playOn(child);
                        child.setVisibility(View.VISIBLE);
                    }
                }, 200);
            } else {
                startAnimator(child, mOutAnimation, new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        child.setVisibility(View.GONE);
                        removeInvisibleFragment();
                    }
                });
            }
        }
        if (hasFocus) {
            requestFocus(FOCUS_FORWARD);
        }
    }

    private void showOnlyForLike(int childIndex) {
        final int count = getChildCount();
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);
            if (i == childIndex) {
                child.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        startAnimator(child, mInAnimation, null);
                        child.setVisibility(View.VISIBLE);
                    }
                }, 200);
            } else {
                YoYo.with(mOutAnimation).duration(300).withListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        child.setVisibility(View.GONE);
                        removeInvisibleFragment();
                    }
                }).playOn(child);
            }
        }
    }

    private void showNext() {
        setDisplayedChild(mWhichChild + 1);
    }

    private void setWhichChild(int whichChild) {
        mWhichChild = whichChild;
        if (whichChild >= getChildCount()) {
            mWhichChild = 0;
        } else if (whichChild < 0) {
            mWhichChild = getChildCount() - 1;
        }
    }

    private void setDisplayedChild(int whichChild) {
        setWhichChild(whichChild);
        boolean hasFocus = getFocusedChild() != null;
        showOnly(mWhichChild);
        if (hasFocus) {
            requestFocus(FOCUS_FORWARD);
        }
    }

    private void showOnly(int childIndex) {
        final int count = getChildCount();
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);
            if (i == childIndex) {
                child.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        startAnimator(child, mInAnimation, animatorListenerAdapter);
                        child.setVisibility(View.VISIBLE);
                    }
                }, 200);
            } else {
                startAnimator(child, mOutAnimation, new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        child.setVisibility(View.GONE);
                        removeInvisibleFragment();
                    }
                });
            }
        }
    }

    private void startAnimator(View child, BaseViewAnimator animator, AnimatorListenerAdapter animatorListenerAdapter) {
        YoYo.with(animator).duration(300).interpolate(InterpolatorUtil.createDefaultOvershootInterpolator())
                .withListener(animatorListenerAdapter).playOn(child);
    }

    private void setNextAnimator() {
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", moveDistance, 0),
                        ObjectAnimator.ofFloat(target, "scaleX", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        };

        mOutAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", 0, -moveDistance),
                        ObjectAnimator.ofFloat(target, "scaleX", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0));
            }
        };
    }

    private void setAboveAnimator() {
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", -moveDistance, 0),
                        ObjectAnimator.ofFloat(target, "scaleX", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        };

        mOutAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", 0, moveDistance),
                        ObjectAnimator.ofFloat(target, "scaleX", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0));
            }
        };
    }

    private void setNextBlockForLikeAnimator() {
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", moveDistance, 0),
                        ObjectAnimator.ofFloat(target, "scaleX", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 0.9f, 1.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        };

        mOutAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0));
            }
        };
    }

    private void setBackForBlockAnimator() {
        mInAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        };

        mOutAnimation = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "translationY", 0, moveDistance),
                        ObjectAnimator.ofFloat(target, "scaleX", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1.0f, 0.9f),
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0));
            }
        };
    }


    /**
     * 做完动画后清除不可见fragment
     */
    public void removeInvisibleFragment() {
        if (currFragment instanceof MatchingUserInfoFragment) {
            FrameLayout nextView = getNextView();
            nextView.removeAllViews();
            if (aboveFragment != null) {
                fragmentTransaction = fragmentManager.beginTransaction();
                fragmentTransaction.remove(aboveFragment);
                fragmentTransaction.commitAllowingStateLoss();
            }
        }
    }
}
