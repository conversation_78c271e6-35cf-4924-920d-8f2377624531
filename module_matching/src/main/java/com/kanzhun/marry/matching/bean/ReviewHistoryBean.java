package com.kanzhun.marry.matching.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class ReviewHistoryBean implements Parcelable {
    public String userId;
    public String tag;

    public ReviewHistoryBean(String tag, String userId) {
        this.userId = userId;
        this.tag = tag;
    }

    public ReviewHistoryBean(Parcel in) {
        userId = in.readString();
        tag = in.readString();
    }

    public static final Creator<ReviewHistoryBean> CREATOR = new Creator<ReviewHistoryBean>() {
        @Override
        public ReviewHistoryBean createFromParcel(Parcel in) {
            return new ReviewHistoryBean(in);
        }

        @Override
        public ReviewHistoryBean[] newArray(int size) {
            return new ReviewHistoryBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(userId);
        dest.writeString(tag);
    }
}
