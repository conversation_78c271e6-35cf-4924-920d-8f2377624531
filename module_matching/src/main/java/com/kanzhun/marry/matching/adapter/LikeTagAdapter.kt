package com.kanzhun.marry.matching.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.views.flowlayout.FlowAdapter
import com.kanzhun.marry.matching.api.model.CertTag
import com.kanzhun.marry.matching.databinding.MatchingLikeMeTagItemBinding

/**
 * 喜欢我标签
 */
class LikeTagAdapter(val context: Context, val list: List<CertTag>) : FlowAdapter<CertTag>(list) {

    private val layoutInflater by lazy {
        LayoutInflater.from(context)
    }

    override fun getView(position: Int, parent: ViewGroup): View {
        val binding = MatchingLikeMeTagItemBinding.inflate(layoutInflater, parent, false)
        val item = getItem(position)
        binding.tvTagName.text = item.content
        binding.ivTagIcon.visible(item.hasIcon())
        return binding.root
    }

}