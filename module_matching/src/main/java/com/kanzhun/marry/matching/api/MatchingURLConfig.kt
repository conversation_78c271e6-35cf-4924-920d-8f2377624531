package com.kanzhun.marry.matching.api

import com.kanzhun.foundation.api.base.URLConfig

object MatchingURLConfig : URLConfig() {
    const val URL_USER_AVATAR_ADVICE: String = "orange/user/avatar/advice" // 查询用户头像建议记录
    const val URL_USER_ACTIVITY_ALBUM_ALERT: String = "orange/activity/album/alert" // 查询用户相册弹窗信息
    const val URL_USER_AVATAR_ADVICE_AGREE: String = "orange/user/avatar/advice/agree" // 同意头像建议
    const val URL_USER_AVATAR_ADVICE_REJECT: String = "orange/user/avatar/advice/reject" // 拒绝头像建议

    const val URL_MEETUP_PLAN_WAITING_DETAIL: String = "orange/meetup/plan/waiting/detail"
    const val URL_MEETUP_PLAN_TEST_MODULE: String = "orange/meetup/plan/testModule"
    const val URL_MEETUP_PLAN_SORTING_DETAIL: String = "orange/meetup/plan/sorting/detail"
    const val URL_MEETUP_PLAN_SORT_SUBMIT: String = "orange/meetup/plan/sort/submit"
    const val URL_MEETUP_PLAN_SORTED_DETAIL: String = "orange/meetup/plan/sorted/detail"
    const val URL_MEETUP_PLAN_CHAT_LIST: String = "orange/meetup/plan/chat/list"
    const val URL_MEETUP_PLAN_INVITE_LIST: String = "orange/meetup/plan/invite/list"
    const val URL_MEETUP_INVITE_SUBMIT: String = "orange/meetup/invite/submit"
    const val URL_MEETUP_INVITE_DIALOG: String = "orange/meetup/invite/dialog"
    const val URL_MEETUP_INVITE_ACCEPT: String = "orange/meetup/invite/accept"
    const val URL_MEETUP_INVITE_REFUSE: String = "orange/meetup/invite/refuse"
    const val URL_MEETUP_PLAN_MATCH_FINISH_CONTENT: String = "orange/meetup/plan/matchFinish/content"
}