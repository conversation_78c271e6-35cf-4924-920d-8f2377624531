package com.kanzhun.marry.matching.repository

import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.marry.matching.api.MatchApi
import com.kanzhun.marry.matching.model.ActivityLiveInfo
import com.kanzhun.marry.matching.model.AiMatchInfo

class MatchRepository {
    private val api: MatchApi =  RetrofitManager.getInstance().createApi(MatchApi::class.java)
    
    /**
     * 获取活动现场信息
     * @param activityId 活动ID
     * @param showType 查询类型 1 男生 2 女生
     */
    suspend fun getActivityLive(activityId: String, showType: Int): Result<ActivityLiveInfo> {
        return try {
            val response = api.getActivityLive(activityId, showType)
            handleResponse(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 查询个人活动 AI 匹配结果
     * @param activityId 加密活动ID
     */
    suspend fun getAiMatch(activityId: String): Result<AiMatchInfo> {
        return try {
            val response = api.getAiMatch(activityId)
            handleResponse(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 处理API响应，将BaseResponse转换为Result
     */
    private fun <T> handleResponse(response: BaseResponse<T>): Result<T> {
        return if (response.isSuccess && response.data != null) {
            Result.success(response.data)
        } else {
            Result.failure(Exception(response.msg ?: "Unknown error"))
        }
    }
} 