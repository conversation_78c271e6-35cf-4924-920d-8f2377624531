package com.kanzhun.marry.matching.fragment.home.performance

import android.animation.Animator
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.toast.OToast
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.databinding.MatchingHomeTodayUpdateDialogBinding
import com.kanzhun.marry.matching.fragment.home.MatchHomePagerFragment
import com.kanzhun.marry.matching.viewmodel.UpdateTipsType
import com.petterp.floatingx.imp.FxAppLifecycleProvider

/**
 * 匹配首页数据更新相关逻辑
 */
class MatchingHomeUpdateDataPerformance(val fragment: MatchHomePagerFragment) : AbsPerformance() {

    val dialog: UpdateDialogWrapper by lazy {
        UpdateDialogWrapper(fragment.mBinding.updateDialog) {
            fragment.mViewModel.getRecommendData()
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        //取消不让任何人看到我，需要刷新数据
        ServiceManager.getInstance().settingService.matchVisibleLiveData.observe(owner) { isNoMatchMode: Boolean ->
            //打开拦截
            fragment.mViewModel.isNoMatchMode = isNoMatchMode
            if (!isNoMatchMode) {
                fragment.mViewModel.getRecommendData()
            }
        }
        //更新提示弹框
        fragment.mViewModel.showUpdateDialog.observe(fragment) { show ->
            if (show) {
                showUpdateDialog()
            }
        }
        //更新提示
        fragment.mViewModel.showUpdateTips.observe(fragment) {
            try {
                if(FxAppLifecycleProvider.getTopActivity() != null && FxAppLifecycleProvider.getTopActivity()!!::class.java.name.contains("MainActivity")){
                    when (it.type) {
                        UpdateTipsType.TODAY_UPDATE -> { //今日推荐已更新
                            OToast.show(fragment.requireActivity(), R.string.matching_recommend_updated_toast.toResourceString(), R.drawable.matching_icon_recommend_user_updated)
                        }

                        UpdateTipsType.TOMORROW_UPDATE -> { //明日预告
                            OToast.show(fragment.requireActivity(), R.string.matching_recommend_updated_toast_1.toResourceString(it.info?.previewUser?.updateTime ?: "8：00"))
                        }
                    }
                }
            }catch (e:Exception){

            }


        }
        //已经更新，停止刷新
        fragment.mViewModel.updateResult.observe(fragment) { result ->
            if (result) {
                fragment.mBinding.smartRefreshLayout.finishRefresh()
            }
        }
        //下拉刷新
        fragment.mBinding.run {
            smartRefreshLayout.setOnRefreshListener {
                if (fragment.mViewModel.isLoadingNow()) {
                    smartRefreshLayout.finishRefresh()
                    return@setOnRefreshListener
                }
                fragment.mViewModel.getRecommendData()
            }
        }
        fragment.mViewModel.isLoading.observe(fragment) {
            if (it) {
                dialog.dismiss()
            }
        }
    }


    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        //已经加载过数据了，那么每次可见都检查更新
        if (fragment.mViewModel.hasLoadData()) {
            //如果有阻断，每次偷偷摸摸给他更新到最新数据，保证阻断卡片正常
            if (fragment.mViewModel.hasGuideBlock()) {
                fragment.mViewModel.getRecommendData(silentUpdate = true)
            } else {
                checkForUpdate()
            }

        }
    }

    private fun checkForUpdate() {
        fragment.mViewModel.isUpdate()
    }


    private fun showUpdateDialog() {
        dialog.show()
    }


}

class UpdateDialogWrapper(val binding: MatchingHomeTodayUpdateDialogBinding, val callback: () -> Unit = {}) {
    private var isShowing: Boolean = false

    private var isDismissing: Boolean = false

    private var translateY = 0F

    init {
        val array = intArrayOf(0, 0)
        binding.root.getLocationOnScreen(array)
        binding.btRefreshNow.clickWithTrigger {
            callback()
        }
        translateY = (-100).dp
        binding.root.translationY = translateY
    }

    fun dismiss() {
        if (!isShowing) {
            return
        }
        if (isDismissing) {
            return
        }
        isDismissing = true
        binding.root.animate().setDuration(500).setListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
                binding.root.isClickable = false
            }

            override fun onAnimationEnd(p0: Animator) {
                isShowing = false
                binding.root.gone()
                isDismissing = false
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }
        }).translationY(translateY).start()
    }

    fun show() {
        if (isShowing) {
            return
        }
        isShowing = true
        binding.root.animate().setDuration(500).setListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
                binding.root.isClickable = false
                binding.root.visible()
            }

            override fun onAnimationEnd(p0: Animator) {
                binding.root.postDelayed({
                    dismiss()
                }, 5000)
                binding.root.isClickable = true

            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }
        }).translationY(0F).start()
    }

}