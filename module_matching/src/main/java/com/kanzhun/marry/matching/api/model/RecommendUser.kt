package com.kanzhun.marry.matching.api.model

import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.model.SameActivityBean
import com.kanzhun.foundation.bean.RecommendInfoBean
import com.kanzhun.foundation.bean.RecommendInfoResponse
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.marry.matching.adpter.home.MatchingHomeViewHolderFactory
import java.io.Serializable


data class RecommendUser(
    val lid: String? = null,
    val notMatch: Int = 1,                          // 不让任何人看到我 1 是 0 否
    var relationShowStatus: Int = 0,                //是否已经喜欢，0-未喜欢，1-已发送喜欢
    val baseInfo: BaseUserInfo? = null,             //基本信息
    val privateInfo: PrivateInfo? = null,           //隐私信息
    val certInfo: HomeCartInfo? = null,             //认证信息
    val tagInfo: UserTagInfo? = null,               //顶部标签信息
    val lifePhotoList: MutableList<LifePhoto>? = null,    //生活照
    var cardType:Int = 1,//1-正常卡片，2-阻断卡片，3-活动兑换卡片

    var introImgList: MutableList<LifePhoto>? = null,
    var familyImgList: MutableList<LifePhoto>? = null,
    var interestImgList: MutableList<LifePhoto>? = null,



    val blockDialogInfo: BlockDialogInfo? = null,    //阻断弹框必要信息
    val blockFields: BlockFields? = null,
    val securityId: String? = null,// 安全id，用于透传请求相关接口
    val signInNum:String? = null,//签到编号
    val sameActivity:SameActivityBean? = null,//同一场活动
    val recommendTag:List<String>? = null,
    var idealTag:List<String>? = null,
    val cacheTs:Long? = null,//缓存时间戳
    val recommendInfo:RecommendInfoBean? = null
) : IPageBean {
    override var topString: String = ""
    var showAnimation: Boolean = false

    var blockInfo: UserGuideBlockInfoBean? = null //阻断信息

    fun getBlock():Boolean{
        return cardType == 2 || cardType == 3
    }

    override fun getItemType():Int {
        return when(cardType){
            1 ->{
                MatchingHomeViewHolderFactory.TYPE_RECOMMEND_USER
            }
            2 ->{
                MatchingHomeViewHolderFactory.TYPE_USER_GUIDE_BLOCK
            }
            3 ->{
                MatchingHomeViewHolderFactory.TYPE_CHRISTMAS
            }
            else -> {
                MatchingHomeViewHolderFactory.TYPE_RECOMMEND_USER
            }
        }
    }


    fun notLikeYet() = relationShowStatus == Constants.RELATION_NOT_LIKE
    fun hasAlreadyLike() = relationShowStatus == Constants.RELATION_LIKE

    fun isFriend() = relationShowStatus == Constants.RELATION_FRIEND

    fun canMatchByOther() = notMatch == 0

    fun hasTheSameTag(): Boolean {
        val count = tagInfo?.sameCount ?: 0
        return count > 0
    }

    fun hasLifePhoto(): Boolean = !lifePhotoList.isNullOrEmpty()

    fun setBlockInfo2CertTag() {
        certInfo?.certTagList?.forEach {
            when (it.certType) {
                3 -> it.isBlur = blockFields?.eduCertBlock ?: false
                4 -> it.isBlur = blockFields?.companyBlock ?: false
                7 -> it.isBlur = blockFields?.annualIncomeBlock ?: false
            }
        }
    }
}

data class BaseUserInfo(
    val age: Int,
    val avatar: String?,
    val career: String?,
    val careerCode: String?,
    val encUserId: String?,
    val gender: Int,// 性别 1男 2女
    val height: Int,
    val addressDesc: String?,
    val industry: String?,
    val industryCode: String?,
    val intro: String?,
    var nickName: String?,
    val tinyAvatar: String?,
    val degreeDesc: String?,
    val recommendTag: List<String>?,
    val tagInfo: UserTagBean?,
    val relationStatus: Int? = 0,
    val lastActiveTimeDesc: String? = "",
    val avatarStyle:Int? = 0
) : Serializable

data class PrivateInfo(
    val annualIncome: String? = ""
) : Serializable

data class HomeCartInfo(
    val certPassCount: Int = 0,
    val certTagList: List<CertTag>? = null
) : Serializable

//胶片
data class FilmBean(//活动卡片
    val picUrl:String? = "",
    val jumpUrl:String? = "",
    val borderColor:String? = "",

    val title:String? = "",
    val content:String? = "",
    val btnText:String? = "",// 按钮文案
    val type:Int = 0,// 卡片类型 1-胶片活动
    val picList:List<String>? = null,// 图片列表

):Serializable,IPageBean {

    override var topString: String = ""
    override fun getItemType() = MatchingHomeViewHolderFactory.TYPE_USER_FILM

    override fun enableFresh(): Boolean {
        return false
    }

    override fun showTitle(): Boolean {
        return false
    }
}

//新手引导卡片
data class CardNewType(val type:Int = 0,
    var name:String = ""
):Serializable,IPageBean {
    override var topString: String = ""

    override fun getItemType() = MatchingHomeViewHolderFactory.TYPE_NEW_USER

    override fun showTitle(): Boolean {
        return false
    }
}


data class CertTag(
    val showIcon: Int = 0,
    val content: String? = "",
    val certType: Int = 0//1-人脸，2-头像，3-学历，4-工作，5-房产，6-车产，7-收入，
) : Serializable {
    fun hasIcon() = showIcon == 1

    var isBlur = false
}


data class UserTagInfo(val sameCount: Int = 0, var tagList: List<UserTagBean>? = null) :
    Serializable

data class UserTagBean(
    val tagName: String? = "",
    val tagIcon: String? = "",
    val isSame: Int = 2 //是否是共同的标签, 1-共同拥有，2-不共同拥有
) : Serializable {
    fun isSameTag() = isSame == 1
}


data class LifePhoto(
    val photo: String? = null,
    val tinyPhoto: String? = null,
    val text: String? = null,
    val photoStyle:Int? = 0,//客户端图片处理动作 1原样展示 2模糊处理
) : Serializable

data class BlockDialogInfo(val address: String?, val recommendReasonList: List<String>?) :
    Serializable

/**
 * {
 *    "industryBlock":true,
 *    "annualIncomeBlock":true, //收入是否打码
 *    "eduCertBlock":true, //学历是否打码
 *    "companyBlock":true, //公司是否打码
 *                 }
 */
data class BlockFields(
    val industryBlock: Boolean = false,    //行业是否打码
    val annualIncomeBlock: Boolean = false,//收入是否打码
    val eduCertBlock: Boolean = false,     //学历是否打码
    val companyBlock: Boolean = false      //公司是否打码
) : Serializable