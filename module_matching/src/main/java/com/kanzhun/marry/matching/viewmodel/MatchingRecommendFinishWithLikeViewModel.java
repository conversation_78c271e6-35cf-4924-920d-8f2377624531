package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;

import com.kanzhun.foundation.base.FoundationViewModel;

public class MatchingRecommendFinishWithLikeViewModel extends FoundationViewModel {

    private ObservableField<String> sloganObservable = new ObservableField();
    private ObservableInt likeCount = new ObservableInt();

    public MatchingRecommendFinishWithLikeViewModel(@NonNull Application application) {
        super(application);
    }

    public ObservableField<String> getSloganObservable() {
        return sloganObservable;
    }

    public void setSloganObservable(ObservableField<String> sloganObservable) {
        this.sloganObservable = sloganObservable;
    }

    public ObservableInt getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(ObservableInt likeCount) {
        this.likeCount = likeCount;
    }
}