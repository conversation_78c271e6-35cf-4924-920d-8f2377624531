package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import com.kanzhun.foundation.base.FoundationViewModel;

public class MatchingRecommendFinishWithoutLikeViewModel extends FoundationViewModel {

    private ObservableField<String> sloganObservable = new ObservableField();

    public MatchingRecommendFinishWithoutLikeViewModel(@NonNull Application application) {
        super(application);
    }

    public ObservableField<String> getSloganObservable() {
        return sloganObservable;
    }

    public void setSloganObservable(ObservableField<String> sloganObservable) {
        this.sloganObservable = sloganObservable;
    }

}