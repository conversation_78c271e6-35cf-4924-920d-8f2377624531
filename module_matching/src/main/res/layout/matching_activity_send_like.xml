<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MatchSendLikeActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchSendLikeViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.SendLikeCallback" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/activityMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:orientation="vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ScrollView
                android:id="@+id/sl_like_preview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingBottom="3dp"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">


                    <FrameLayout
                        android:id="@+id/cl_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="45dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="26dp"
                            android:layout_height="26dp"
                            android:layout_marginLeft="18dp"
                            android:src="@mipmap/social_icon_send_like_edit" />


                        <EditText
                            android:id="@+id/et_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="18dp"
                            android:background="@null"
                            android:hint="@string/matching_send_like_edit_hint"
                            android:minHeight="26dp"
                            android:paddingLeft="4dp"
                            android:textColor="@color/matching_color_FF000000"
                            android:textColorHint="@color/common_color_CCCCCC"
                            android:textSize="@dimen/common_text_sp_18"
                            android:textStyle="bold">

                        </EditText>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="18dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginRight="18dp"
                        android:background="@color/common_color_191919" />
                </LinearLayout>

            </ScrollView>

            <LinearLayout
                android:id="@+id/ll_send_like_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingTop="32dp">

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:layout_marginLeft="18dp"
                    android:layout_marginBottom="18dp"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:onClick="@{()->callback.clickCancel()}"
                    android:text="@string/matching_wait_more"
                    android:textColor="@color/common_color_191919"
                    android:textSize="16sp"
                    app:qmui_borderColor="@color/common_color_191919"
                    app:qmui_borderWidth="1dp"
                    app:qmui_radius="25dp" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btn_send_like"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/common_blue_button_style"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="18dp"
                    android:layout_weight="7"
                    android:onClick="@{()->callback.clickSendLike()}"
                    android:text="@string/matching_send_like" />


            </LinearLayout>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/ll_love_has_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="114dp"
                android:alpha="0"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="47dp"
                    android:layout_height="47dp"
                    android:src="@drawable/common_ic_icon_match_like">

                </ImageView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/matching_love_has_send"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_14">

                </TextView>


            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>