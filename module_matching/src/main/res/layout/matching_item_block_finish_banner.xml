<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:paddingLeft="24dp"
        android:paddingRight="24dp">
        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/iv_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:scaleType="fitXY"
            app:common_error="@color/common_translate"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_title"
            app:layout_constraintTop_toTopOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            app:layout_constraintHorizontal_chainStyle="packed"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toRightOf="@+id/iv_icon"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="3dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="@dimen/common_text_sp_24"
            android:textColor="@color/common_color_333333"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            tools:text="title" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:gravity="center_horizontal"
            android:maxLines="2"
            android:ellipsize="end"
            android:textSize="@dimen/common_text_sp_18"
            android:lineHeight="25dp"
            android:textColor="@color/common_color_707070"
            tools:text="subtitle" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
