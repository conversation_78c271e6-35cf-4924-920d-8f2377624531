<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/divider1_1"
        android:layout_width="1dp"
        android:layout_height="8dp"
        android:layout_marginEnd="6dp"
        android:background="@color/common_white"
        app:layout_constraintBottom_toBottomOf="@id/tvTagName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTagName" />

    <TextView
        android:id="@+id/tvTagName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="5dp"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/divider1_1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginStart="0dp"
        tools:text="195cm"
        tools:textColor="@color/common_color_292929" />

</androidx.constraintlayout.widget.ConstraintLayout>