<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MatchingTemporaryChatActivity">

    <data>
        <import type="android.text.TextUtils" />
        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchingTemporaryChatViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchingTemporaryChatCallback" />
    </data>

    <RelativeLayout
        android:onClick="@{()->callback.hideKeyboard()}"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/matching_bg_temporary"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/ll_mode_icon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/iv_mode1"
                android:layout_width="54dp"
                android:layout_height="54dp"
                android:layout_marginTop="56dp"
                android:alpha="0.4"
                app:imageUrl="@{viewModel.modeIcon}"
                android:rotation="-20"
                app:common_placeholder="@drawable/common_bg_placeholder_translate" />

            <com.kanzhun.common.views.image.OImageView
                app:common_placeholder="@drawable/common_bg_placeholder_translate"
                android:id="@+id/iv_mode2"
                android:alpha="0.1"
                android:rotation="30"
                app:imageUrl="@{viewModel.modeIcon}"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="65dp"
                android:layout_width="36dp"
                android:layout_height="36dp"/>

            <com.kanzhun.common.views.image.OImageView
                app:common_placeholder="@drawable/common_bg_placeholder_translate"
                android:id="@+id/iv_mode3"
                android:layout_alignParentRight="true"
                app:imageUrl="@{viewModel.modeIcon}"
                android:alpha="0.6"
                android:rotation="-10"
                android:layout_marginTop="56dp"
                android:layout_width="54dp"
                android:layout_height="54dp"/>

            <com.kanzhun.common.views.image.OImageView
                app:common_placeholder="@drawable/common_bg_placeholder_translate"
                android:id="@+id/iv_mode4"
                app:imageUrl="@{viewModel.modeIcon}"
                android:alpha="0.2"
                android:layout_marginTop="161dp"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <com.kanzhun.common.views.image.OImageView
                app:common_placeholder="@drawable/common_bg_placeholder_translate"
                android:id="@+id/iv_mode5"
                app:imageUrl="@{viewModel.modeIcon}"
                android:alpha="0.2"
                android:layout_marginTop="156dp"
                android:layout_alignParentRight="true"
                android:layout_width="44dp"
                android:layout_height="44dp"/>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_temporary_chat"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:layout_marginTop="80dp"
                android:layout_gravity="center_horizontal"
                android:id="@+id/tv_count_down"
                android:textSize="68sp"
                android:textColor="@color/common_black"
                android:layout_width="310dp"
                android:text="00:00.00"
                android:maxLines="1"
                android:layout_height="wrap_content"
                />

            <FrameLayout
                android:layout_marginTop="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <FrameLayout
                    android:id="@+id/ll_friend_input"
                    android:layout_marginTop="9dp"
                    android:background="@drawable/common_bg_corner_20_color_white_50"
                    android:minHeight="150dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:enabled="false"
                        android:lineSpacingExtra="8dp"
                        android:textStyle="bold"
                        android:textSize="22dp"
                        android:layout_marginStart="27dp"
                        android:layout_marginEnd="27dp"
                        android:layout_marginTop="51dp"
                        android:layout_marginBottom="27dp"
                        android:background="@null"
                        android:layout_gravity="center_horizontal"
                        android:id="@+id/edit_friend"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLength="24"
                        android:textColor="@color/common_black"
                        />
                    <ImageView
                        android:id="@+id/iv_friend_link_status"
                        android:layout_marginTop="11dp"
                        android:layout_marginEnd="8dp"
                        android:layout_gravity="end"
                        android:src="@drawable/matching_ic_temporary_chat_offline"
                        android:layout_width="12dp"
                        android:layout_height="12dp"/>
                </FrameLayout>
                <FrameLayout
                    visibleGone="@{!TextUtils.isEmpty(viewModel.friendAvatar)}"
                    android:id="@+id/ll_friend_avatar"
                    android:background="@drawable/common_bg_corner_25_color_cccccc"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="48dp"
                    android:layout_height="48dp">
                    <com.kanzhun.common.views.image.OAvatarImageView
                        android:src="@mipmap/ic_launcher_round"
                        app:imageUrl="@{viewModel.friendAvatar}"
                        android:layout_gravity="center"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        />
                </FrameLayout>
            </FrameLayout>

            <FrameLayout
                android:layout_marginTop="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <FrameLayout
                    android:id="@+id/ll_edit_input"
                    android:layout_marginTop="9dp"
                    android:background="@drawable/common_bg_corner_20_color_white_50"
                    android:minHeight="150dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText
                        android:cursorVisible="true"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:lineSpacingExtra="8dp"
                        android:textStyle="bold"
                        android:textSize="22dp"
                        android:layout_marginStart="27dp"
                        android:layout_marginEnd="27dp"
                        android:layout_marginTop="51dp"
                        android:layout_marginBottom="27dp"
                        android:background="@color/common_translate"
                        android:layout_gravity="center_horizontal|top"
                        android:id="@+id/edit_input"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="22dp"
                        android:maxLength="24"
                        android:hint="@string/matching_temorary_hint"
                        android:inputType="textMultiLine"
                        android:imeOptions="actionSend"
                        android:onEditorAction="@{callback}"
                        android:textColor="@color/common_black" />

                    <ImageView
                        android:id="@+id/iv_self_link_status"
                        android:layout_marginTop="11dp"
                        android:layout_marginEnd="8dp"
                        android:layout_gravity="end"
                        android:src="@drawable/matching_ic_temporary_chat_offline"
                        android:layout_width="12dp"
                        android:layout_height="12dp"/>

                    <ImageView
                        android:onClick="@{()->callback.clearInput()}"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="8dp"
                        android:src="@mipmap/matching_ic_temporary_clear_input"
                        android:layout_width="32dp"
                        android:layout_height="32dp"/>
                </FrameLayout>
                <FrameLayout
                    visibleGone="@{!TextUtils.isEmpty(viewModel.selfAvatar)}"
                    android:id="@+id/ll_self_avatar"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="48dp"
                    android:layout_height="48dp">
                    <com.kanzhun.common.views.image.OAvatarImageView
                        android:src="@mipmap/ic_launcher_round"
                        app:imageUrl="@{viewModel.selfAvatar}"
                        android:layout_gravity="center"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        />
                </FrameLayout>
            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:paddingBottom="52dp"
            android:gravity="center_vertical"
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>
            <TextView
                android:onClick="@{()->callback.quit()}"
                android:drawableTop="@mipmap/matching_ic_temporary_quit"
                android:textSize="16dp"
                android:gravity="center"
                android:drawablePadding="19dp"
                android:text="@string/common_exist"
                android:textColor="@color/common_color_000000_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>
            <TextView
                android:id="@+id/tv_show_avatar"
                android:onClick="@{()->callback.showAvatar()}"
                android:drawableTop="@mipmap/matching_ic_temporary_agree"
                android:textSize="16dp"
                android:gravity="center"
                android:drawablePadding="19dp"
                android:text="@string/matching_publish_avatar"
                android:textColor="@color/common_color_000000_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>
            <TextView
                android:onClick="@{()->callback.report()}"
                android:drawableTop="@mipmap/matching_ic_temporary_report"
                android:textSize="16dp"
                android:gravity="center"
                android:drawablePadding="19dp"
                android:text="@string/common_report"
                android:textColor="@color/common_color_000000_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>
        </LinearLayout>
    </RelativeLayout>

</layout>