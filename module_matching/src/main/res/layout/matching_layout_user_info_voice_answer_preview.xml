<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.SendLikeCallback" />
    </data>

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:transitionName="content_preview"
        android:layout_marginLeft="37dp"
        android:layout_marginRight="37dp"
        android:layout_marginTop="72dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:qmui_radius="12dp"
        android:background="@color/common_color_EBEBEB">

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="22dp"
            android:layout_marginEnd="22dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textSize="@dimen/common_text_sp_18"
            android:textColor="@color/common_color_191919"
            tools:text="最难忘的一次相识是" />

        <ImageView
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/me_ic_icon_info_preview_voice_start"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            android:layout_marginTop="16dp"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="22dp"
            android:layout_marginBottom="30dp" />

        <com.kanzhun.common.views.AudioRecorderPlayView
            android:id="@+id/v_voice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="@+id/iv_icon"
            app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="22dp"
            android:layout_marginEnd="22dp" />


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
</layout>