<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchingRecommendFinishWithLikeViewModel" />
        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchingRecommendFinishCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:layout_margin="12dp"
            app:qmui_shadowAlpha="0.4"
            app:qmui_shadowElevation="29dp"
            app:qmui_radius="12dp">
            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <com.kanzhun.common.views.OTextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="20dp"
                        android:drawableLeft="@mipmap/matching_ic_like_title"
                        android:drawablePadding="8dp"
                        android:textStyle="bold"
                        android:textSize="@dimen/common_text_sp_24"
                        android:text="@string/matching_recommend_finish_title_like"
                        android:textColor="@color/common_color_333333"/>

                    <TextView
                        android:id="@+id/tv_sub_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_title"
                        android:layout_marginTop="6dp"
                        android:textSize="@dimen/common_text_sp_14"
                        android:text="@string/matching_recommend_finish_subtitle_like"
                        android:textColor="@color/common_color_B7B7B7"/>

                    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                        android:id="@+id/cl_like_parent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@+id/tv_sub_title"
                        android:layout_marginTop="12dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:paddingBottom="12dp"
                        android:background="@drawable/matching_bg_color_00000000_to_fff5f8ff"
                        app:qmui_radius="10dp">
                        <com.kanzhun.marry.matching.views.MatchingLikeAvatarView
                            android:id="@+id/view_like_avatar"
                            android:layout_width="178dp"
                            android:layout_height="178dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_marginTop="12dp"/>

                        <TextView
                            android:id="@+id/tv_like_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/view_like_avatar"
                            android:layout_marginTop="12dp"
                            android:paddingLeft="5dp"
                            android:paddingRight="5dp"
                            android:drawableRight="@mipmap/matching_ic_like_arrow"
                            android:drawablePadding="2dp"
                            android:onClick="@{v->callback.clickLikeMe()}"
                            android:textSize="@dimen/common_text_sp_16"
                            android:text="@{@string/matching_recommend_finish_title_like_count(viewModel.likeCount)}"
                            tools:text="@string/matching_recommend_finish_title_like_count"
                            android:textColor="@color/common_color_7171F6"/>
                    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

                    <View
                        android:id="@+id/view_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        app:layout_constraintTop_toBottomOf="@+id/cl_like_parent"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:background="@color/common_color_F4F4F6"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@+id/view_divider"
                        android:layout_marginTop="28dp">
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_guide"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="parent"
                            android:visibility="gone"
                            tools:visibility="visible">
                            <com.youth.banner.Banner
                                android:id="@+id/banner"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:banner_auto_loop="true"
                                app:banner_infinite_loop="true"
                                app:banner_loop_time="5000"
                                app:layout_constraintBottom_toTopOf="@+id/btn_action"
                                android:layout_marginBottom="28dp"/>

                            <net.lucode.hackware.magicindicator.MagicIndicator
                                android:id="@+id/mi"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginBottom="20dp"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintBottom_toTopOf="@+id/btn_action" />

                            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                                android:id="@+id/btn_action"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:layout_constraintBottom_toTopOf="@+id/tv_slide_title"
                                android:layout_marginLeft="20dp"
                                android:layout_marginRight="20dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:paddingTop="12dp"
                                android:paddingBottom="12dp"
                                android:paddingLeft="50dp"
                                android:paddingRight="50dp"
                                android:textColor="@color/common_color_7171F6"
                                android:textSize="@dimen/common_text_sp_16"
                                android:textStyle="bold"
                                android:maxLines="1"
                                android:ellipsize="end"
                                app:qmui_backgroundColor="@color/common_color_E9EEFF"
                                app:qmui_radius="25dp"
                                tools:text="@string/chat_relationship_btn_i_see" />

                            <TextView
                                android:id="@+id/tv_slide_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                android:layout_marginBottom="20dp"
                                android:text="@string/matching_recommend_finish_slide_title"
                                android:textSize="@dimen/common_text_sp_18"
                                android:textColor="@color/common_color_000000_40" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                            android:id="@+id/cl_empty_guide"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="parent"
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="20dp"
                            android:background="@color/matching_color_FFF5F8FF"
                            app:qmui_radius="10dp"
                            android:visibility="gone"
                            tools:visibility="gone">
                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="140dp"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintDimensionRatio="w,167:140"
                                android:src="@mipmap/matching_bg_like_empty"/>

                            <com.kanzhun.common.views.OTextView
                                android:id="@+id/tv_slogan_empty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                android:layout_marginTop="32dp"
                                android:layout_marginLeft="20dp"
                                android:layout_marginRight="20dp"
                                android:gravity="center"
                                android:textSize="@dimen/common_text_sp_22"
                                android:lineHeight="30dp"
                                android:text="@{viewModel.sloganObservable}"
                                android:textColor="@color/common_color_000000_70"
                                tools:text="@string/matching_recommend_finish_slogan" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/tv_slogan_empty"
                                android:layout_marginTop="24dp"
                                android:paddingBottom="34dp"
                                android:text="@string/matching_recommend_finish_slide_title"
                                android:textSize="@dimen/common_text_sp_18"
                                android:textColor="@color/common_color_000000_40" />
                        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>