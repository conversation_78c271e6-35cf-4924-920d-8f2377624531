<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="url"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/iv_pic"
            android:layout_width="162dp"
            android:layout_height="215dp"
            app:common_radius="12dp"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:imageUrl="@{url}"/>


        <FrameLayout
            android:layout_width="162dp"
            android:layout_height="215dp"
            android:background="@drawable/common_fg_corner_12_color_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <FrameLayout
            android:layout_width="162dp"
            android:layout_height="215dp"
            android:background="@drawable/common_fg_corner_0_color_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>