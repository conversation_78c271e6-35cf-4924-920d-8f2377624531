<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:layout_marginHorizontal="4dp"
    android:id="@+id/idCard"
    app:cardBackgroundColor="@color/common_white"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatar"
            android:layout_width="78dp"
            android:layout_height="78dp"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/common_color_FF7847" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:ellipsize="end"
            android:layout_marginTop="7dp"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textColor="@color/common_black"
            android:textSize="16dp"
            app:layout_constraintTop_toTopOf="@+id/ivAvatar"
            app:layout_constraintLeft_toRightOf="@+id/ivAvatar"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="@string/common_long_placeholder" />


        <TextView
            android:id="@+id/idTextTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="2dp"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="8dp"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁" />

        <TextView
            android:maxLines="1"
            android:ellipsize="end"
            android:id="@+id/idTextLocal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="2dp"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintTop_toBottomOf="@+id/idTextTime"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="4dp"
            tools:text="30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁30岁" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>