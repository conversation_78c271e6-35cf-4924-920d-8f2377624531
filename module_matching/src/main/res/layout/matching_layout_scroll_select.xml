<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.kanzhun.marry.matching.bean.ScrollSelectBean" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="112dp">


        <TextView
            android:id="@+id/tv_select_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="20dp"
            android:enabled="@{bean.enable}"
            android:text="@{bean.title}"
            android:textColor="@color/common_selector_text_color_b2b2b2_enable_color_cccccc"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="年龄" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="35dp"
            app:imageSrc="@{bean.leftIc}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@+id/tv_select_title" />

        <TextView
            android:id="@+id/tv_select_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginRight="12dp"
            android:enabled="@{bean.enable}"
            android:text="@{bean.content}"
            android:textColor="@color/common_selector_text_color_191919_enable_color_cccccc"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="20-50" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="35dp"
            app:imageSrc="@{bean.rightIc}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="@+id/tv_select_content" />

        <com.kanzhun.common.views.RangeBar
            android:id="@+id/rb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            app:common_inside_range_line_stroke_width="2dp"
            app:common_is_show_bubble="true"
            app:common_lable_text_color="@color/common_white"
            app:common_lable_text_size="@dimen/common_text_sp_14"
            app:common_outside_range_line_stroke_width="2dp"
            app:common_slider_icon_size="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:rangeBarEnable="@{bean.enable}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>