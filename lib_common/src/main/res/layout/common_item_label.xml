<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingRight="10dp">

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        app:qmui_borderColor="@color/common_color_F5F5F5"
        app:qmui_borderWidth="2dp"
        app:qmui_radius="19dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/idImageView"
            android:layout_gravity="center_vertical"
            tools:src="@drawable/common_tip_bar_warn"
            android:layout_marginRight="3dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idLabel"
            app:layout_constraintBottom_toBottomOf="@+id/idLabel"
            android:layout_width="20dp"
            android:layout_height="20dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="20dp"
            android:visibility="visible"
            android:gravity="center_vertical"
            android:textColor="@color/common_color_5E5E5E"
            android:textSize="14dp"
            tools:text="123213" />


    </com.qmuiteam.qmui.layout.QMUILinearLayout>

</com.qmuiteam.qmui.layout.QMUIConstraintLayout>