<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_corner_20_top_2_color_white"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:textColor="@color/common_color_0D0D1A"
        android:textSize="20dp"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="10dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        tools:itemCount="2"
        tools:listitem="@layout/common_item_bottom_select_dialog" />


    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_margin="20dp"
        android:background="@drawable/common_bg_corner_7_color_f2f2f5"
        android:gravity="center"
        android:text="@string/common_cancel"
        android:textColor="@color/common_color_0D0D1A"
        android:textSize="17dp" />

</LinearLayout>
