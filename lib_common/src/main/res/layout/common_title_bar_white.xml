<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <variable
            name="showIvRight"
            type="boolean" />

        <variable
            name="hideBackIv"
            type="boolean" />

        <variable
            name="hasDivider"
            type="boolean" />

        <variable
            name="title"
            type="String" />

        <variable
            name="centerDrawable"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="right"
            type="String" />

        <variable
            name="rightEnabled"
            type="Boolean" />

        <variable
            name="callback"
            type="com.kanzhun.common.callback.TitleBarCallback" />

        <variable
            name="hasBackground"
            type="Boolean" />

        <variable
            name="clickTitle"
            type="android.view.View.OnClickListener" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_title_height"
        android:background="@{hasBackground ? @android:color/black : @android:color/transparent}"
        tools:background="@color/common_black">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:paddingLeft="20dp"
            android:paddingRight="5dp"
            android:src="@drawable/common_ic_white_back"
            android:visibility="@{hideBackIv ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!-- 为保证title居中，右侧隐藏一对和左侧一样的布局 -->
        <ImageView
            android:id="@+id/iv_back_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingLeft="20dp"
            android:paddingRight="5dp"
            android:src="@drawable/common_ic_white_back"
            android:visibility="@{hideBackIv ? View.GONE : View.INVISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:onClick="@{clickTitle}"
            android:orientation="horizontal"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/barrier"
            app:layout_constraintStart_toEndOf="@+id/barrier_left"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:drawableRight="@{centerDrawable}"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:text='@{title,default = "" }'
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                tools:text="标题" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:enabled='@{rightEnabled ?? true}'
            android:gravity="center|right"
            android:onClick="@{(view)->callback.clickRight(view)}"
            android:paddingRight="20dp"
            android:text="@{right}"
            android:textColor="@color/common_selector_text_color_191919_enable_color_cccccc"
            android:textSize="17dp"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="右侧文字"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{!TextUtils.isEmpty(right)}" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:enabled='@{rightEnabled ?? true}'
            android:onClick="@{(view)->callback.clickRight(view)}"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:src="@drawable/common_ic_icon_chat_setting"
            android:visibility="@{showIvRight ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="iv_back"
            app:layout_constrainedWidth="true" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="iv_back_right"
            app:layout_constrainedWidth="true" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_divider_height"
            android:background="#cfcfcf"
            android:visibility="@{hasDivider ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
