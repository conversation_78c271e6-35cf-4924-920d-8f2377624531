package com.kanzhun.common.recorder;

import android.content.Context;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.MediaPlayer.OnErrorListener;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.utils.T;

import java.io.IOException;

/**
 * Created by guo<PERSON>
 * on 2019/3/23.
 * 声音播放
 */

public class MediaPlayerManager {

    private static MediaPlayerManager instance = new MediaPlayerManager();
    private AudioManager audioManager = (AudioManager) BaseApplication.getApplication().getSystemService(Context.AUDIO_SERVICE);

    public static MediaPlayerManager getInstance() {
        return instance;
    }

    private MediaPlayer mediaPlayer;

    public void play(String path, PlayingListener playingListener) {

        if (mediaPlayer != null) {
            mediaPlayer.pause();
            mediaPlayer.stop();
            mediaPlayer.release();
        }

        mediaPlayer = new MediaPlayer();
        mediaPlayer.setAudioStreamType(AudioManager.STREAM_VOICE_CALL);
        mediaPlayer.setLooping(false);
        //设置成听筒模式
//        closeSpeaker();
        openSpeaker();
        try {
            mediaPlayer.setDataSource(path);
            mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mp) {
                    mediaPlayer.start();
                    if (playingListener != null) {
                        playingListener.playCallback();
                    }
                }
            });
            mediaPlayer.setOnErrorListener(onErrorListener);
            mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    openSpeaker();
                    if (playingListener != null) {
                        playingListener.onComplete();
                    }
                }
            });
            mediaPlayer.prepareAsync();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void pause() {
        if (mediaPlayer != null) {
            mediaPlayer.pause();
        }
    }


    public void reStart() {
        if (mediaPlayer != null) {
            mediaPlayer.start();
        }
    }

    public void rePlay(int time, String path, PlayingListener playingListener) {

        if (mediaPlayer != null) {
            mediaPlayer.pause();
            mediaPlayer.stop();
            mediaPlayer.release();
        }

        mediaPlayer = new MediaPlayer();
        mediaPlayer.setAudioStreamType(AudioManager.STREAM_VOICE_CALL);
        mediaPlayer.setLooping(false);
        openSpeaker();
        try {
            mediaPlayer.setDataSource(path);
            mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(MediaPlayer mp) {
                    mediaPlayer.seekTo(time);
                    mediaPlayer.start();
                    if (playingListener != null) {
                        playingListener.playCallback();
                    }
                }
            });
            mediaPlayer.setOnErrorListener(onErrorListener);
            mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    openSpeaker();
                    if (playingListener != null) {
                        playingListener.onComplete();
                    }
                }
            });
            mediaPlayer.prepareAsync();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public boolean isPlaying() {
        return mediaPlayer != null && mediaPlayer.isPlaying();
    }

    public int getCurrentPosition() {
        if (isPlaying()) {
            return mediaPlayer.getCurrentPosition();
        }
        return 0;
    }

    public float getPlayProgress() {
        if (isPlaying()) {
            return mediaPlayer.getCurrentPosition() * 1.0f / mediaPlayer.getDuration();
        }
        return 0;
    }

    private OnErrorListener onErrorListener = (mp, what, extra) -> {
        T.ss("文件播放异常");
        return false;
    };

    private MediaPlayer.OnPreparedListener onCompletionListener = mp -> mp.start();

    public void release() {
        if (mediaPlayer != null) {
            mediaPlayer.release();
            mediaPlayer = null;
            //设置为正常状态
            openSpeaker();
        }
    }

    /**
     * 关闭听筒模式
     */
    private void openSpeaker() {
        audioManager.setMode(AudioManager.MODE_NORMAL);
        audioManager.setSpeakerphoneOn(true);
    }

    /**
     * 开启听筒模式
     */
    private void closeSpeaker() {
        audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        audioManager.setSpeakerphoneOn(false);//关闭扬声器
    }

    public interface PlayingListener {
        void playCallback();

        void onComplete();
    }
}
