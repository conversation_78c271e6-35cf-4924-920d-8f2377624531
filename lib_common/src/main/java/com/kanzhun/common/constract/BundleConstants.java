package com.kanzhun.common.constract;

/**
 * <AUTHOR>
 * @date 2021/6/23.
 */
public class BundleConstants {
    /**
     * Bundle传递参数key
     */
    public static final String BUNDLE_DATA = "data";
    public static final String BUNDLE_WEB_VIEW_BEAN = "web_view_bean";
    public static final String BUNDLE_PERSONALITY_TEST_BEAN = "personality_test_bean";//性格测试题
    public static final String BUNDLE_PAGE_COUNT = "page_count";//总数
    public static final String BUNDLE_PAGE_INDEX = "page_index";//索引
    public static final String BUNDLE_IDENTIFY_NAME = "identify_name";//认证姓名
    public static final String BUNDLE_IDENTIFY_NUM = "identify_num";//认证号码
    public static final String BUNDLE_CERTIFICATION_INFO_ERROR = "certification_info_error";//实名认证信息错误

    public static final String BUNDLE_USER_ID = "user_id";
    public static final String BUNDLE_SYSTEM_ID = "system_id";
    public static final String BUNDLE_CHAT_ID = "chat_id";
    public static final String BUNDLE_MSG_ID = "msg_id";
    public static final String BUNDLE_MSG_SENDER_ID = "msg_sender_id";
    public static final String BUNDLE_STATURE = "stature";// 身高
    public static final String BUNDLE_LIVING_PLACE_CODE = "living_place_code";// 现居地code
    public static final String BUNDLE_BIRTHDAY = "birthday";// 出生日期
    public static final String BUNDLE_FROM_NAME = "from_name";
    public static final String BUNDLE_TO_NAME = "to_name";
    public static final String BUNDLE_TO_CHAT_SCROLL_TO_BOTTOM = "chat_scroll_to_bottom";

    public static final String BUNDLE_A_B_THEME = "a_b_theme";
    public static final String BUNDLE_A_B_IMPRESSION = "a_b_impression";
    public static final String BUNDLE_A_B_IMPRESSION_GUIDE = "a_b_impression_guide";
    public static final String BUNDLE_A_B_IMPRESSION_SIDE = "a_b_impression_side";
    public static final String CROP_VIEW_ANIMATION = "crop_view_animation";
    public static final String BUNDLE_FILE_URI = "file_uri";
    public static final String BUNDLE_EDIT_TEXT = "edit_text";

    public static final String BUNDLE_AVATAR_UPLOAD_DATA = "avatar_upload_data";//形象照上传
    public static final String BUNDLE_TAG = "tag";
    public static final String BUNDLE_STRING_ID = "string_id";
    public static final String BUNDLE_STRING_1 = "string_1";
    public static final String BUNDLE_STRING_2 = "string_2";

    public static final String BUNDLE_CONTENT_DATA = "content_data";
    public static final String BUNDLE_IS_GUIDE = "is_guide";
    public static final String BUNDLE_TYPE = "bundle_type";
    public static final String BUNDLE_FROM = "from";// 来源
    public static final String BUNDLE_CERT_TYPE = "cert_type";//
    public static final String BUNDLE_PROTOCOL_FROM = "protocol_from";// 来源

    public static final String BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN = "UserGuideBlockInfoBean";// 阻断信息bean

    public static final String BUNDLE_LIKE_EACH_OTHER = "bundle_like_each_other";// 相互喜欢
    public static final String BUNDLE_NO_DATA_DESC = "no_data_desc";
    public static final String BUNDLE_BLOCK_INFO = "block_info";
    public static final String BUNDLE_BLOCK_GUIDE_MODEL = "block_guide_model";
    public static final String BUNDLE_BLOCK_TAB_SUMMARY_INFO = "block_tab_summary_info";
    public static final String BUNDLE_BLOCK_FROM_LIKE = "block_from_like";

    public static final String BUNDLE_LIKE_RELATED_TYPE = "like_related_type"; //我喜欢的或者喜欢我的
    public static final String BUNDLE_LIKE_ME_COUNT = "like_me_count"; //我喜欢的或者喜欢我的

    public static final String BUNDLE_DATA_SERIALIZABLE = "bundle_data_serializable";

    public static final String BUNDLE_VIDEO_CHAT_ROOM_ID = "video_chat_room_id";//服务端虚拟房间ID，与VIDEO_CHAT_NEBULA_ID一一对应
    public static final String BUNDLE_VIDEO_CHAT_TYPE = "video_chat_type";
    public static final String BUNDLE_VIDEO_CHAT_INVITATION = "video_chat_invitation";
    public static final String BUNDLE_VIDEO_CHAT_NEBULA_ID = "video_chat_nebula_id";//音视频SDK的资源ID
    public static final String BUNDLE_VIDEO_CHAT_TIME = "video_chat_time";//音视频接通后持续时间
    public static final String BUNDLE_VIDEO_CHAT_ACCEPT = "video_chat_accept";//音视频是否默认接通，用于对方发起邀请，点击悬浮窗

    public static final String BUNDLE_DATA_STRING = "bundle_data_string";
    public static final String BUNDLE_DATA_STRING_1 = "bundle_data_string_1";
    public static final String BUNDLE_DATA_STRING_2 = "bundle_data_string_2";
    public static final String BUNDLE_DATA_STRING_3 = "bundle_data_string_3";
    public static final String BUNDLE_DATA_LONG = "bundle_data_long";
    public static final String BUNDLE_DATA_LONG_1 = "bundle_data_long_1";
    public static final String BUNDLE_DATA_INT = "bundle_data_int";
    public static final String BUNDLE_DATA_INT_ARRAY = "bundle_data_int_array";
    public static final String BUNDLE_DATA_BOOLEAN = "bundle_data_boolean";
    public static final String BUNDLE_DATA_BOOLEAN_1 = "bundle_data_boolean_1";
    public static final String BUNDLE_DATA_BOOLEAN_2 = "bundle_data_boolean_2";
    public static final String BUNDLE_DATA_BOOLEAN_3 = "bundle_data_boolean_3";
    public static final String BUNDLE_DATA_INT_1 = "bundle_data_int_1";
    public static final String BUNDLE_DATA_INT_2 = "bundle_data_int_2";
    public static final String BUNDLE_DATA_INT_3 = "bundle_data_int_3";
    public static final String BUNDLE_DATA_INT_4 = "bundle_data_int_4";
    public static final String BUNDLE_ORDER = "order";

    public static final String BUNDLE_CHAT_NICK_NAME = "chat_nick_name";
    public static final String BUNDLE_CHAT_AVATAR = "chat_avatar";
    public static final String BUNDLE_CHAT_FRIEND = "chat_friend";
    public static final String BUNDLE_CHAT_MOOD_ICON = "chat_mood_icon";
    public static final String BUNDLE_CHAT_MOOD_TITLE = "chat_mood_title";
    public static final String BUNDLE_FILE_PATH = "file_path";
    public static final String BUNDLE_CIRCLE_ID = "circle_id";//圈子id
    public static final String BUNDLE_PUBLISH_TYPE = "publish_type";//发布类型

    public static final String BUNDLE_REPORT_USERID = "report_userid";
    public static final String BUNDLE_REPORT_RESOURCE_ID = "report_resource_id";
    public static final String BUNDLE_DATA_2 = "data_2";
    public static final String BUNDLE_DATA_3 = "data_3";
    public static final String BUNDLE_TEXT_REJECT_TYPE = "bundle_text_reject_type";
    public static final String BUNDLE_PHOTO_REJECT_TYPE = "bundle_photo_reject_type";

    public static final String BUNDLE_AVATAR = "bundle_avatar";
    public static final String BUNDLE_FRIEND_AVATAR = "bundle_friend_avatar";
    public static final String BUNDLE_REMAIN_TIMES = "bundle_remain_times";
    public static final String BUNDLE_MOOD_IC = "bundle_mood_ic";
    public static final String BUNDLE_SOURCE_ENUM = "bundle_source_enum";
    public static final String BUNDLE_SOURCE_ENUM_2 = "bundle_source_enum_2";
    public static final String BUNDLE_RELATION_STATUS = "bundle_relation_status";
    public static final String BUNDLE_LID = "bundle_lid";
    public static final String BUNDLE_THIRD_ID = "bundle_third_id";
    public static final String BUNDLE_THIRD_TYP = "bundle_third_type";
    public static final String BUNDLE_SOURCE_TYP = "bundle_source_type";
    public static final String BUNDLE_PHONE = "bundle_phone";
    public static final String BUNDLE_FACE_RESULT_CODE = "bundle_face_result_code";
    public static final String BUNDLE_FACE_CODE = "bundle_face_code";
    public static final String BUNDLE_FACE_MSG = "bundle_face_msg";
    public static final String BUNDLE_SECURITY_ID = "bundle_security_id";

    public static final String BUNDLE_AVATAR_ADVICE_ID = "bundle_avatar_advice_id";
    public static final String BUNDLE_AVATAR_PHOTO_ID = "bundle_avatar_photo_id";
    public static final String BUNDLE_AVATAR_PHOTO_URI = "bundle_avatar_photo_url";

    public static final String BUNDLE_AVATAR_CROP_IMAGE_URI = "bundle_avatar_crop_image_uri";

}
