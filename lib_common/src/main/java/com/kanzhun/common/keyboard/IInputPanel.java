package com.kanzhun.common.keyboard;

/**
 * <AUTHOR>
 * @date 2022/3/2.
 */
public interface IInputPanel extends IPanel {

    /**
     * 软键盘打开
     */
    void onSoftKeyboardOpened();

    /**
     * 软件盘关闭
     */
    void onSoftKeyboardClosed();

    /**
     * 设置布局动画处理监听器
     */
    void setOnLayoutAnimatorHandle(PanelMoveAnimatorHandler handler);

    /**
     * 设置输入面板（包括软键盘、表情、更多等）状态改变监听器
     */
    void setOnInputStateChangedListener(OnInputPanelStateChangedListener listener);
}
