package com.kanzhun.common.util;

import static com.kanzhun.common.util.ActivityAnimType.SLIDE_FROM_LEFT_TO_RIGHT;
import static com.kanzhun.common.util.ExecutorFactory.getMainHandler;

import android.app.Activity;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.sankuai.waimai.router.common.FragmentUriRequest;
import com.sankuai.waimai.router.components.UriSourceTools;
import com.kanzhun.common.R;
import com.kanzhun.common.app.AppThreadFactory;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.exception.MException;
import com.kanzhun.common.router.HiDefaultUriRequest;
import com.kanzhun.utils.L;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


/**
 * Created by monch on 15/8/31.
 */
public class AppUtil {

    private static final String tag = AppUtil.class.getSimpleName();

    /**
     * 启动一个新的Activity
     *
     * @param context 上下文
     * @param intent  意图
     */
    public static void startActivity(Context context, Intent intent) {
        startActivity(context, intent, false, ActivityAnimType.DEFAULT);
    }

    /**
     * 启动一个新的Activity
     *
     * @param context      上下文
     * @param intent       意图
     * @param jumpAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void startActivity(Context context, Intent intent, int jumpAnimType) {
        startActivity(context, intent, false, jumpAnimType);
    }

    /**
     * 启动一个新的Activity
     *
     * @param context  上下文
     * @param intent   意图
     * @param isFinish 是否销毁当前页面
     */
    public static void startActivity(Context context, Intent intent, boolean isFinish) {
        startActivity(context, intent, isFinish, ActivityAnimType.DEFAULT);
    }

    /**
     * 启动一个新的Activity
     *
     * @param context          上下文
     * @param intent           意图
     * @param isFinish         是否销毁当前页面
     * @param activityAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void startActivity(Context context, Intent intent, boolean isFinish, int activityAnimType) {
        if (context == null || intent == null) {
            L.i(tag, "跳转Activity时，context或intent不能为空");
            return;
        }
        if (intent.getComponent() == null
                && TextUtils.isEmpty(intent.getAction())) {
            L.i(tag, "跳转Activity时，跳转目标不能为空");
            return;
        }
        try {
            context.startActivity(intent);
        } catch (Exception e) {
            L.e("AppUtil", "startActivity error.", e);
            return;
        }
        if (isFinish && context instanceof Activity) ((Activity) context).finish();
        jumpActivityAnim(context, activityAnimType);
    }

    /**
     * 启动一个新的Activity，返回时并需要带返回值
     *
     * @param context     上下文
     * @param intent      意图
     * @param requestCode 请求码
     */
    public static void startActivityForResult(Context context, Intent intent, int requestCode) {
        startActivityForResult(context, intent, requestCode, ActivityAnimType.DEFAULT);
    }

    /**
     * 启动一个新的Activity，返回时并需要带返回值
     *
     * @param context          上下文
     * @param intent           意图
     * @param requestCode      请求码
     * @param activityAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void startActivityForResult(Context context, Intent intent, int requestCode, int activityAnimType) {
        if (context == null || intent == null) {
            L.i(tag, "跳转Activity时，context或intent不能为空");
            return;
        }
        if (!(context instanceof Activity)) {
            L.i(tag, "跳转Activity并需要返回值时，context必须为Activity");
            return;
        }
        Activity activity = (Activity) context;
        ComponentName info = intent.getComponent();
        if (info == null) {
            L.i(tag, "跳转Activity时，跳转目标不能为空");
            return;
        }
        activity.startActivityForResult(intent, requestCode);
        jumpActivityAnim(context, activityAnimType);
    }

    /**
     * 销毁一个Activity
     *
     * @param context 上下文
     */
    public static void finishActivity(Context context) {
        finishActivity(context, ActivityAnimType.DEFAULT);
    }

    //暂时解决一些跳转动画不正常情况
    public static void finishActivityDelay(Context context) {
        AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                finishActivity(context, ActivityAnimType.DEFAULT);
            }
        }, 10);

    }

    /**
     * 销毁一个Activity
     *
     * @param context      上下文
     * @param jumpAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void finishActivity(Context context, int jumpAnimType) {
        if (context == null || !(context instanceof Activity)) {
            L.i(tag, "销毁Activity时，context不能为空或必须为Activity");
            return;
        }
        Activity activity = (Activity) context;
        activity.finish();
        finishActivityAnim(activity, jumpAnimType);
    }

    /**
     * 跳转Activity动画
     *
     * @param context      上下文
     * @param jumpAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void jumpActivityAnim(Context context, int jumpAnimType) {
        Activity activity = null;
        if (context instanceof Activity) activity = (Activity) context;
        if (activity == null) return;
        switch (jumpAnimType) {
            case ActivityAnimType.NONE:
                activity.overridePendingTransition(0, 0);
                break;
            case ActivityAnimType.UP_GLIDE:
                activity.overridePendingTransition(R.anim.common_activity_new_enter_up_glide, R.anim.common_activity_old_exit_up_glide);
                break;
            case ActivityAnimType.ALPHA_FAST:
                activity.overridePendingTransition(R.anim.common_fade_in_fast, R.anim.common_fade_out_fast);
                break;
            case ActivityAnimType.ALPHA:
                activity.overridePendingTransition(R.anim.common_fade_in, R.anim.common_fade_out);
                break;
            case ActivityAnimType.ALPHA_SLOW:
                activity.overridePendingTransition(R.anim.common_fade_in_slow, R.anim.common_fade_out_slow);
                break;
            case ActivityAnimType.SCALE:
                activity.overridePendingTransition(R.anim.common_activity_scale_new_open, R.anim.common_activity_scale_old_close);
                break;
            case SLIDE_FROM_LEFT_TO_RIGHT:
                activity.overridePendingTransition(R.anim.common_activity_slide_from_left_to_right_enter, R.anim.common_activity_slide_from_left_to_right_exit);
                break;
            case ActivityAnimType.UP_ALPHA:
                activity.overridePendingTransition(R.anim.common_activity_slide_from_bottom_to_top_alpha_enter, R.anim.common_activity_old_exit_up_glide);
                break;
            default:
                activity.overridePendingTransition(R.anim.common_activity_new_enter_default, R.anim.common_activity_old_exit_default);
                break;
        }
    }

    /**
     * 销毁Activity动画
     *
     * @param activity     上下文
     * @param jumpAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void finishActivityAnim(Activity activity, int jumpAnimType) {
        switch (jumpAnimType) {
            case ActivityAnimType.NONE:
                activity.overridePendingTransition(0, 0);
                break;
            case ActivityAnimType.UP_GLIDE:
                activity.overridePendingTransition(R.anim.common_activity_old_enter_up_glide, R.anim.common_activity_new_exit_up_glide);
                break;
            case ActivityAnimType.ALPHA_FAST:
                activity.overridePendingTransition(R.anim.common_fade_in_fast, R.anim.common_fade_out_fast);
                break;
            case ActivityAnimType.ALPHA:
                activity.overridePendingTransition(R.anim.common_fade_in, R.anim.common_fade_out);
                break;
            case ActivityAnimType.ALPHA_SLOW:
                activity.overridePendingTransition(R.anim.common_fade_in_slow, R.anim.common_fade_out_slow);
                break;
            case ActivityAnimType.SCALE:
                activity.overridePendingTransition(R.anim.common_activity_scale_old_open, R.anim.common_activity_scale_new_close);
                break;
            default:
                activity.overridePendingTransition(R.anim.common_activity_old_enter_default, R.anim.common_activity_new_exit_default);
                break;
        }
    }

    /**
     * 开启一个服务
     *
     * @param context
     * @param clazz
     */
    public static void startService(Context context, Class<? extends Service> clazz) {
        startService(context, new Intent(context, clazz));
    }

    /**
     * 开启一个服务
     *
     * @param context
     * @param intent
     */
    public static void startService(Context context, Intent intent) {
        try {
            context.startService(intent);
        } catch (Throwable tr) {
            L.e(tag, "startService error.", tr);
        }
    }

    /**
     * 停止一个服务
     *
     * @param context
     * @param clazz
     */
    public static void stopService(Context context, Class<? extends Service> clazz) {
        stopService(context, new Intent(context, clazz));
    }

    /**
     * 停止一个服务
     *
     * @param context
     * @param intent
     */
    public static void stopService(Context context, Intent intent) {
        try {
            context.stopService(intent);
        } catch (Throwable tr) {
            L.e(tag, "stopService error.", tr);
        }
    }

    /**
     * Shows the soft input for its input text.
     * 使用QMUIKeyboardHelper中的键盘方法
     */
    @Deprecated
    public static void showSoftInput(Context context, View mInputText) {
        if (context == null || mInputText == null) return;

        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            mInputText.requestFocusFromTouch();
            inputMethodManager.viewClicked(mInputText);
            inputMethodManager.showSoftInput(mInputText, 0);
        }
    }

    /**
     * Hides the soft input if it is active for the input text.
     * 使用QMUIKeyboardHelper中的键盘方法
     */
    @Deprecated
    public static void hideSoftInput(Context context, View mInputText) {
        if (context == null || mInputText == null) return;

        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            inputMethodManager.hideSoftInputFromWindow(mInputText.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * Hides the soft input if it is active for the input text.
     * 使用QMUIKeyboardHelper中的键盘方法
     */
    @Deprecated
    public static void hideSoftInput(Activity activity) {
        if (activity == null) return;

        InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null) {
            View currentFocus = activity.getCurrentFocus();
            if (currentFocus != null) {
                inputMethodManager.hideSoftInputFromWindow(currentFocus.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }


    /**
     * 克隆实例方法，传入泛型的源数据实例，结果返回克隆后的泛型数据对象
     *
     * @param sourceObject 源数据实例
     * @param <T>          泛型数据，此泛型必须继承Object
     * @return 克隆后的泛型数据类型的实例
     * @throws Exception 克隆失败的异常
     */
    public static <T extends Object> T cloneObject(T sourceObject) {
        if (sourceObject != null) {
            if (sourceObject instanceof LinkedList) {
                try {
                    return (T) cloneLinkedList((List) sourceObject);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (sourceObject instanceof List) {
                try {
                    return (T) cloneArrayList((List) sourceObject);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        // 将sourceObject序列化
        ByteArrayOutputStream baos = null;
        ObjectOutputStream oos = null;
        ByteArrayInputStream bais = null;
        ObjectInputStream ois = null;
        Object resultObject = null;
        try {
            baos = new ByteArrayOutputStream();
            oos = new ObjectOutputStream(baos);
            oos.writeObject(sourceObject);
            // 得到反序列化的结果
            bais = new ByteArrayInputStream(baos.toByteArray());
            ois = new ObjectInputStream(bais);
            resultObject = ois.readObject();
        } catch (Exception e) {
            MException.printError(e);
            resultObject = sourceObject;
        } finally {
            try {
                if (ois != null) ois.close();
                if (bais != null) bais.close();
                if (oos != null) oos.close();
                if (baos != null) baos.close();
            } catch (Exception e) {
                MException.printError(e);
            }
        }
        // 返回强转为泛型后的结果
        return resultObject != null ? (T) resultObject : null;
    }

    private static List cloneArrayList(List sourceList) {
        int size = sourceList.size();
        List newList = new ArrayList();
        for (int i = 0; i < size; i++) {
            newList.add(cloneObject(sourceList.get(i)));
        }
        return newList;
    }

    private static List cloneLinkedList(List sourceList) {
        int size = sourceList.size();
        List newList = new LinkedList();
        for (int i = 0; i < size; i++) {
            newList.add(cloneObject(sourceList.get(i)));
        }
        return newList;
    }


    /**
     * 启动一个新的Activity，返回时并需要带返回值
     *
     * @param context     上下文
     * @param intent      意图
     * @param requestCode 请求码
     */
    public static void startActivityForResultOfFragment(Context context, Fragment fragment, Intent intent, int requestCode) {
        startActivityForResultOfFragment(context, fragment, intent, requestCode, ActivityAnimType.DEFAULT);
    }


    /**
     * 启动一个新的Activity，返回时并需要带返回值
     *
     * @param context          上下文
     * @param intent           意图
     * @param requestCode      请求码
     * @param activityAnimType 跳转动画，类型使用{@link ActivityAnimType}下的常量类型
     */
    public static void startActivityForResultOfFragment(Context context, Fragment fragment, Intent intent, int requestCode, int activityAnimType) {
        if (context == null || intent == null) {
            L.i(tag, "跳转Activity时，context或intent不能为空");
            return;
        }
        if (!(context instanceof Activity)) {
            L.i(tag, "跳转Activity并需要返回值时，context必须为Activity");
            return;
        }
        ComponentName info = intent.getComponent();
        if (info == null) {
            L.i(tag, "跳转Activity时，跳转目标不能为空");
            return;
        }
        fragment.startActivityForResult(intent, requestCode);
        jumpActivityAnim(context, activityAnimType);
    }

    private static int[] getActivityAnim(int jumpAnimType) {
        int[] anim = new int[]{0, 0};
        switch (jumpAnimType) {
            case ActivityAnimType.NONE:
                anim[0] = 0;
                anim[1] = 0;
                break;
            case ActivityAnimType.UP_GLIDE:
                anim[0] = R.anim.common_activity_new_enter_up_glide;
                anim[1] = R.anim.common_activity_old_exit_up_glide;
                break;
            case ActivityAnimType.ALPHA_FAST:
                anim[0] = R.anim.common_fade_in_fast;
                anim[1] = R.anim.common_fade_out_fast;
                break;
            case ActivityAnimType.ALPHA:
                anim[0] = R.anim.common_fade_in;
                anim[1] = R.anim.common_fade_out;
                break;
            case ActivityAnimType.ALPHA_SLOW:
                anim[0] = R.anim.common_fade_in_slow;
                anim[1] = R.anim.common_fade_out_slow;
                break;
            case ActivityAnimType.SCALE:
                anim[0] = R.anim.common_activity_scale_new_open;
                anim[1] = R.anim.common_activity_scale_old_close;
                break;
            case ActivityAnimType.SLIDE_FROM_LEFT_TO_RIGHT:
                anim[0] = R.anim.common_activity_slide_from_left_to_right_enter;
                anim[1] = R.anim.common_activity_slide_from_left_to_right_exit;
                break;
            case ActivityAnimType.UP_ALPHA:
                anim[0] = R.anim.common_activity_slide_from_left_to_right_enter;
                anim[1] = R.anim.common_activity_old_exit_up_glide;
                break;
            default:
                anim[0] = R.anim.common_activity_new_enter_default;
                anim[1] = R.anim.common_activity_old_exit_default;
                break;
        }
        return anim;
    }

    /**
     * 跳转到一个指定 url 路径
     *
     * @param path 路由路径
     */
    public static void startUri(Context context, String path) {
        startUri(context, path, null, ActivityAnimType.DEFAULT);
    }

    /**
     * 跳转到一个指定 url 路径
     *
     * @param path     路由路径
     * @param animType 动画类型
     */
    public static void startUri(Context context, String path, int animType) {
        startUri(context, path, null, animType);
    }

    public static void startUri(Context context, String path, Bundle data) {
        startUri(context, path, data, ActivityAnimType.DEFAULT);
    }

    /**
     * 跳转到一个指定 url 路径
     *
     * @param path     路由路径
     * @param animType 动画类型
     */
    public static void startUri(@NonNull Context context, @NonNull String path, @Nullable Bundle data, int animType) {

        // 这里的start实际也是调用了Router.startUri方法
        getDefaultUriRequest(context, path, data, animType).start();
    }

    public static DefaultUriRequest getDefaultUriRequest(@NonNull Context context, @NonNull String path) {
        return getDefaultUriRequest(context, path, null, ActivityAnimType.DEFAULT);
    }

    public static DefaultUriRequest getDefaultUriRequest(@NonNull Context context, @NonNull String path, @Nullable Bundle data, int animType) {
        int[] anim = getActivityAnim(animType);
        return new HiDefaultUriRequest(context, path)
                // 设置跳转来源，默认为内部跳转，还可以是来自WebView、来自Push通知等。
                // 目标Activity可通过UriSourceTools区分跳转来源。
                .from(UriSourceTools.FROM_INTERNAL)
                .putExtras(data)
                // 设置Activity跳转动画
                .overridePendingTransition(anim[0], anim[1]);
    }

    /**
     * 跳转到一个指定 url 路径
     *
     * @param requestCode 请求码
     * @param path        路由路径
     */
    public static void startUriForResult(Context context, String path, int requestCode) {
        startUriForResult(context, path, requestCode, null, ActivityAnimType.DEFAULT);
    }

    /**
     * 跳转到一个指定 url 路径
     *
     * @param requestCode 请求码
     * @param path        路由路径
     */
    public static void startUriForResult(Context context, String path, int requestCode, @Nullable Bundle data) {
        startUriForResult(context, path, requestCode, data, ActivityAnimType.DEFAULT);
    }

    /**
     * 目前适用于activity 跳转activity
     * 跳转到一个指定 url 路径
     *
     * @param requestCode 请求码
     * @param path        路由路径
     * @param animType    动画类型
     */
    public static void startUriForResult(Context context, String path, int requestCode, @Nullable Bundle data, int animType) {
        // 这里的start实际也是调用了Router.startUri方法
        getDefaultUriResultRequest(context, path, requestCode, data, animType).start();
    }

    public static DefaultUriRequest getDefaultUriResultRequest(Context context, String path, int requestCode, @Nullable Bundle data, int animType) {
        int[] anim = getActivityAnim(animType);
        return new HiDefaultUriRequest(context, path)
                // startActivityForResult使用的RequestCode
                .activityRequestCode(requestCode)
                // 设置跳转来源，默认为内部跳转，还可以是来自WebView、来自Push通知等。
                // 目标Activity可通过UriSourceTools区分跳转来源。
                .from(UriSourceTools.FROM_INTERNAL)
                .putExtras(data)
                // 设置Activity跳转动画
                .overridePendingTransition(anim[0], anim[1]);
    }

    public static void startFragmentUriForResult(Fragment fragment, String path, int requestCode, @Nullable Bundle data, int animType) {
        getDefaultFragmentUriResultRequest(fragment, path, requestCode, data, animType).start();
    }

    public static void startFragmentUri(Fragment fragment, String path, @Nullable Bundle data, int animType) {
        getDefaultFragmentUriRequest(fragment, path, data, animType).start();
    }

    public static DefaultUriRequest getDefaultFragmentUriRequest(@NonNull Fragment fragment, @NonNull String path, @Nullable Bundle data, int animType) {
        int[] anim = getActivityAnim(animType);
        return new FragmentUriRequest(fragment, path)
                .putExtras(data)
                // 设置Activity跳转动画
                .overridePendingTransition(anim[0], anim[1]);
    }

    public static DefaultUriRequest getDefaultFragmentUriResultRequest(@NonNull Fragment fragment, @NonNull String path, int requestCode, @Nullable Bundle data, int animType) {
        int[] anim = getActivityAnim(animType);
        return new FragmentUriRequest(fragment, path)
                .activityRequestCode(requestCode)
                .putExtras(data)
                .overridePendingTransition(anim[0], anim[1]);
    }


    public static void exitApp() {
        BaseApplication.getApplication().finishAll();
        getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                System.exit(0);
            }
        }, 300);
    }



}
