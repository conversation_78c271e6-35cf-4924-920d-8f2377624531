@file:Suppress("DEPRECATION")

package com.kanzhun.common.util

import android.net.Uri
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.postprocessors.IterativeBoxBlurPostProcessor
import com.facebook.imagepipeline.request.ImageRequestBuilder
import com.techwolf.lib.tlog.TLog

private const val TAG = "FrescoUtil"

fun SimpleDraweeView.loadBlur(
    url: String?,
    iterations: Int,
    blurRadius: Int,
    autoPlayAnimation: Boolean = false,
) {
    try {
        val uri = Uri.parse(url)
        val request = ImageRequestBuilder.newBuilderWithSource(uri)
            .setPostprocessor(IterativeBoxBlurPostProcessor(iterations, blurRadius))
            .build()
        val controller = Fresco.newDraweeControllerBuilder()
            .setAutoPlayAnimations(autoPlayAnimation)
            .setOldController(this.controller)
            .setImageRequest(request)
            .build()
        this.setController(controller)
    } catch (e: Exception) {
        TLog.error(TAG, "SimpleDraweeView.load: $e")
    }
}

fun SimpleDraweeView.load(
    url: String?,
    type: Int? = 0,
    autoPlayAnimation: Boolean = true,
) {
    if (type == 2) {
        this.loadBlur(
            url = url,
            iterations = 2,
            blurRadius = 25,
            autoPlayAnimation = autoPlayAnimation,
        )
    } else {
//        val imageRequest = ImageRequestBuilder.newBuilderWithResourceId(R.raw.avatar).build();

        setController(
            Fresco.newDraweeControllerBuilder()
                .setUri(url)
//                .setUri(imageRequest.sourceUri)
//                .setUri("https://mathiasbynens.be/demo/animated-webp-supported.webp")
//                .setUri("https://lengjing-cdn.zhipin.com/azeroth/static/activity/241209/wiD20zaadyGQNmfR9MaCipiZD4JrG-qtxZpTmIGMQkt7b1SQ8EU9o2QSIrmvLIQ_0b5sALOH3gyLwAr6bgRncvs_wjIqTQIfXGVKAxTRYBY~.webp")
                .setAutoPlayAnimations(autoPlayAnimation)
                .build()
        )
    }
}
