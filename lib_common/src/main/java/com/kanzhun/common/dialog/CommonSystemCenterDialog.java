package com.kanzhun.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.kanzhun.common.R;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/9/23
 * 居中公共系统弹窗 按照最新规范修改
 */
public class CommonSystemCenterDialog extends CommonBaseDialog<CommonSystemCenterDialog.Builder> {

    private ImageView ivIcon;
    private TextView tvTitle, tvContent;
    private ConstraintLayout clHBtn;
    private TextView tvHNegative, tvVPositive, tvVNegative;
    private QMUIRoundButton tvHPositive;
    private LinearLayout idVLinear;
    private View viewVerticalLine;


    protected CommonSystemCenterDialog(CommonSystemCenterDialog.Builder builder, Context context) {
        super(builder, context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.common_dialog_system_center);
        setWindowParams();
        setViewClick();
        initView();
    }

    private void initView() {
        ivIcon = findViewById(R.id.iv_icon);
        tvTitle = findViewById(R.id.tv_title);
        tvContent = findViewById(R.id.tv_content);
        clHBtn = findViewById(R.id.cl_h_btn);
        tvHNegative = findViewById(R.id.tv_h_negative);
        tvHPositive = findViewById(R.id.tv_h_positive);
        tvVPositive = findViewById(R.id.tv_v_positive);
        tvVNegative = findViewById(R.id.tv_v_negative);
        idVLinear = findViewById(R.id.idVLinear);
        viewVerticalLine = findViewById(R.id.view_vertical_line);

        if (builder.resId > 0) {
            ivIcon.setVisibility(View.VISIBLE);
            ivIcon.setBackgroundResource(builder.resId);
        }
        if (!TextUtils.isEmpty(builder.title)) {
            tvTitle.setText(builder.title);
        }
        if (!TextUtils.isEmpty(builder.content)) {
            tvContent.setVisibility(View.VISIBLE);
            tvContent.setText(builder.content);
        }
        if (!TextUtils.isEmpty(builder.contentLikeText)) {
            tvContent.setVisibility(View.VISIBLE);
            tvContent.setText(builder.contentLikeText);
            tvContent.setMovementMethod(LinkMovementMethod.getInstance());
        }
        tvHPositive.setText(builder.positiveText);
        tvVPositive.setText(builder.positiveText);
        tvHNegative.setText(builder.negativeText);
        tvVNegative.setText(builder.negativeText);
        if (builder.onlyBtn) {
            clHBtn.setVisibility(View.GONE);
            tvVPositive.setVisibility(View.VISIBLE);
            idVLinear.setVisibility(View.VISIBLE);
        } else {
            if (builder.btnIsVertical) {
                clHBtn.setVisibility(View.GONE);
                tvVPositive.setVisibility(View.VISIBLE);
                viewVerticalLine.setVisibility(View.VISIBLE);
                tvVNegative.setVisibility(View.VISIBLE);
                idVLinear.setVisibility(View.VISIBLE);
            }
        }
        if (builder.positiveIsRed) {
            tvHPositive.setBackgroundColor(getContext().getColor(R.color.common_color_FF3F4B));
            tvHPositive.setStrokeData(0,null);
        }
    }


    public static class Builder extends CommonBaseDialog.Builder<CommonSystemCenterDialog.Builder, CommonSystemCenterDialog> {
        private String title;
        private String content;
        private CharSequence contentLikeText;
        private int resId;  //icon
        private String positiveText;
        private String negativeText;
        private boolean positiveIsRed; //是否是红色的
        private boolean btnIsVertical; //按钮是否垂直布局
        private boolean onlyBtn; //是否只有一个按钮
        protected OnClickListener clickListener;
        private boolean autoDismiss = true;

        public Builder(Context context) {
            super(context);
        }


        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContent(String content) {
            this.content = content;
            return this;
        }

        public Builder setContentLikeText(CharSequence contentLikeText) {
            this.contentLikeText = contentLikeText;
            return this;
        }

        public Builder setResId(int resId) {
            this.resId = resId;
            return this;
        }

        public Builder setPositiveText(String positiveText) {
            this.positiveText = positiveText;
            return this;
        }

        public Builder setNegativeText(String negativeText) {
            this.negativeText = negativeText;
            return this;
        }

        public Builder setPositiveIsRed(boolean positiveIsRed) {
            this.positiveIsRed = positiveIsRed;
            return this;
        }

        public Builder setBtnIsVertical(boolean btnIsVertical) {
            this.btnIsVertical = btnIsVertical;
            return this;
        }

        public Builder setOnlyBtn(boolean onlyBtn) {
            this.onlyBtn = onlyBtn;
            return this;
        }

        public Builder setAutoDismiss(boolean autoDismiss) {
            this.autoDismiss = autoDismiss;
            return this;
        }

        public Builder setButtonClickListener(OnClickListener listener) {
            this.clickListener = listener;
            return this;
        }


        @Override
        public CommonSystemCenterDialog createDialog() {
            setCancelable(false)
                    .add(R.id.tv_h_negative)
                    .add(R.id.tv_h_positive)
                    .add(R.id.tv_v_negative)
                    .add(R.id.tv_v_positive)
                    .setCanceledOnTouchOutside(false)
                    .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
                        @Override
                        public void onItemClick(Dialog dialog, View view) {
                            if (autoDismiss) {
                                dialog.dismiss();
                            }
                            if (clickListener != null) {
                                if (view.getId() == R.id.tv_h_positive || view.getId() == R.id.tv_v_positive) {
                                    clickListener.onPositiveClick(dialog, view);
                                } else if (view.getId() == R.id.tv_v_negative || view.getId() == R.id.tv_h_negative) {
                                    clickListener.onNegativeClick(dialog, view);
                                }
                            }
                        }
                    });
            return new CommonSystemCenterDialog(this, context);
        }
    }

    public interface OnClickListener {
        void onPositiveClick(Dialog dialog, View view);

        void onNegativeClick(Dialog dialog, View view);
    }
}
