package com.kanzhun.common.kotlin.ui.fragment

import android.content.Context
import android.os.Bundle
import android.view.View
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.base.AllBaseFragment
import com.kanzhun.common.kotlin.ui.dialog.loading.ILoadingDialog
import com.kanzhun.common.kotlin.ui.dialog.loading.LoadingDialogDelegate

open class BaseFragment :AllBaseFragment(),ILoadingDialog {

    protected var mActivity: AllBaseActivity? = null

    protected var loadingDialogDelegate: LoadingDialogDelegate? = null


    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (mActivity == null) {
            mActivity = context as AllBaseActivity
        }
        if(loadingDialogDelegate == null){
            loadingDialogDelegate = LoadingDialogDelegate(mActivity)
            loadingDialogDelegate?.attachActivity(mActivity)
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }

    override fun showProgressDialog(text: String?, cancelable: Boolean) {
        loadingDialogDelegate?.showProgressDialog(text, cancelable)
    }

    override fun showProgressDialog(resId: Int, cancelable: Boolean) {
        loadingDialogDelegate?.showProgressDialog(resId, cancelable)
    }

    override fun dismissProgressDialog() {
        loadingDialogDelegate?.dismissProgressDialog()
    }




}