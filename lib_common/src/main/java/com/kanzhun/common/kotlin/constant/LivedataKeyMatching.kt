package com.kanzhun.common.kotlin.constant

class LivedataKeyMatching {
    companion object{
        const val MATCH_SEND_LIKE_SUCCESS = "match_send_like_success"
        //互动的列表们主动刷新逻辑
        const val MATCH_INTERACT_LIST_REFRESH = "match_interact_list_refresh"
        //互动的列表提示刷新条
        const val MATCH_INTERACT_LIST_REFRESH_HINT = "match_interact_list_refresh_hint"
        //匹配首页新手引导蒙层
        const val MATCH_HOME_USER_COVER_GUIDE = "match_interact_view_me_refresh"
        //匹配首页新手引导蒙层结束
        const val MATCH_HOME_USER_COVER_GUIDE_FINISH = "match_interact_view_me_refresh_finish"
        //隐藏互动的列表提示刷新条消失
        const val MATCH_INTERACT_LIST_REFRESH_HINT_DISMISS = "MATCH_INTERACT_LIST_REFRESH_HINT_DISMISS"
        //隐藏互动的列表tab数字角标
        const val MATCH_INTERACT_LIST_TAB_NUM_DISMISS = "MATCH_INTERACT_LIST_TAB_NUM_DISMISS"

        //通知-要求执行点赞
        const val MATCH_NOTIFY_DO_LIKE = "MATCH_NOTIFY_DO_LIKE"
    }
}

