package com.kanzhun.common.kotlin.ui.recyclerview

import android.content.Context
import android.util.DisplayMetrics
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.hpbr.ui.recyclerview.ListData
import com.kanzhun.common.kotlin.ext.dpI

/**
 * 请在ViewHolder中使用
 */
inline fun <reified T : ViewBinding> View.getViewBinding(): T {
    var binding = getTag(Int.MIN_VALUE) as? T
    if (binding == null) {
        val method =T::class.java.getMethod("bind", View::class.java)
        binding = method.invoke(null, this) as T
        setTag(Int.MIN_VALUE, binding)
    }
    return binding
}
/**
 * 请在ViewHolder中使用
 */
inline fun <reified T : ViewBinding> BaseViewHolder.getViewBinding(): T {
    var binding = itemView.getTag(Int.MIN_VALUE) as? T
    if (binding == null) {
        val method =T::class.java.getMethod("bind", View::class.java)
        binding = method.invoke(null, itemView) as T
        itemView.setTag(Int.MIN_VALUE, binding)
    }
    return binding
}

fun <T : BaseListItem> CommonListAdapter<T>.setListData(listData: ListData<T>) {
    if (listData.isRefresh) {
        this.setNewInstance(listData.data.toMutableList())
    } else {
        this.addData(listData.data)
    }
}

/*
* 本质是添加一个footer，如果列表里面有，注意顺序
 */
fun <T : BaseListItem> CommonListAdapter<T>.addPaddingButtonAtLast(context: Context,dp:Int) {
    val view = View(context)
    val layoutParams = RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, dp.dpI)
    view.layoutParams = layoutParams
    addFooterView(view)
}

/**
 * recyclerview滚动到指定位置
 * @param speed 滚动速度，值越大，速度越快
 */
fun RecyclerView.scrollToPositionTop(position: Int, speed: Float = 35F) {
    if (position != -1) {
        val mLayoutManager = layoutManager
        if (mLayoutManager is LinearLayoutManager) {
            val smollScroller = KZSmoothScroller(context, speed)
            smollScroller.targetPosition = position
            mLayoutManager.startSmoothScroll(smollScroller)
        }
    }

}

private class KZSmoothScroller(var context: Context, val speed:Float = 35F) : LinearSmoothScroller(context) {


    override fun getHorizontalSnapPreference(): Int {
        return SNAP_TO_START
    }

    override fun getVerticalSnapPreference(): Int {
        return SNAP_TO_START
    }

    override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics?): Float {
        return speed / (displayMetrics?.densityDpi ?: 300)
    }
}

fun RecyclerView.scrollToPositionTopFast(position: Int, offset:Int=0) {
    val count = adapter?.itemCount?:0
    if (position >= 0 && count > 0 && position < count) {

        val mLayoutManager = layoutManager
        if (mLayoutManager is LinearLayoutManager) {
            mLayoutManager.scrollToPositionWithOffset(position, offset)
        }
        if (mLayoutManager is GridLayoutManager) {
            mLayoutManager.scrollToPositionWithOffset(position, offset)
        }
    }
}




