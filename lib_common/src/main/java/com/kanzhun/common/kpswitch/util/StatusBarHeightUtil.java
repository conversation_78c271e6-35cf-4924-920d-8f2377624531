/*
 * Copyright (C) 2015-2017 <PERSON><PERSON><PERSON>(blog.dreamtobe.cn)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.kanzhun.common.kpswitch.util;

import android.content.Context;
import android.util.Log;

/**
 * Created by <PERSON><PERSON><PERSON> on 3/26/16.
 * <p/>
 * In order to avoid the layout of the Status bar.
 */
public class StatusBarHeightUtil {

    private static boolean init = false;
    private static int statusBarHeight = 50;

    private static final String STATUS_BAR_DEF_PACKAGE = "android";
    private static final String STATUS_BAR_DEF_TYPE = "dimen";
    private static final String STATUS_BAR_NAME = "status_bar_height";

    public static synchronized int getStatusBarHeight(final Context context) {
        if (!init) {
            int resourceId = context.getResources().
                    getIdentifier(STATUS_BAR_NAME, STATUS_BAR_DEF_TYPE, STATUS_BAR_DEF_PACKAGE);
            if (resourceId > 0) {
                statusBarHeight = context.getResources().getDimensionPixelSize(resourceId);
                init = true;
                Log.d("StatusBarHeightUtil",
                        String.format("Get status bar height %d", statusBarHeight));
            }
        }

        return statusBarHeight;
    }
}
