package com.kanzhun.common.views.loading;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 * @date 2020/4/22.
 */
public class DoubleBounce extends Sprite {
    float value;
    int circleColor1 = Color.RED;
    int circleColor2 = Color.GREEN;
    public static final float scale = 0.6f;
    private Paint paint = new Paint();
    private boolean isDoubleTravel = false;

    public DoubleBounce(int circleColor1, int circleColor2) {
        this.circleColor1 = circleColor1;
        this.circleColor2 = circleColor2;
        paint.setAntiAlias(true);
    }

    @Override
    public ValueAnimator onCreateAnimation() {
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(0f, 1f);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.setDuration(1000);
        valueAnimator.setRepeatCount(ValueAnimator.INFINITE);
        return valueAnimator;
    }

    @Override
    public void onAnimationValueUpdate(Object animatedValue) {
        value = (float) animatedValue;
    }

    @Override
    public void onAnimationRepeat(Animator animation) {
        isDoubleTravel = !isDoubleTravel;
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        if (getBounds() != null) {
            float maxRadius = getBounds().height() / 2;
            float minRadius = maxRadius * scale;
            float radius = minRadius;
            float range = maxRadius - minRadius;
            float leftX = 0;
            float rightX = 0;
            leftX = value * (getBounds().width() - 2 * minRadius) + minRadius;
            rightX = getBounds().width() - leftX;
            radius = minRadius + (value > 0.5 ? (1 - value) : value) * range;

            if (isDoubleTravel) {
                paint.setColor(circleColor1);
                canvas.drawCircle(rightX,
                        getBounds().centerY(),
                        radius, paint);
                paint.setColor(circleColor2);
                canvas.drawCircle(leftX,
                        getBounds().centerY(),
                        radius, paint);
            } else {
                paint.setColor(circleColor2);
                canvas.drawCircle(rightX,
                        getBounds().centerY(),
                        radius, paint);
                paint.setColor(circleColor1);
                canvas.drawCircle(leftX,
                        getBounds().centerY(),
                        radius, paint);
            }
        }
    }

}
