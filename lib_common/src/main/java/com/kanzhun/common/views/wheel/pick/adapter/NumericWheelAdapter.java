package com.kanzhun.common.views.wheel.pick.adapter;


import com.kanzhun.common.views.wheel.adapter.WheelAdapter;

/**
 * Numeric Wheel adapter.
 *
 * <AUTHOR>
 * @date 2022/3/15.
 */
public class NumericWheelAdapter implements WheelAdapter {

    private int minValue;
    private int maxValue;

    /**
     * Constructor
     *
     * @param minValue the wheel min value
     * @param maxValue the wheel max value
     */
    public NumericWheelAdapter(int minValue, int maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
    }

    @Override
    public Object getItem(int index) {
        if (index >= 0 && index < getItemsCount()) {
            int value = minValue + index;
            return value;
        }
        return 0;
    }

    @Override
    public int getItemsCount() {
        return maxValue - minValue + 1;
    }

    @Override
    public int indexOf(Object o) {
        try {
            return (int) o - minValue;
        } catch (Exception e) {
            return -1;
        }

    }
}
