package com.kanzhun.common.views.image;

import com.kanzhun.common.model.ImageInfo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/30
 * description:图片视频切换 视频实体类
 */
public class Video implements Serializable {

    private long mid;
    private long seq;
    private long chatId;
    private int chatType;

    private String url;
    private String name;
    private long size;
    private int duration;
    private String localPath;
    private ImageInfo thumbnail;

    public Video(long mid, long seq, long chatId, int chatType) {
        this.mid = mid;
        this.seq = seq;
        this.chatId = chatId;
        this.chatType = chatType;
    }

    public long getMid() {
        return mid;
    }

    public void setMid(long mid) {
        this.mid = mid;
    }

    public long getSeq() {
        return seq;
    }

    public void setSeq(long seq) {
        this.seq = seq;
    }

    public long getChatId() {
        return chatId;
    }

    public void setChatId(long chatId) {
        this.chatId = chatId;
    }

    public int getChatType() {
        return chatType;
    }

    public void setChatType(int chatType) {
        this.chatType = chatType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public ImageInfo getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(ImageInfo thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
