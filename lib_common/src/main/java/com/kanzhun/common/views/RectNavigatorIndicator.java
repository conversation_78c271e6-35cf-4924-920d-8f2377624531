package com.kanzhun.common.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;

import net.lucode.hackware.magicindicator.abs.IPagerNavigator;
import net.lucode.hackware.magicindicator.buildins.UIUtil;

/**
 * <AUTHOR>
 * @date 2022/4/18.
 */
public class RectNavigatorIndicator extends View implements IPagerNavigator {
    private int mRadius;
    private int mIndicatorSelectedColor;
    private int mIndicatorUnSelectedColor;
    private int indicatorSelectedWidth;
    private int indicatorUnSelectedWidth;
    private int mSpacing;
    private int mCurrentIndex;
    private int mTotalCount;

    private Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    public RectNavigatorIndicator(Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        mRadius = UIUtil.dip2px(context, 7);
        mSpacing = UIUtil.dip2px(context, 8);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(measureWidth(widthMeasureSpec), measureHeight(heightMeasureSpec));
    }

    private int measureWidth(int widthMeasureSpec) {
        int mode = MeasureSpec.getMode(widthMeasureSpec);
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int result = 0;
        switch (mode) {
            case MeasureSpec.EXACTLY:
                result = width;
                break;
            case MeasureSpec.AT_MOST:
            case MeasureSpec.UNSPECIFIED:
                result = indicatorSelectedWidth + (mTotalCount - 1) * (mSpacing + indicatorUnSelectedWidth) + getPaddingLeft() + getPaddingRight();
                break;
            default:
                break;
        }
        return result;
    }

    private int measureHeight(int heightMeasureSpec) {
        int mode = MeasureSpec.getMode(heightMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        int result = 0;
        switch (mode) {
            case MeasureSpec.EXACTLY:
                result = height;
                break;
            case MeasureSpec.AT_MOST:
            case MeasureSpec.UNSPECIFIED:
                result = mRadius * 2 + +getPaddingTop() + getPaddingBottom();
                break;
            default:
                break;
        }
        return result;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mTotalCount > 0) {
            float top = getPaddingTop();
            float bottom = getHeight() - getPaddingBottom();
            mPaint.setStyle(Paint.Style.FILL);
            float nextLeft = getPaddingLeft();
            for (int i = 0; i < mTotalCount; i++) {
                float right;
                if (i == mCurrentIndex) {
                    mPaint.setColor(mIndicatorSelectedColor);
                    right = nextLeft + indicatorSelectedWidth;
                } else {
                    mPaint.setColor(mIndicatorUnSelectedColor);
                    right = nextLeft + indicatorUnSelectedWidth;
                }
                canvas.drawRoundRect(nextLeft, top, right, bottom, mRadius, mRadius, mPaint);
                nextLeft = right + mSpacing;
            }
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
    }


    @Override
    public void onPageSelected(int position) {
        mCurrentIndex = position;
        invalidate();
    }

    @Override
    public void onPageScrollStateChanged(int state) {
    }

    @Override
    public void onAttachToMagicIndicator() {
    }

    @Override
    public void notifyDataSetChanged() {
        invalidate();
    }

    @Override
    public void onDetachFromMagicIndicator() {
    }

    public void setCount(int count) {
        mTotalCount = count;  // 此处不调用invalidate，让外部调用notifyDataSetChanged
    }

    public void setRadius(int mRadius) {
        this.mRadius = mRadius;
    }

    public void setIndicatorSelectedColor(int mIndicatorSelectedColor) {
        this.mIndicatorSelectedColor = mIndicatorSelectedColor;
    }

    public void setIndicatorUnSelectedColor(int mIndicatorUnSelectedColor) {
        this.mIndicatorUnSelectedColor = mIndicatorUnSelectedColor;
    }

    public void setIndicatorSelectedWidth(int indicatorSelectedWidth) {
        this.indicatorSelectedWidth = indicatorSelectedWidth;
    }

    public void setIndicatorUnSelectedWidth(int indicatorUnSelectedWidth) {
        this.indicatorUnSelectedWidth = indicatorUnSelectedWidth;
    }

    public void setSpacing(int mSpacing) {
        this.mSpacing = mSpacing;
    }
}
