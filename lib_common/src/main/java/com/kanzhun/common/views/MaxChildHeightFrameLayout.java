package com.kanzhun.common.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.R;

/**
 * <AUTHOR>
 * @date 2022/9/15.
 */
public class MaxChildHeightFrameLayout extends FrameLayout {
    int maxChildHeight;

    public MaxChildHeightFrameLayout(@NonNull Context context) {
        this(context, null);
    }

    public MaxChildHeightFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MaxChildHeightFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.common_max_child_height_layout);
        maxChildHeight = array.getDimensionPixelSize(R.styleable.common_max_child_height_layout_common_max_child_height, 0);
        array.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int height = getMeasuredHeight();
        if (height > maxChildHeight) {
            int childCount = getChildCount();
            for (int i = 0; i < childCount; i++) {
                View child = getChildAt(i);
                child.measure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(maxChildHeight, MeasureSpec.EXACTLY));
            }
        }
    }
}
