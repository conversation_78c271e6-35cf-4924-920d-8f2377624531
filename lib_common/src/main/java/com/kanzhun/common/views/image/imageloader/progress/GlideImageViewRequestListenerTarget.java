package com.kanzhun.common.views.image.imageloader.progress;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.transition.Transition;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/22
 */
public class GlideImageViewRequestListenerTarget extends DrawableImageViewTarget {

    private OnRequestListener requestListener;

    public GlideImageViewRequestListenerTarget(ImageView view, OnRequestListener requestListener) {
        super(view);
        this.requestListener = requestListener;
    }

    @Override
    public void onLoadFailed(@Nullable Drawable errorDrawable) {
        if (requestListener != null) {
            requestListener.onLoadFile();
        }
        super.onLoadFailed(errorDrawable);
    }

    @Override
    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
        if (requestListener != null) {
            requestListener.onLoadSuccess(resource);
        }
        super.onResourceReady(resource, transition);
    }
}
