package com.kanzhun.common.views.loading;

import com.kanzhun.common.R;
import com.kanzhun.common.base.BaseApplication;

public class SpriteFactory {

    public static Sprite create(Style style) {
        Sprite sprite = null;
        switch (style) {
            case DOUBLE_BOUNCE:
                sprite = new DoubleBounce(BaseApplication.getApplication().getResources().getColor(R.color.common_color_5D68E8), BaseApplication.getApplication().getResources().getColor(R.color.common_color_55D5DB));
                break;
            default:
                break;
        }
        return sprite;
    }
}
