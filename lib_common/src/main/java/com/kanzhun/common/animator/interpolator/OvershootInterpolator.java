package com.kanzhun.common.animator.interpolator;

import android.view.animation.Interpolator;

/**
 * Created by Cha<PERSON>Jiangpeng
 * Date: 2022/4/2
 * UIKitSpring 动画
 */
public class OvershootInterpolator implements Interpolator {

    //弹行因数
    private float tension;

    public OvershootInterpolator(float tension) {
        this.tension = tension;
    }

    @Override
    public float getInterpolation(float input) {
        input -= 1;
        return input * input * ((tension + 1) * input + tension) + 1.0f;
    }
}
