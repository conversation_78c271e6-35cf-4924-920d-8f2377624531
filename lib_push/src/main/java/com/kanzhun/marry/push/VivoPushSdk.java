package com.kanzhun.marry.push;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.utils.L;
import com.vivo.push.IPushActionListener;
import com.vivo.push.PushClient;
import com.vivo.push.PushConfig;
import com.vivo.push.listener.IPushQueryActionListener;
import com.vivo.push.util.VivoPushException;

public class VivoPushSdk implements IPushSdk {
    Context application;

    public VivoPushSdk(Application context) {
        this.application = context;
    }

    @Override
    public void start() {
        try {
            PushConfig config = new PushConfig.Builder()
                    .agreePrivacyStatement(true)
                    .build();
            PushClient.getInstance(this.application).initialize(config);
            PushClient.getInstance(this.application).turnOnPush(new IPushActionListener() {
                @Override
                public void onStateChanged(int state) {
                    L.e("push","state:"+state);
                    if (state != 0) {//失败 101为不支持
                        if(state == 101){
    //                        PushSdkManager.getInstance().canNotSupportSystemNotification(PushSdkManager.PUSH_TYPE_VIVO);
    //                        PushSdkManager.getInstance().retry();
                        }
                    } else {
                        PushClient.getInstance(application).getRegId(new IPushQueryActionListener() {
                            @Override
                            public void onSuccess(String s) {
                                if (TextUtils.isEmpty(s)) {
                                } else {
                                    PushSdkManager.getInstance().registerToken(s);
                                }
                            }

                            @Override
                            public void onFail(Integer integer) {

                            }
                        });

                    }
                }
            });
        } catch (VivoPushException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop(String token) {
        if (this.application != null) {
            PushClient.getInstance(this.application).turnOffPush(new IPushActionListener() {
                @Override
                public void onStateChanged(int i) {

                }
            });
        }
    }

    @Override
    public void setForeground(boolean isForeground) {

    }

    @Override
    public void clearPushSdkNotification() {
    }

    @Override
    public void firstActivityShow(Activity activity) {

    }

    @Override
    public void requestPermission() {

    }
}
