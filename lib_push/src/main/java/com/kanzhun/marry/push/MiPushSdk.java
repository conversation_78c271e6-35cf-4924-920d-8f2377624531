package com.kanzhun.marry.push;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import com.xiaomi.mipush.sdk.MiPushClient;
import com.kanzhun.foundation.utils.MetaUtil;

public class MiPushSdk implements IPushSdk {
    private Context context;

    public MiPushSdk(Application context) {
        this.context = context;
    }

    @Override
    public void start() {
        if (this.context != null) {
            String miAppid = MetaUtil.getMeta(context, "com.xiaomi.push.app_id");
            String miAppkey = MetaUtil.getMeta(context, "com.xiaomi.push.app_key");

            if (!TextUtils.isEmpty(miAppid) && !TextUtils.isEmpty(miAppkey)) {
                MiPushClient.registerPush(this.context, miAppid, miAppkey);
            }
        }
    }

    @Override
    public void stop(String token) {
        if (this.context != null) {
            MiPushClient.unregisterPush(context);
        }
    }

    @Override
    public void setForeground(boolean isForeground) {

    }

    @Override
    public void clearPushSdkNotification() {
        if (this.context != null) {
            MiPushClient.clearNotification(context);
        }
    }

    @Override
    public void firstActivityShow(Activity activity) {

    }

    @Override
    public void requestPermission() {

    }
}
