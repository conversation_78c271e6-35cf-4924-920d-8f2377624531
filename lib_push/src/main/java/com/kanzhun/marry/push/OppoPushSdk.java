package com.kanzhun.marry.push;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import com.heytap.msp.push.HeytapPushManager;
import com.heytap.msp.push.callback.ICallBackResultService;
import com.kanzhun.utils.L;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.foundation.utils.MetaUtil;

public class OppoPushSdk implements IPushSdk {
    public static final String TAG = "push";
    Context application;

    private String oppoAppSecret;
    private String oppoAppkey;

    public OppoPushSdk(Application context) {
        this.application = context;
        oppoAppkey = MetaUtil.getMeta(context, "com.oppo.push.app_key");
        oppoAppSecret = MetaUtil.getMeta(context, "com.oppo.push.app_secret");
        HeytapPushManager.init(context, true);
    }

    @Override
    public void start() {
        if (!TextUtils.isEmpty(oppoAppSecret) && !TextUtils.isEmpty(oppoAppkey)) {
            //appkey,appSecret
            HeytapPushManager.register(this.application, oppoAppkey, oppoAppSecret, new ICallBackResultService() {
                /**
                 * 注册的结果,如果注册成功,registerID就是客户端的唯一身份标识
                 *
                 * @param responseCode    接口执行结果码，0表示接口执行成功
                 * @param registerID      注册id/token
                 * @param packageName     如果当前执行注册的应用是常规应用，则通过packageName返回当前应用对应的包名
                 * @param miniPackageName 如果当前是快应用进行push registerID的注冊，则通过miniPackageName进行标识快应用包名
                 */
                @Override
                public void onRegister(int responseCode, String registerID, String packageName, String miniPackageName) {
                    TLog.info(TAG, "======IOppoSdk======i:" + responseCode + " s:" + registerID);
                    if (responseCode == 0) {
                        PushSdkManager.getInstance().registerToken(registerID);
                    }
                }

                @Override
                public void onUnRegister(int i, String s, String s1i) {
                    L.e(TAG,"onUnRegister:code=" + i );
                }

                @Override
                public void onGetPushStatus(int i, int i1) {
                    L.e(TAG,"onSetPushTime:code=" + i + ",i1=" + i1);
                }

                @Override
                public void onSetPushTime(int i, String s) {
                    L.e(TAG,"onSetPushTime:code=" + i + ",result=" + s);
                }

                @Override
                public void onGetNotificationStatus(int i, int i1) {
                    L.e(TAG,"onGetNotificationStatus:code=" + i + ",status=" + i1);
                }

                @Override
                public void onError(int i, String s, String s1, String s2) {
                    L.e(TAG,"onError:code=" + i + ",message=" + s);
                }
            });
        }
    }

    @Override
    public void stop(String token) {
        try {
            HeytapPushManager.unRegister();
        } catch (Exception e) {
        }
    }

    @Override
    public void setForeground(boolean isForeground) {

    }

    @Override
    public void clearPushSdkNotification() {
        try {
            HeytapPushManager.clearNotificationType();
        } catch (Exception e) {

        }
    }

    @Override
    public void firstActivityShow(Activity activity) {

    }

    @Override
    public void requestPermission() {
        HeytapPushManager.requestNotificationPermission();
    }
}
