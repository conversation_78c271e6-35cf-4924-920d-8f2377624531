package com.kanzhun.marry.push;

import android.content.Context;

import com.techwolf.lib.tlog.TLog;
import com.xiaomi.mipush.sdk.MiPushCommandMessage;
import com.xiaomi.mipush.sdk.MiPushMessage;
import com.xiaomi.mipush.sdk.PushMessageReceiver;

import java.util.List;


public class MiPushReceiver extends PushMessageReceiver {
    public static final String TAG = "push";
    public static int pushType = PushSdkConfig.miPassThroughType;

    /**
     * 接收客户端向服务器发送注册指令的结果
     *
     * @param context
     * @param miPushCommandMessage
     */
    @Override
    public void onReceiveRegisterResult(Context context, MiPushCommandMessage miPushCommandMessage) {
        super.onReceiveRegisterResult(context, miPushCommandMessage);
        TLog.info(TAG, "MiPushReceiver onReceiveRegisterResult:" + miPushCommandMessage.toString());
        List<String> list = miPushCommandMessage.getCommandArguments();

        if (list != null && list.size() > 0) {
            String token = list.get(0);
            TLog.info(TAG, "MiPushReceiver token:" + token);
            PushSdkManager.getInstance().registerToken(token);
        }
    }

    /**
     * 接收透传消息的回调方法
     *
     * @param context
     * @param miPushMessage
     */
    @Override
    public void onReceivePassThroughMessage(Context context, MiPushMessage miPushMessage) {
        super.onReceivePassThroughMessage(context, miPushMessage);
        // 暂无实现
        TLog.info(TAG, "MiPushReceiver onReceivePassThroughMessage:" + miPushMessage.toString());
        PushSdkManager.getInstance().onReceivePassThroughMessage(pushType,miPushMessage.getContent());
    }

    /**
     * 用户点击通知栏消息触发
     *
     * @param context
     * @param miPushMessage
     */
    @Override
    public void onNotificationMessageClicked(Context context, MiPushMessage miPushMessage) {
        super.onNotificationMessageClicked(context, miPushMessage);
        TLog.info(TAG, "MiPushReceiver onNotificationMessageClicked:" + miPushMessage.toString());
        if(miPushMessage != null && miPushMessage.getExtra() != null){
            PushUtil.Companion.handler(miPushMessage.getExtra());
        }
        PushSdkManager.getInstance().onNotificationMessageClicked(pushType,miPushMessage.getExtra());
    }

    /**
     * 接收通知栏消息的回调方法
     *
     * @param context
     * @param miPushMessage
     */
    @Override
    public void onNotificationMessageArrived(Context context, MiPushMessage miPushMessage) {
        super.onNotificationMessageArrived(context, miPushMessage);
        // 接收通知栏消息回调
        TLog.info(TAG, "MiPushReceiver onNotificationMessageArrived:" + miPushMessage.toString());
    }

    /**
     * 接收客户端处服务端发送指令的结果
     *
     * @param context
     * @param miPushCommandMessage
     */
    @Override
    public void onCommandResult(Context context, MiPushCommandMessage miPushCommandMessage) {
        super.onCommandResult(context, miPushCommandMessage);
        TLog.info(TAG, "MiPushReceiver onCommandResult:" + miPushCommandMessage.toString());
    }

}
