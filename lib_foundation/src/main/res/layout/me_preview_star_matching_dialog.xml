<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/common_black_60">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDialogContent"
        android:layout_width="match_parent"
        android:background="@mipmap/me_bg_character_dialog"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.coorchice.library.SuperTextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:paddingHorizontal="24dp"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:stv_corner="24dp"
                app:stv_shaderEnable="true"
                app:stv_shaderEndColor="#FFFCE8FF"
                app:stv_shaderMode="leftToRight"
                app:stv_shaderStartColor="#FFD6E4FF"
                app:stv_stroke_color="@color/common_color_191919"
                app:stv_stroke_width="2dp"
                tools:text="性格匹配度 99分"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/ivStar1"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="163dp"
                android:layout_marginTop="14dp"
                android:src="@drawable/me_preivew_chatacter_star"
                app:layout_constraintStart_toStartOf="@id/tvTitle"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.lihang.ShadowLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="34dp"
            android:paddingBottom="15dp"
            app:hl_cornerRadius="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible">

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/leftType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:text="金牛座"
                        android:textColor="@color/common_color_191919"
                        android:textSize="18dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/rightType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="20dp"
                        android:text="INTJ"
                        android:textColor="@color/common_color_191919"
                        android:textSize="18dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_marginTop="32dp"
                        android:layout_marginHorizontal="20dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/leftType"
                        tools:text="fdsafd\njklfdj\njfdklafj"
                        android:id="@+id/rcvResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_14"
                        android:orientation="vertical"
                        android:textStyle="bold"
                        />



                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>


        </com.lihang.ShadowLayout>


        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/leftAvatar"
            android:layout_width="78dp"
            android:layout_height="78dp"
            android:layout_marginTop="17dp"
            tools:background="@color/image_color_yellow"
            android:layout_marginEnd="60dp"
            app:common_circle="true"
            app:common_image_stroke_color="@color/common_white"
            app:common_image_stroke_width="4dp"
            app:common_src="@mipmap/common_default_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/rightAvatar"
            android:layout_width="78dp"
            tools:background="@color/image_color_blue"
            android:layout_height="78dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="17dp"
            app:common_circle="true"
            app:common_image_stroke_color="@color/common_white"
            app:common_image_stroke_width="4dp"
            app:common_src="@mipmap/common_default_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:src="@drawable/common_white_circle_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clDialogContent" />

</androidx.constraintlayout.widget.ConstraintLayout>