<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginVertical="15dp"
        android:scaleType="centerCrop"/>

    <ImageView
        android:id="@+id/iv_file"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_toEndOf="@id/iv_logo"
        android:layout_marginTop="20dp"/>

    <TextView
        android:id="@+id/tv_file"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="13sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_alignBottom="@+id/iv_file"
        android:layout_toEndOf="@id/iv_file"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="40dp"/>

    <ProgressBar
        android:id="@+id/pb_progress"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_below="@+id/iv_file"
        android:layout_alignStart="@id/iv_file"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:progressDrawable="@drawable/bg_notification_progress"/>


</RelativeLayout>