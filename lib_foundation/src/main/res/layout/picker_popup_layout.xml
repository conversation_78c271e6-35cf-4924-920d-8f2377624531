<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundColor"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="15dp"
        android:padding="10dp">

        <TextView
            android:id="@+id/text_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center"
            android:text="@android:string/cancel"
            android:textColor="@color/color_text"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/text_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center"
            android:text="@android:string/ok"
            android:textColor="@color/blue"
            android:textSize="15sp"
            android:textStyle="bold" />
    </FrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_grey2" />

    <LinearLayout
        android:id="@+id/popup_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="5dp" />
</LinearLayout>