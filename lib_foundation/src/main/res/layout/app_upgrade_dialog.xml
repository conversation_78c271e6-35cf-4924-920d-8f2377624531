<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idRoot0"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        tools:layout_marginHorizontal="20dp"
        android:background="@drawable/common_bg_corner_20_color_white"
        android:padding="24dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textColor="@color/common_black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="发现新版本" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:lineSpacingExtra="4dp"
            android:textColor="@color/common_color_4C4C4C"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintEnd_toEndOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            tools:text="V1.0" />


        <TextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:lineSpacingExtra="4dp"
            android:gravity="start"
            android:textColor="@color/common_color_4C4C4C"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_version"
            tools:text="1、修复了一堆bug\n2、增加了海量功能\n3、杀了一批程序员祭天\n4、首次注册送对象" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/login_upgrade_now"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_content"
            app:qmui_backgroundColor="@color/common_color_app_theme_button"
            app:qmui_radius="25dp" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/login_upgrade_later"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_ok"
            app:layout_constraintTop_toBottomOf="@+id/btn_ok"
            app:qmui_borderColor="@color/common_color_EBEBEB"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="25dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idRoot1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="gone"
        >
        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.6" />


        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/iv_image"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginHorizontal="30dp"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:actualImageScaleType="fitBottomStart"
            app:layout_constraintBottom_toBottomOf="@+id/guideline"
            />

        <ImageView
            android:layout_marginTop="30dp"
            android:id="@+id/idIVClose"
            app:layout_constraintTop_toBottomOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@mipmap/image_white_close_r"
            android:layout_width="24dp"
            android:layout_height="24dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
