<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,20m-20,0a20,20 0,1 1,40 0a20,20 0,1 1,-40 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="40"
          android:startY="7.248"
          android:endX="-0"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFE3E2"/>
        <item android:offset="1" android:color="#FFFDFDEB"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M20,20m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#191919"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M13.715,26.285L27.071,12.929"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#191919"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
</vector>
