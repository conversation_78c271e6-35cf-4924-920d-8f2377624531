package com.kanzhun.common.base.compose.ui

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.common.R
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.theme.O2Theme

@Composable
fun O2Toolbar(
    @DrawableRes iconLeft: Int = R.drawable.common_ic_black_back,
    title: String = "",
    onBackClick: () -> Unit = {},
    @DrawableRes iconRight: Int = R.drawable.common_ic_icon_chat_setting,
    onMoreClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    titleColor: Color = colorResource(R.color.common_color_191919),
) {
    Box(modifier = modifier) {
        Row(modifier = Modifier.height(44.dp), verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(iconLeft),
                modifier = Modifier
                    .padding(start = 20.dp, end = 5.dp)
                    .size(24.dp)
                    .noRippleClickable { onBackClick() },
                contentDescription = ""
            )

            Text(
                text = title,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 20.dp, end = 69.dp),
                color = titleColor,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis
            )
        }

        if (onMoreClick != null) {
            Image(
                painter = painterResource(iconRight),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .size(24.dp)
                    .align(Alignment.CenterEnd)
                    .noRippleClickable { onMoreClick() },
                contentDescription = ""
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewToolbar() {
    O2Theme {
        O2Toolbar(title = "标题标题标题标题标题标题标题标题标题标题")
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewToolbarMore() {
    O2Theme {
        O2Toolbar(title = "标题", onMoreClick = {})
    }
}