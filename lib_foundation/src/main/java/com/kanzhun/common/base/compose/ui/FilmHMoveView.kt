package com.kanzhun.common.base.compose.ui

import android.annotation.SuppressLint
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.MutatePriority
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.foundation.R
import com.kanzhun.foundation.bean.FilmWall
import com.kanzhun.foundation.bean.StubFilmWall

@SuppressLint("UnrememberedMutableState")
@Composable
@Preview
fun PreviewFlushedFilm() {
    FlushedFilm(
        filmWallList = mutableStateListOf<FilmWall>().apply {
            addAll(
                mockFilmWallList().take(9)
            )
        },
    ){

    }
}

fun mockFilmWallList(): List<FilmWall> {
    return mutableListOf<FilmWall>().apply {
        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试1",
//                    img = "http://gips0.baidu.com/it/u=1690853528,2506870245&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
                id = "1"
            )
        )

        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试2",
//                    img = "http://gips2.baidu.com/it/u=195724436,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960",
                id = "2"
            )
        )

        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试3",
//                    img = "http://gips1.baidu.com/it/u=3874647369,3220417986&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
                id = "3"
            )
        )

        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试4",
//                    img = "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                id = "4"
            )
        )

        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试5",
//                    img = "http://gips2.baidu.com/it/u=295419831,2920259701&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
                id = "5"
            )
        )

        add(
            FilmWall(
                nickName = "测试用户",
                content = "测试6",
//                    img = "https://inews.gtimg.com/om_bt/OQDTT-S6-2ldIPahKpMgoT6-UuztR0kwGC7wEt1abCEvIAA/1000",
                id = "6"
            )
        )

        for (i in 7..18) {
            add(
                FilmWall(
                    nickName = "测试用户",
                    content = "测试$i",
//                        img = "http://gips0.baidu.com/it/u=1690853528,2506870245&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
                    id = "$i"
                )
            )
        }
    }
}

@Composable
fun FlushedFilm(
    modifier: Modifier = Modifier,
    filmWallList: SnapshotStateList<FilmWall> = mutableStateListOf(),
    onClickFilmWallItem: (FilmWall?) -> Unit = {}
) {
    if (filmWallList.isNotEmpty()) {
        ConstraintLayout(modifier = modifier) {
            val (firstLine, secondLine) = createRefs()

            /*
                How to create a circular (Endless) Lazycolumn/LazyRow in Compose?

                https://stackoverflow.com/a/68805604/24070075
             */

            // 单行
            // 冲洗次数1-7张
            // 从左向右循环滚动
            ConstraintLayout(
                modifier = Modifier
                    .constrainAs(firstLine) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top)
                        end.linkTo(parent.end)
                    }
                    .clipToBounds()
            ) {
                val (kodak, film, wall, leftCover, rightCover) = createRefs()

                Image(
                    painter = painterResource(R.drawable.activity_ff_film_right),
                    contentDescription = null,
                    modifier = Modifier
                        .width(419.dp)
                        .height(132.dp)
                        .constrainAs(film) {
                            start.linkTo(parent.start, margin = 42.dp)
                            top.linkTo(kodak.top, margin = 6.dp)
                            bottom.linkTo(kodak.bottom)
                        }
                )

                val lazyListState =
                    rememberLazyListState(initialFirstVisibleItemIndex = 4) // 初始化 4，和第二行可以衔接起来

                LaunchedEffect(Unit) {
                    while (true) {
                        lazyListState.autoScroll()
                    }
                }

                LazyRow(
                    modifier = Modifier
                        .height(100.dp)
                        .padding(8.dp)
                        .constrainAs(wall) {
                            start.linkTo(film.start, margin = 16.dp)
                            top.linkTo(film.top)
                            bottom.linkTo(film.bottom)
                            end.linkTo(film.end, margin = 16.dp)

                            width = Dimension.fillToConstraints
                        },
                    state = lazyListState,
                    reverseLayout = true,
                    userScrollEnabled = false,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(Int.MAX_VALUE) { seq ->
                        val filmWall = filmWallList[seq % filmWallList.size]
                        FilmWallItem(filmWall = filmWall, onClickFilmWallItem = onClickFilmWallItem)
                    }
                }

                Image(
                    painter = painterResource(R.drawable.activity_ff_kodak),
                    contentDescription = null,
                    modifier = Modifier
                        .width(86.dp)
                        .height(189.dp)
                        .constrainAs(kodak) {
                            start.linkTo(parent.start, margin = (-36).dp)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                        }
                )

                Image(
                    painter = painterResource(R.drawable.activity_ff_film_left_cover_first_left),
                    contentDescription = null,
                    modifier = Modifier
                        .width(86.dp)
                        .height(189.dp)
                        .constrainAs(leftCover) {
                            start.linkTo(parent.start, margin = (-3).dp)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                        }
                )

                Image(
                    painter = painterResource(R.drawable.activity_ff_film_left_cover_first_right),
                    contentDescription = null,
                    modifier = Modifier
                        .width(69.dp)
                        .height(170.dp)
                        .constrainAs(rightCover) {
                            end.linkTo(parent.end, margin = (-5).dp)
                            top.linkTo(parent.top)
                            bottom.linkTo(parent.bottom)
                        }
                )
            }

            // 双行
            // 冲洗次>8张
            // 第一行从左向右，第二行从右向左循环滚动
            if (filmWallList.filter { it != StubFilmWall }.size >= 8) {
                ConstraintLayout(Modifier.constrainAs(secondLine) {
                    start.linkTo(parent.start)
                    top.linkTo(firstLine.bottom, margin = (-24).dp)
                    end.linkTo(parent.end)
                }) {
                    val (film, wall, leftCover, rightCover) = createRefs()

                    Image(
                        painter = painterResource(R.drawable.activity_ff_film_left),
                        contentDescription = null,
                        modifier = Modifier
                            .width(419.dp)
                            .height(132.dp)
                            .constrainAs(film) {
                                start.linkTo(parent.start, margin = 20.dp)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )

                    val lazyListState = rememberLazyListState()

                    LaunchedEffect(Unit) {
                        while (true) {
                            lazyListState.autoScroll()
                        }
                    }

                    LazyRow(
                        modifier = Modifier
                            .height(100.dp)
                            .padding(8.dp)
                            .constrainAs(wall) {
                                start.linkTo(film.start, margin = 24.dp)
                                top.linkTo(film.top)
                                bottom.linkTo(film.bottom)
                                end.linkTo(film.end)

                                width = Dimension.fillToConstraints
                            },
                        state = lazyListState,
                        userScrollEnabled = false,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(Int.MAX_VALUE) { seq ->
                            val filmWall = filmWallList[seq % filmWallList.size]
                            FilmWallItem(filmWall, onClickFilmWallItem = onClickFilmWallItem)
                        }
                    }

                    Image(
                        painter = painterResource(R.drawable.activity_ff_film_left_cover_first_left_2),
                        contentDescription = null,
                        modifier = Modifier
                            .width(77.dp)
                            .height(110.dp)
                            .constrainAs(leftCover) {
                                start.linkTo(film.start, margin = 16.dp)
                                top.linkTo(film.top)
                                bottom.linkTo(film.bottom)
                            }
                    )

                    Image(
                        painter = painterResource(R.drawable.activity_ff_film_left_cover_first_right),
                        contentDescription = null,
                        modifier = Modifier
                            .width(69.dp)
                            .height(170.dp)
                            .constrainAs(rightCover) {
                                end.linkTo(parent.end, margin = (-5).dp)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                }
            }
        }
    }
}

@Composable
private fun FilmWallItem(
    filmWall: FilmWall,
    onClickFilmWallItem: (filmWall: FilmWall?) -> Unit = {}
) {
    val imageUrl = filmWall.img
    val content = filmWall.content
    if (!imageUrl.isNullOrEmpty()) {
        // 图片胶片
        AsyncImage(
            model = imageUrl,
            contentDescription = null,
            modifier = Modifier
                .aspectRatio(1f)
                .noRippleClickable { onClickFilmWallItem(filmWall) },
            contentScale = ContentScale.Crop,
            placeholder = painterResource(R.drawable.activity_ff_ic_placeholder),
            error = painterResource(R.drawable.activity_ff_ic_placeholder)
        )
    } else if (!content.isNullOrEmpty()) {
        // 文字胶片
        Box(
            modifier = Modifier
                .aspectRatio(1f)
                .paint(
                    painter = painterResource(R.drawable.activity_ff_ic_text_frame),
                    contentScale = ContentScale.FillBounds
                )
                .noRippleClickable { onClickFilmWallItem(filmWall) },
        ) {
            Image(
                painter = painterResource(R.drawable.activity_ff_ic_text_bg),
                contentDescription = null,
                modifier = Modifier.align(alignment = Alignment.BottomEnd)
            )

            Text(
                text = content.take(4),
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(500),
                    color = colorResource(R.color.common_color_FFE6BE)
                ),
                modifier = Modifier.align(Alignment.Center)
            )
        }
    } else {
        // 占位
        Box(
            modifier = Modifier
                .aspectRatio(1f)
                .paint(
                    painter = painterResource(R.drawable.activity_ff_ic_placeholder),
                    contentScale = ContentScale.FillBounds
                )
                .noRippleClickable { onClickFilmWallItem(null) },
        )
    }
}

private suspend fun ScrollableState.autoScroll(
    animationSpec: AnimationSpec<Float> = tween(durationMillis = 10, easing = LinearEasing),
    reversed: Boolean = false
) {
    var previousValue = 0f
    scroll(MutatePriority.UserInput) {
        animate(0f, 1f, animationSpec = animationSpec) { currentValue, _ ->
            previousValue += scrollBy((if (reversed) -1f else 1f) * (currentValue - previousValue))
        }
    }
}