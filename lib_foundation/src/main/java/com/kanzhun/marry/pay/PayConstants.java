package com.kanzhun.marry.pay;

public class PayConstants {
    public static final String PREFIX = "PayConstants";

    /*
     * APP应用内支付返回结果
     */
    /******三方支付平台返回的结果code******/
    /*三方支付平台返回的结果Code*/
    public static final String APP_PAY_CODE = PREFIX + ".APP_PAY_CODE";
    /*三方支付平台返回的结果描述*/
    public static final String APP_PAY_ERR_MSG = PREFIX + ".APP_PAY_ERR_MSG";

    /******APP内自定义支付平台******/
    public static final String APP_PAY_PLATFORM = PREFIX + ".APP_PAY_PLATFORM";
    /*微信*/
    public static final int APP_PAY_PLATFORM_WX = 1;
    /*支付宝*/
    public static final int APP_PAY_PLATFORM_ZFB = 2;

    /******APP内自定义支付结果******/
    public static final String APP_PAY_RESULT = PREFIX + ".APP_PAY_RESULT";
    /*支付成功*/
    public static final int APP_PAY_RESULT_SUCCEED = 0;
    /*支付失败*/
    public static final int APP_PAY_RESULT_FAILED = -1;
    /*用户取消*/
    public static final int APP_PAY_RESULT_USER_CANCEL = -2;
    /*未安装*/
    public static final int APP_PAY_NO_INSTALLED= -3;
    /*参数错误*/
    public static final int APP_PAY_PARAM_ERROR = -4;

    /**
     * 支付结果广播ACTION
     */
    public static final String RECEIVER_WX_PAY_RESULT_ACTION = PREFIX+".RECEIVER_WX_PAY_RESULT_ACTION";

}
