package com.kanzhun.foundation.bean

import java.io.Serializable

data class LikeEachOtherListBean(
    val likeEachOtherList: List<LikeEachOtherListItemBean?>?,
    val replyLikeList: List<LikeEachOtherListItemBean?>?
):Serializable

data class LikeEachOtherListItemBean(
    val boyAvatar: String?,
    val boyNickname: String?,
    val friendId: String?,
    val girlAvatar: String?,
    val girlNickname: String?
):Serializable
