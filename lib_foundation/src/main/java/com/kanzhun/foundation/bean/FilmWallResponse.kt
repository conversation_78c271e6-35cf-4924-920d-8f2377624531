package com.kanzhun.foundation.bean

import java.io.Serializable

/**
 * https://api.weizhipin.com/project/1975/interface/api/725107
 *
 * {
 *         "canShow":true, // 是否展示
 *         "filmwall": [{
 *             "content": "今天出去玩了", // 胶片文案
 *             "img": "url", // 胶片图片
 *             "nickName": "用户昵称",
 *             "id":"xxx",// 胶片冲洗id
 *         }]
 *     }
 */
data class FilmWallResponse(
    var canShow: Boolean? = false,
    var filmWallList: MutableList<FilmWall>? = null
) : Serializable

data class FilmWall(
    var content: String? = "",
    var img: String? = "",
    var nickName: String? = "",
    var id: String? = ""
) : Serializable

val StubFilmWall = FilmWall()