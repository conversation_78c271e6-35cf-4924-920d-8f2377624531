package com.kanzhun.foundation.api.response

import com.kanzhun.foundation.model.profile.Button
import java.io.Serializable

data class GetUserTagResponse(
    var idealPartnerTags: List<GetUserTagResponseItem>? = null,
    val idealPartnerDescInfo:String? = null,//拒绝原因
    val idealPartnerDesc:String? = null,//理想型内容
    val idealPartnerDescStatus:Int = 0,//理想型状态 1:待审核 2:已驳回 3:通过
): Serializable

data class GetUserTagResponseItem(
    var id: Long? = null,
    var content: String? = null,
    var icon: String? = null,
    var createTime: Long? = null,
    var updateTime: Long? = null,
): Serializable

data class CompleteGuideRecommendInfoResponse(
    val button: Button?,
    val levelOne: LevelOne?,
    val levelThree: LevelThree?,
    val levelTwo: LevelTwo?,
    val smallPop: SmallPop?
):Serializable


data class LevelOne(
    val avatarList: List<String>?,
    val title: String?,
    val score: Int,
    val userCount: Int,
    val recommendTag: RecommendTag?
):Serializable

data class LevelThree(
    val content: String?,
    val tagList: List<LevelThreeTag>?,
    val title: String?
):Serializable

data class LevelTwo(
    val title: String?,
    val content: String?,
    val certTypeDesc: String?,
    val avatarList: List<String>?,
    val degreeList: List<String>?,
):Serializable

data class SmallPop(
    val content: String?,
    val title: String?
):Serializable

data class RecommendTag(
    val tag: String?,
    val content: String?
):Serializable

data class LevelThreeTag(
    val content: String?,
    val icon: String?,
    val title: String?
):Serializable