package com.kanzhun.foundation.api

import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.base.URLConfig.URL_COMPLETE_GUIDE_RECOMMEND_INFO
import com.kanzhun.foundation.api.base.URLConfig.URL_MEETUP_PLAN_FINISH_DIALOG
import com.kanzhun.foundation.api.base.URLConfig.URL_USER_GET_IDEAL_PARTNER
import com.kanzhun.foundation.api.response.CompleteGuideRecommendInfoResponse
import com.kanzhun.foundation.api.response.GetUserTagResponse
import com.kanzhun.foundation.api.response.MeetupPlanFinishDialogResponse
import com.kanzhun.foundation.api.response.RecommendTagContentGenerateResponse
import com.kanzhun.http.response.BaseResponse
import io.reactivex.rxjava3.core.Observable
import retrofit2.http.GET
import retrofit2.http.Query

interface FoundationApiK {

    @GET(URL_USER_GET_IDEAL_PARTNER)
    fun getUserTagUserTag(@Query("scene") scene: String): Observable<BaseResponse<GetUserTagResponse>>

    @GET(URL_MEETUP_PLAN_FINISH_DIALOG)
    fun getMeetupPlanFinishDialog(): Observable<BaseResponse<MeetupPlanFinishDialogResponse>>

    @GET(URL_COMPLETE_GUIDE_RECOMMEND_INFO)
    fun getCompleteGuideRecommendInfo(@Query("type") scene: String): Observable<BaseResponse<CompleteGuideRecommendInfoResponse>>

    @GET(URLConfig.URL_RECOMMEND_TAG_CONTENT_GENERATE)
    fun getRecommendTagContentGenerate(
        @Query("tagIds") tagIds: String, // 标签id
        @Query("scene") scene: Int // 场景 0:我的标签 1:爱好 2:理想型
    ): Observable<BaseResponse<RecommendTagContentGenerateResponse>>
}