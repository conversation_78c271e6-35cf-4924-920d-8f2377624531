package com.kanzhun.foundation.api.model;

import com.kanzhun.common.base.PageSource;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date:2022-05-12
 * Description:  发送喜欢页面的Model
 */
public class SendLikeModel implements Serializable {

    private static final long serialVersionUID = -320225923244999273L;

    public String userId;

    public String userName;

    public String avatar;

    //资源Id
    public String resId;

    //10-形象照, 50-用户故事, 61-语音问答, 62-文字问答, 70-AB面
    public int resourceType;

    // 资源对象  和ResourceType对应 ，10-形象照, 50-用户故事, 61-语音问答, 62-文字问答, 70-AB面
    public Object resourceObject;

    //AB面时必填，1-A面图片；2-B面图片；其他为空
    public int subType;

    public PageSource pageSource;

    public int relationStatus;

    public String encMoodId;
    public String securityId;

    public List<String> chatWordList;
    public boolean autoLike;
    public boolean useAi;

    public SendLikeModel(String userId, String userName, String avatar, PageSource pageSource, List<String> chatWordList, int relationStatus, String encMoodId,String securityId,boolean autoLike,boolean useAi) {
        this.userId = userId;
        this.userName = userName;
        this.avatar = avatar;
        this.pageSource = pageSource;
        this.chatWordList = chatWordList;
        this.relationStatus = relationStatus;
        this.encMoodId = encMoodId;
        this.securityId = securityId;
        this.autoLike = autoLike;
        this.useAi = useAi;
    }

}
