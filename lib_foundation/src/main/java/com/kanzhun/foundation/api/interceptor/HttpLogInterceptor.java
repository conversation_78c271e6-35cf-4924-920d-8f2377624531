package com.kanzhun.foundation.api.interceptor;

import android.text.TextUtils;

import com.techwolf.lib.tlog.BuildConfig;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.foundation.api.base.HostConfig;
import com.kanzhun.utils.NetworkUtils;
import com.kanzhun.utils.platform.Utils;

import java.io.EOFException;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import okhttp3.Connection;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okio.Buffer;
import okio.BufferedSource;


/**
 * Created by wang<PERSON> on 2017/7/19.
 */

public class HttpLogInterceptor implements Interceptor {
    private static final Charset UTF8 = Charset.forName("UTF-8");
    public static final String TAG = "bapi";

    private static boolean isSpecialTag(String url) {
        Set<String> urlSet = new HashSet<>();
        urlSet.add("collector");
//        urlSet.add("recommendExpect");
        for (String u : urlSet) {
            if (url.contains(u)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 对于有些网络请求，数据量太大不需要保存
     **/
    public static boolean discardRequestLog(String url) {
//        if(url == null){
//            return false;
//        }
//        if(url.contains(URLConfig.URL_LOG_UPLOAD)){ //日志上传
//            return true;
//        }
//        if(url.contains(URLConfig.URL_LOG_COLLECTOR)){//f1曝光统计
//            return true;
//        }
//        return false;
        return true;
    }

    public interface Log {
        void log(String url, String message);

        Log DEFAULT = new Log() {

            @Override
            public void log(String url, String message) {
                // TODO: 2022/2/28  
//                if (discardRequestLog(url)) {//是否需要存储文件
//                    TLog.error(TAG, "%s", message);
//                } else {
//                    TLog.info(TAG, "%s", message);
//                }
//                if (isSpecialTag(url)) {
//                    TLog.error("fuck", "%s", message);
//                }
            }
        };
    }

    public HttpLogInterceptor() {
        this(Log.DEFAULT);
    }

    public HttpLogInterceptor(Log logger) {
        this.logger = logger;
    }

    private final Log logger;

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response;
        long startNs = 0;

        String url = request.url().toString();

//        if(filterRequest(request.url())){
        boolean isMultipart = false;
        RequestBody requestBody = request.body();
        boolean hasRequestBody = requestBody != null;
        Connection connection = chain.connection();
        Protocol protocol = connection != null ? connection.protocol() : Protocol.HTTP_1_1;
        logger.log(url, "\n");
        String requestUrl = request.url().toString().replaceAll("%(?![0-9a-fA-F]{2})", "%25");
        String requestStartMessage = "request--> " + request.method() + ' ' + URLDecoder.decode(requestUrl, "utf-8") + ' ' + protocol;
        if (hasRequestBody) {
            requestStartMessage += " (" + requestBody.contentLength() + "-byte body)";
        }

        logger.log(url, requestStartMessage);
        if (hasRequestBody) {
            // Request body headers are only present when installed as a network interceptor. Force
            // them to be included (when available) so there values are known.
            if (requestBody.contentType() != null) {
                if (requestBody.contentType().toString().toLowerCase().contains("multipart")) {
                    isMultipart = true;
                }
                logger.log(url, "Content-Type: " + requestBody.contentType());
            }
            if (requestBody.contentLength() != -1) {
                logger.log(url, "Content-Length: " + requestBody.contentLength());
            }
        }
        Headers requestHeaders = request.headers();
        if (BuildConfig.DEBUG) {//debug 模式下才会打印返回头
            for (int i = 0, count = requestHeaders.size(); i < count; i++) {
                String name = requestHeaders.name(i);
                // Skip headers from the request body as they are explicitly logged above.
                if (!"Content-Type".equalsIgnoreCase(name) && !"Content-Length".equalsIgnoreCase(name)) {
                    logger.log(url, name + ": " + requestHeaders.value(i));
                }
            }
        }
        if (!hasRequestBody) {
            logger.log(url, "request--> END " + request.method());
        } else if (bodyEncoded(requestHeaders)) {
            logger.log(url, "request--> END " + request.method() + " (encoded body omitted)");
        } else {
            try {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);

                Charset charset = UTF8;
                MediaType contentType = requestBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                logger.log(url, "");
                if (isPlaintext(buffer)) {
                    if (!isMultipart) {
                        logger.log(url, buffer.readString(charset));
                    }
                    logger.log(url, "request--> END " + request.method()
                            + " (" + requestBody.contentLength() + "-byte body)");
                } else {
                    logger.log(url, "request--> END " + request.method() + " (binary "
                            + requestBody.contentLength() + "-byte body omitted)");
                }
            } catch (OutOfMemoryError outOfMemoryError) {
                TLog.error(TAG, "outOfMemoryError url : %s ", url);
            } catch (Throwable throwable) {
                TLog.error(TAG, "intercept throwable : %s, url : %s", throwable.toString(), url);
            }
        }
        logger.log(url, "\n");
        startNs = System.nanoTime();
//        }else{
//            L.info(TAG,"%s is filter",request.url());
//        }

        try {
            response = chain.proceed(request);
        } catch (Exception e) {
            dealNetError();
            logger.log(url, "response<-- HTTP FAILED: " + e);
            throw e;
        }

//        if(filterRequest(request.url())){
        long tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs) / 1000;
        ResponseBody responseBody = response.body();
        long contentLength = responseBody.contentLength();
        String bodySize = contentLength != -1 ? contentLength + "-byte" : "unknown-length";


        logger.log(url, "response<-- " + response.code() + ' ' + response.message() + ' '
                + URLDecoder.decode(requestUrl, "utf-8") + " (" + tookMs + "ms" + ", "
                + bodySize + " body" + ')');

        Headers resopnseHeaders = response.headers();
        for (int i = 0, count = resopnseHeaders.size(); i < count; i++) {
            logger.log(url, resopnseHeaders.name(i) + ": " + resopnseHeaders.value(i));
        }

        if (!HttpHeaders.hasBody(response)) {
            logger.log(url, "response<-- END HTTP");
        } else if (bodyEncoded(resopnseHeaders)) {
            logger.log(url, "response<-- END HTTP (encoded body omitted)");
        } else {
            Charset charset = UTF8;
            MediaType contentType = responseBody.contentType();
            if (contentType != null) {
                charset = contentType.charset(UTF8);
            }
            if (response.code() != 200) {
                logger.log(url, "error code:" + response.code());
            }

            BufferedSource source = responseBody.source();
            source.request(64);
            Buffer buffer1 = source.getBuffer();
            boolean plaintext = isPlaintext(buffer1);
            if (!plaintext) {
                logger.log(url, "");
                logger.log(url, "response<-- END HTTP (binary " + buffer1.size() + "-byte body omitted)");
                return response;
            }
            source.request(Long.MAX_VALUE); // Buffer the entire body.
            Buffer buffer = source.getBuffer();
            if (contentLength != 0 && contentType != null && "json".equals(contentType.subtype())) {
                logger.log(url, "");
                logger.log(url, buffer.clone().readString(charset));
            }

            logger.log(url, "response<-- END HTTP (" + buffer.size() + "-byte body)");
        }
//        }
        return response;
    }

    /**
     * Returns true if the body in question probably contains human readable text. Uses a small sample
     * of code points to detect unicode control characters commonly used in binary file signatures.
     */
    static boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

    private boolean bodyEncoded(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return contentEncoding != null && !contentEncoding.equalsIgnoreCase("identity");
    }

    private void dealNetError() {
        if (NetworkUtils.hasNetwork(Utils.getApp())) {
            String host = HostConfig.getCONFIG().getApiAddr();
            if (TextUtils.isEmpty(host)) {
                return;
            }
            try {
                String ipAddress = NetworkUtils.GetInetAddress(host);
                TLog.info("ip_parse", ipAddress);
            } catch (Exception e) {

            }
        }
    }
}
