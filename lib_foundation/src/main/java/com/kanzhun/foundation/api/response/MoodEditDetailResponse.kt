package com.kanzhun.foundation.api.response

import com.kanzhun.utils.GsonUtils
import java.io.Serializable

data class MoodEditDetailResponse(
    val encMoodId: String?,//加密的心情Id
    val encUserId: String?,
    val moodItemId: String?,
    val securityId: String?,
    val nickName: String?,
    val avatar: String?,
    val tinyAvatar: String?,
    val moodIcon: String?,
    val moodTitle: String?,
    val moodContent: String?,
    val permission: Int?,
    val createTime: Long?,
    val updateTime: Long?,
    val expireTime: Long?,
    val status: Int?,//状态：0-失效，1-审核中，2-驳回，3-正常（通过）
    val rejectReason: String?,//异步机审驳回的时候，会返回该字段
    val thumbList: List<MoodThumbBean?>?,
) : Serializable

data class MoodThumbBean(
    val userId: String?,
    val nickName: String?,
    val avatar: String?,
    val tinyAvatar: String?,
    val createTime: String?,
    val securityId: String?,
) : Serializable


@Retention(AnnotationRetention.SOURCE)
annotation class MoodPermissionType {
    companion object {
        const val PUBLIC = 1 //1-公开，2-互相喜欢的人可见
        const val PRIVATE = 2//1-公开，2-互相喜欢的人可见
    }
}


fun mock(): MoodEditDetailResponse {
    val json = "{\n" +
            "    \"encMoodId\": \"OAuKs_wOXfLHTxM_Br-W8M6Ruu_U2C3zQynC_SFewkY~\",\n" +
            "    \"encUserId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "    \"nickName\": \"盖伦\",\n" +
            "    \"avatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~.jpg\",\n" +
            "    \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "    \"moodItemId\": \"x72Fq3MBk-otYVMpkWVEmPuajXL_z_gsSZHpF_U2o8E~\",\n" +
            "    \"moodIcon\": \"https://lengjing-cdn.zhipin.com/system/public/P2b-9c9HFUa-CeiwdnzhCW-0TNdFv9OmFo9X-bNB9On6uOeNnzmo7tI1jvUYFmng_B44mHH8cecij69wO9J4-w~~.png\",\n" +
            "    \"moodTitle\": \"焦虑\",\n" +
            "    \"moodContent\": \"记得记得记得记得记得记得就觉得\",\n" +
            "    \"permission\": 1,\n" +
            "    \"createTime\": 1719235793000,\n" +
            "    \"updateTime\": 1719235804000,\n" +
            "    \"expireTime\": 1719322193000,\n" +
            "    \"thumbList\": [\n" +
            "        {\n" +
            "            \"userId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "            \"nickName\": \"小明小明\",\n" +
            "            \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "            \"createTime\": \"06月25日0点\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"userId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "            \"nickName\": \"小明小明\",\n" +
            "            \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "            \"createTime\": \"06月25日0点\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"userId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "            \"nickName\": \"小明小明\",\n" +
            "            \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "            \"createTime\": \"06月25日0点\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"userId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "            \"nickName\": \"小明小明\",\n" +
            "            \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "            \"createTime\": \"06月25日0点\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"userId\": \"K_O7FdkwJjwh3ajbC2lCogPvARZgiZ7yzXrK_2glkBY~\",\n" +
            "            \"nickName\": \"小明小明\",\n" +
            "            \"tinyAvatar\": \"https://lengjing-cdn.zhipin.com/azeroth/avatar/240604/Fs64GMX1G2ywYSMx0hIuFSzVjI9A4Q7cbNj4SX-rkYKLsovjUOBwd-jv21KLPXXpSzvGxZj0SsgO-fGzmPiAx24W6xtFdM8CRoSfcsJZ5lU~_s2772.jpg\",\n" +
            "            \"createTime\": \"06月25日0点\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"startChat\": false,\n" +
            "    \"thumbStatus\": 0\n" +
            "}"
    return GsonUtils.fromJson(json, MoodEditDetailResponse::class.java)
}
