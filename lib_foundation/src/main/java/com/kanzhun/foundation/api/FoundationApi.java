package com.kanzhun.foundation.api;

import android.util.ArrayMap;

import com.google.gson.JsonObject;
import com.kanzhun.foundation.api.base.MediaTypeConstant;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.bean.AcceptBindResp;
import com.kanzhun.foundation.api.bean.F1DialogBean;
import com.kanzhun.foundation.api.bean.HideTaskFIndBean;
import com.kanzhun.foundation.api.bean.HomePopBean;
import com.kanzhun.foundation.api.bean.InviteBindResp;
import com.kanzhun.foundation.api.bean.QueryVersionBean;
import com.kanzhun.foundation.api.bean.ThumbAddBean;
import com.kanzhun.foundation.api.model.ABFace;
import com.kanzhun.foundation.api.model.CommonAddressResponse;
import com.kanzhun.foundation.api.model.CommonAddressResponseK;
import com.kanzhun.foundation.api.model.ContactResponse;
import com.kanzhun.foundation.api.model.ConversationTabSummaryInfo;
import com.kanzhun.foundation.api.model.InteractTabSummary;
import com.kanzhun.foundation.api.model.LimitedActivityTaskExpoBean;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.api.model.NoticeListResponse;
import com.kanzhun.foundation.api.model.ProfileStatusModel;
import com.kanzhun.foundation.api.model.SearchSchoolResponse;
import com.kanzhun.foundation.api.model.SecurityNoticeResponse;
import com.kanzhun.foundation.api.model.SocialTotalUnreadResponse;
import com.kanzhun.foundation.api.model.SystemConfigModel;
import com.kanzhun.foundation.api.model.ThumbListResponse;
import com.kanzhun.foundation.api.model.UserCertStatusModel;
import com.kanzhun.foundation.api.model.UserCertStatusModelV2;
import com.kanzhun.foundation.api.model.UserInfoModel;
import com.kanzhun.foundation.api.model.VideoMeetingInfo;
import com.kanzhun.foundation.api.response.CouponGetProcessBarResponse;
import com.kanzhun.foundation.api.response.GpsLocationResponse;
import com.kanzhun.foundation.bean.MyWashResponse;
import com.kanzhun.foundation.bean.RecommendTagIdealPartnerMatchResponse;
import com.kanzhun.foundation.bean.UserSchoolGetBean;
import com.kanzhun.foundation.bean.WashMeResponse;
import com.kanzhun.foundation.model.AgreementBean;
import com.kanzhun.foundation.model.GeeCaptchaRegisterResponse;
import com.kanzhun.foundation.model.QRBean;
import com.kanzhun.foundation.model.UserSafeModel;
import com.kanzhun.foundation.model.matching.MatchingBlockInfoModel;
import com.kanzhun.foundation.model.matching.UserGuideBlockModel;
import com.kanzhun.foundation.model.profile.GuideItemsResponse;
import com.kanzhun.foundation.model.profile.QuestionInfoResponse;
import com.kanzhun.foundation.zxing.bean.ProtoBean;
import com.kanzhun.http.response.BaseResponse;

import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR>
 * @date 2022/3/29.
 */
public interface FoundationApi {
    @GET(URLConfig.URL_SYSTEM_CONFIG)
    Observable<BaseResponse<SystemConfigModel>> getSystemConfigModel();

    @GET(URLConfig.URL_ACTIVITY_COUPON_GET_PROCESS_BAR)
    Observable<BaseResponse<CouponGetProcessBarResponse>> getActivityCouponGetProcessBar();

    @GET(URLConfig.URL_USER_GET_QR_BY_USER_ID)
    Observable<BaseResponse<QRBean>> getQRByUserId(@Query("userId") String userId);

    @GET(URLConfig.URL_COMMON_SCHOOL_SUGGEST)
    Observable<BaseResponse<SearchSchoolResponse>> searchSchool(@Query("content") String content);

    @GET(URLConfig.URL_USER_ME_INFO)
    Observable<BaseResponse<UserInfoModel>> queryUserInfo();

    @GET(URLConfig.URL_ORANGE_USER_SCHOOL_GET)
    Observable<BaseResponse<UserSchoolGetBean>> querySchoolGet();

    @GET(URLConfig.URL_RECOMMEND_TAG_IDEAL_PARTNER_MATCH)
    Observable<BaseResponse<RecommendTagIdealPartnerMatchResponse>> requestRecommendTagIdealPartnerMatch(@Query("tagId") String tagId);

    /**
     * @param model 1-直辖市下级只返回区，2-直辖市下级只返回市,3-直辖市下级只返回市，增加不限的选项
     */
    @GET(URLConfig.URL_COMMON_ADDRESS)
    Observable<BaseResponse<CommonAddressResponse>> getCommonAddress(@Query("model") int model);

    @GET(URLConfig.URL_COMMON_ADDRESS)
    Observable<BaseResponse<CommonAddressResponseK>> getCommonAddress1(@Query("model") int model);

    @GET(URLConfig.URL_GET_GUIDE_ITEMS)
    Observable<BaseResponse<GuideItemsResponse>> getGuideItems();

    @GET(URLConfig.URL_GET_FRIENDS)
    Observable<BaseResponse<ContactResponse>> getFriends(@QueryMap Map<String, Object> map);

    @GET(URLConfig.URL_GET_NOTICE_LIST)
    Observable<BaseResponse<NoticeListResponse>> getNoticeList();

    @GET(URLConfig.URL_GET_THUMB_LIST)
    Observable<BaseResponse<ThumbListResponse>> getThumbList(@Query("offsetId") String thumbId, @Query("pageSize") int pageSize);

    @GET(URLConfig.URL_SYNC_CONVERSATION_TAB_SUMMARY)
    Observable<BaseResponse<ConversationTabSummaryInfo>> getConversationTab();

    @GET(URLConfig.URL_SYNC_CONVERSATION_TAB_SUMMARY)
    Observable<BaseResponse<InteractTabSummary>> getInteractTabCount();

    @GET(URLConfig.URL_SYNC_MATCH_PAGE)
    Observable<BaseResponse<MatchingPageInfo>> getMatchingPageInfo();

    /**
     * 查询房间信息
     *
     * @param roomId
     * @return
     */
    @GET(URLConfig.URL_VIDEO_MEETING_INFO)
    Observable<BaseResponse<VideoMeetingInfo>> getVideoMeetingInfo(@Query("roomId") String roomId);

    @GET(URLConfig.URL_BLOCK_INFO)
    Observable<BaseResponse<MatchingBlockInfoModel>> requestBlockInfo();

    @FormUrlEncoded
    @POST(URLConfig.URL_TOKEN_UPDATE)
    Observable<BaseResponse> registerPushToken(@Field("type") String type, @Field("token") String token);

    @FormUrlEncoded
    @POST(URLConfig.URL_ORANGE_LIMITED_ACTIVITY_TASK_HIDE_TASK_FIND)
    Observable<BaseResponse<HideTaskFIndBean>> hideTaskFind(@Field("type") String type);

    @FormUrlEncoded
    @POST(URLConfig.URL_ORANGE_LIMITED_ACTIVITY_TASK_FINDCOMPLETE)
    Observable<BaseResponse<HideTaskFIndBean>> taskCpFindComplete(@Field("taskId") String taskId);

    @FormUrlEncoded
    @POST(URLConfig.URL_TOKEN_DELETE)
    Observable<BaseResponse> unRegisterToken();

    @FormUrlEncoded
    @POST(URLConfig.URL_SETTING_UPDATE)
    Observable<BaseResponse> settingUpdate(@Field("key") int key, @Field("value") int value);

    @FormUrlEncoded
    @POST(URLConfig.URL_POST_USER_UPDATE_SCHOOL)
    Observable<BaseResponse> updateSchool(@FieldMap ArrayMap<String, Object> map);

    /**
     * @param version 客户端版本号
     * @param real    为1时返回当前的type，为0时建议升级的type有可能不是真正的(如两天内只发一次真正的)
     */
    @GET(URLConfig.URL_VERSION_QUERY)
    Observable<BaseResponse<QueryVersionBean>> queryVersion(@Query("version") String version, @Query("real") int real);

    @GET(URLConfig.URL_ORANGE_COMMON_DIALOG_F1)
    Observable<BaseResponse<F1DialogBean>> commonDialogF1();

    @GET(URLConfig.URL_ORANGE_HOME_POP)
    Observable<BaseResponse<HomePopBean>> homePop();

    /**
     * 查询其他用户认证状态
     *
     * @param userId 用户id
     * @return
     */
    @GET(URLConfig.URL_USER_CERT_STATUS)
    Observable<BaseResponse<UserCertStatusModel>> getUserCertStatus(@Query("userId") String userId);

    @GET(URLConfig.URL_USER_CERT_STATUS_V2)
    Observable<BaseResponse<UserCertStatusModelV2>> getUserCertStatusV2(@Query("userId") String userId);

    @GET(URLConfig.URL_GET_SOCIAL_UNREAD)
    Observable<BaseResponse<SocialTotalUnreadResponse>> requestSocialUnreadCount();

    @GET(URLConfig.URL_GET_LIMITED_ACTIVITY_TASK_EXPO)
    Observable<BaseResponse<LimitedActivityTaskExpoBean>> requestLimitedActivityTaskExpoBean();

    @Headers(MediaTypeConstant.CONTENT_TYPE_APPLICATION_JSON)
    @POST(URLConfig.URL_DATA_POINT)
    Observable<BaseResponse> dataPoint(@Body RequestBody body);

    /**
     * 查询用户信息完善情况
     */
    @GET(URLConfig.URL_PROFILE_STATUS)
    Observable<BaseResponse<ProfileStatusModel>> requestProfileStatus();

    /**
     * 查询用户信息完善情况
     */
    @GET(URLConfig.URL_PARENT_INVITE_INVITE_BIND_LIST)
    Observable<BaseResponse<InviteBindResp>> requestInviteBindList();

    /**
     * 接受绑定邀请
     *
     * @param inviteId
     * @return
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_PARENT_INVITE_BIND_ACCEPT)
    Observable<BaseResponse<AcceptBindResp>> acceptInviteBind(@Field("inviteId") String inviteId);

    /**
     * 拒绝绑定邀请
     *
     * @param inviteId
     * @return
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_PARENT_INVITE_BIND_REFUSE)
    Observable<BaseResponse<Object>> refuseInviteBind(@Field("inviteId") String inviteId);

    /**
     * 发送喜欢前检查
     */
    @GET(URLConfig.URL_FOUNDATION_SEND_LIKE_CHECK)
    Observable<BaseResponse<UserGuideBlockModel>> checkBeforeSendLike(@Query("resourceType") String resourceType);

    /**
     * 获取用户安全信息
     */
    @GET(URLConfig.URL_USER_SAFE)
    Observable<BaseResponse<UserSafeModel>> getUserSafeInfo();

    /**
     * 获取协议详情
     */
    @GET(URLConfig.URL_AGREEMENT_DETAIL)
    Observable<BaseResponse<AgreementBean>> getAgreementDetail(@Query("agreementId") String agreementId);

    @GET(URLConfig.URL_USER_QUESTION_INFO)
    Observable<BaseResponse<QuestionInfoResponse>> getQuestionDetail(@Query("id") long id);

    @FormUrlEncoded
    @POST(URLConfig.URL_GEE_CAPTCHA_REGISTER)
    Observable<BaseResponse<GeeCaptchaRegisterResponse>> geeCaptchaRegister(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_ORANGE_USER_AB_FACE_GET_BY_ID)
    Observable<BaseResponse<ABFace>> getABbyId(@Query("id") String id);

    /**
     * 获取协议详情v1
     */
    @GET(URLConfig.URL_CHAT_STICKER_LIST_V1)
    Observable<BaseResponse<JsonObject>> getStickerList();

    /**
     * 查询用户是否存在处置
     */
    @GET(URLConfig.URL_ORANGE_SECURITY_GET)
    Observable<BaseResponse<SecurityNoticeResponse>> getSecurityNoticeInfo();


    @GET(URLConfig.URL_ORANGE_QR_GETPROTO)
    Observable<BaseResponse<ProtoBean>> getProto(@Query("id") String id, @Query("type") String type, @Query("sourceType") String sourceType);

    @FormUrlEncoded
    @POST(URLConfig.URL_ORANGE_THUMB_ADD)
    Observable<BaseResponse<ThumbAddBean>> thumbAdd(@Field("userId") String userId, @Field("resourceType") String resourceType, @Field("resourceId") String resourceId,@Field("filmProcessId")String filmProcessId);

    @GET(URLConfig.URL_COMMON_GPS_LOCATION)
    Observable<BaseResponse<GpsLocationResponse>> getGpsLocation(@Query("gpsInfo") String encGpsInfo); // 116.307490,39.984154
}
