package com.kanzhun.foundation.router;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.api.callback.SendLikeBean;
import com.kanzhun.foundation.api.model.SendChatBean;
import com.kanzhun.foundation.api.model.SendLikeModel;
import com.kanzhun.foundation.bean.SerializableMap;
import com.kanzhun.foundation.model.UserGuideBlockInfoBean;
import com.kanzhun.foundation.model.matching.MatchingLikeModel;
import com.kanzhun.foundation.router.service.IMatchingRouterService;
import com.kanzhun.foundation.views.PreviewLikeView;
import com.sankuai.waimai.router.Router;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12.
 */
public class MatchingPageRouter {
    private static final String MODULE_NAME_MATCHING = "/matching";
    public static final String MATCHING_REQUIREMENT_ACTIVITY = MODULE_NAME_MATCHING + "/requirement_activity";

    public static final String MATCHING_GUEST_ACTIVITY = MODULE_NAME_MATCHING + "/guest_activity";

    public static final String MATCHING_REVIEW_LIST_ACTIVITY = MODULE_NAME_MATCHING + "/review_list_activity";

    //service
    public static final String MATCHING_SERVICE = "matching_service";


//    /**
//     * 跳转到发送喜欢页面
//     */
//    public static void jumpToSendLikeActivity(Activity activity, SendLikeModel sendLikeModel, View view) {
//        if (view == null) {
//            Bundle bundle = new Bundle();
//            bundle.putSerializable(BundleConstants.BUNDLE_DATA, sendLikeModel);
//            AppUtil.startUri(activity, MATCHING_SEND_LIKE_ACTIVITY, bundle);
//        } else {
//            Pair<View, String>[] pairs = new Pair[]{Pair.create(view, "preview_content")};
//            ActivityOptionsCompat transitionActivityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(activity, pairs);
//            Bundle bundle = transitionActivityOptions.toBundle();
//            bundle.putSerializable(BundleConstants.BUNDLE_DATA, sendLikeModel);
//            AppUtil.startUri(activity, MATCHING_SEND_LIKE_ACTIVITY, bundle);
//        }
//    }

    public static void jumpSendLikeDialog(FragmentActivity activity, SendLikeModel sendLikeModel, int pageSource) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpSendLikeDialog(activity, sendLikeModel, pageSource);
        }
    }

    public static void jumpSendLikeDialog(FragmentActivity activity, SendLikeModel sendLikeModel, int type, int pageSource) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpSendLikeDialog(activity, sendLikeModel, type, pageSource);
        }
    }

    /**
     * 跳转到回看页面
     */
    public static void jumpToReviewListActivity(Activity activity) {
        AppUtil.startUri(activity, MATCHING_REVIEW_LIST_ACTIVITY, null);
    }

    public static void jumpToGuestActivity(Context context, SerializableMap map) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, map);
        AppUtil.startUri(context, MATCHING_GUEST_ACTIVITY, bundle);
    }

    public static void jumpToNewGuestActivity(Context context, SerializableMap map) {
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, map);
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT,1);
        AppUtil.startUri(context, MATCHING_GUEST_ACTIVITY, bundle);
    }

    public static void jumpMatchRequirementActivity(Context activity) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpMatchRequirementActivity(activity);
        }
    }


    /**
     * @param activity
     * @param userId
     * @param userName
     * @param avatar
     * @param source   参考这个类 SendLikePageSource
     * @param relationStatus 埋点用
     */
    public static void jumpSendLikeActivity(Context activity, String userId, String userName,
                                            String avatar, int relationStatus, PageSource source,
                                            List<String> chatWordList, String encMoodId,String securityId,boolean autoLike,boolean useAi) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpSendLikeActivity(activity, new SendLikeModel(userId, userName, avatar,
                    source, chatWordList, relationStatus, encMoodId,securityId,autoLike,useAi));
        }
    }

    public static void jumpSendLikeChatActivity(Context activity, String userId, PreviewLikeView.LIKE_TYPE likeType, PageSource source,
                                                List<String> chatWordList, String resId, SendLikeBean sendLikeBean) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpSendLikeChatActivity(activity,new SendChatBean(userId,likeType,source,chatWordList,resId,sendLikeBean,false));
        }
    }

    public static void jumpSendLikeChatActivity(Context activity, String userId, PreviewLikeView.LIKE_TYPE likeType, PageSource source,
                                                List<String> chatWordList, String resId, SendLikeBean sendLikeBean,boolean isSecondPageView) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.jumpSendLikeChatActivity(activity,new SendChatBean(userId,likeType,source,chatWordList,resId,sendLikeBean,isSecondPageView));
        }
    }

    /**
     * 统一处理发送喜欢后的逻辑，弹出toast或者顶部的通知
     *
     * @param context
     * @param matchingLikeModel
     */
    public static void handleSendLikeResult(Activity context, MatchingLikeModel matchingLikeModel) {
        IMatchingRouterService s = Router.getService(IMatchingRouterService.class, MATCHING_SERVICE);
        if (s != null) {
            s.handleSendLikeResult(context, matchingLikeModel);
        }
    }


    public static void showSendLikeExaminingBlockDialog(
            Context context,
            UserGuideBlockInfoBean blockInfoBean,
            PageSource currentPage) {
        IMatchingRouterService service = Router.getService(
                IMatchingRouterService.class, MatchingPageRouter.MATCHING_SERVICE);
        service.showUserGuideBlockDialogWhenSendLike(context, blockInfoBean, currentPage);
    }

}
