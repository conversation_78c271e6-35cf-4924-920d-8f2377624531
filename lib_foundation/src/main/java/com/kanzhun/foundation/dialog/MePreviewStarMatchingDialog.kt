package com.kanzhun.foundation.dialog

import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.coorchice.library.SuperTextView
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.click
import com.kanzhun.foundation.R
import com.kanzhun.foundation.bean.StarInfo
import com.kanzhun.foundation.databinding.MePreviewCharacterMatchingDialogBinding
import com.kanzhun.foundation.databinding.MePreviewStarMatchingDialogBinding
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.profile.CompareContent
import com.kanzhun.foundation.model.profile.MatchInfo

/**
 * 性格匹配度测试预览弹窗
 */
class MePreviewStarMatchingDialog(val activity: FragmentActivity) {
    fun show(starInfo: StarInfo?) {
        CommonViewBindingDialog(activity,
            mCancelable = true,
            mCanceledOnTouchOutside = true,
            mGravity = Gravity.CENTER,
            mPaddingLeft = 32,
            mPaddingRight = 32,
            onInflateCallback = { inflater, dialog ->
                val binding = MePreviewStarMatchingDialogBinding.inflate(inflater)
                binding.apply {
                    showScore(true)
                    tvTitle.text = "星座匹配度 ${starInfo?.score}分"

                    starInfo?.loginUser?.run {
                        leftType.text = this.starName
                        leftAvatar.load(this.tinyAvatar)
                    }

                    starInfo?.viewUser?.run {
                        rightType.text = this.starName
                        rightAvatar.load(this.tinyAvatar)
                    }
                    rcvResult.text = starInfo?.matchContent
                    ivClose.click {
                        dialog.dismiss()
                    }
                }
                binding
            })
            .show()

    }

    private fun MePreviewStarMatchingDialogBinding.showScore(show:Boolean){
        tvTitle.visible(show)
        ivStar1.visible(show)
    }

}

