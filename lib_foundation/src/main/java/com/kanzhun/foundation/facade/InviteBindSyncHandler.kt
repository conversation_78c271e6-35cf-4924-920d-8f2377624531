package com.kanzhun.foundation.facade

import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.bean.InviteBindResp
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager


/**
 * 邀请绑定同步列表
 */
class InviteBindSyncHandler( val inviteBindData: MutableLiveData<InviteBindResp?> = MutableLiveData()) : BaseHandler<InviteBindResp?>() {
    override fun syncData() {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).requestInviteBindList()
        HttpExecutor.execute<InviteBindResp>(observable, this)
    }

    override fun handleInChildThread(data: InviteBindResp?) {
        super.handleInChildThread(data)
        data?.run {
            inviteBindData.postValue(this)
        }
    }

    override fun destroy() {
        super.destroy()
        inviteBindData.value = null
    }
}