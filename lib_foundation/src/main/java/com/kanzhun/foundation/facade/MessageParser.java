package com.kanzhun.foundation.facade;

import static com.kanzhun.common.kotlin.ext.LiveEventBusExtKt.sendLocalMessageChangedEvent;

import com.kanzhun.common.app.AppThreadFactory;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon;
import com.kanzhun.common.kotlin.ext.BroadcastExtKt;
import com.kanzhun.common.kotlin.ext.LiveEventBusExtKt;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.ProtocolHelper;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.ReadStatus;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.localNotify.LocalNotifyMessageBean;
import com.kanzhun.localNotify.SwipeNotificationManager;
import com.kanzhun.mms.client.IReceiveListener;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.utils.GsonUtils;
import com.techwolf.lib.tlog.TLog;
import com.xxjz.orange.protocol.codec.ChatProtocol;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

//负责消息的接收和解析，以及事件分发给其他组件
public class MessageParser implements IReceiveListener {
    private static final String TAG = "MessageParser";

    private MessageCallback mCallback;

    public void setCallback(MessageCallback callback) {
        mCallback = callback;
    }

    public MessageCallback getCallback() {
        return mCallback;
    }

    public MessageParser() {
    }

    @Override
    public void onReceive(byte[] data) {
        try {

            ChatProtocol.OrangeChatProtocol hiChatProtocol = ChatProtocol.OrangeChatProtocol.parseFrom(data);
            BLog.d(TAG, "onReceive size = [%d], protocol : %s", data.length, hiChatProtocol);
            //消息类型datasync action等
            switch (hiChatProtocol.getType()) {
                case MessageConstants.MSG_TYPE_MESSAGE:
//                    if (hiChatProtocol.getScene() == MessageConstants.MSG_SCENE_PART) {//如果是部分消息,更新会话
//                        SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONVERSTAION_SYNC);
//                    }
                    onMessage(hiChatProtocol.getMessagesList(), hiChatProtocol.getScene() == 1);

                    sendLocalMessageChangedEvent();
                    break;
                case MessageConstants.MSG_TYPE_READ_MARK:
                    onReadMark(hiChatProtocol.getReadMarksList());
                    break;
                case MessageConstants.MSG_TYPE_READ_SYNC:
                    onReadSync(hiChatProtocol.getReadSyncsList(), hiChatProtocol.getScene() == 1);
                    break;
                case MessageConstants.MSG_TYPE_DATA_SYNC:
                    onDataSync(hiChatProtocol.getDataSyncsList());
                    break;
                case MessageConstants.MSG_TYPE_PRESENCE:
//                    ServiceManager.getInstance().logout();
                    break;
                case MessageConstants.MSG_TYPE_CONVERSATION_SYNC:
                    if (AccountHelper.getInstance().chatEnable()) {
                        ChatProtocol.OgChatSync hiChatSync = hiChatProtocol.getChatSync();
                        SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONVERSATION_SYNC_COMPLETE_DATA, hiChatSync);
                    }

                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            TLog.error(TAG, "onReceive error : %s", e.toString());
        }
    }

    public void onConnectionConnected() {
        MessageCallback messageCallback = mCallback;
        if (messageCallback != null) messageCallback.onConnectionConnected();
    }

    private void onMessage(List<ChatProtocol.OgMessage> messagesList, boolean isRealTimeMessage) {
        List<ChatMessage> chatMessageList = new ArrayList<>(messagesList.size());
        for (ChatProtocol.OgMessage ogMessage : messagesList) {
            ChatMessage chatMessage = PB2MessageConvert.parseMessage(ogMessage);
            chatMessageList.add(chatMessage);
        }
        MessageCallback messageCallback = mCallback;
        if (messageCallback != null) messageCallback.onMessage(chatMessageList, isRealTimeMessage);
    }

    private void onReadSync(List<ChatProtocol.OgMessageRead> readList, boolean isRealTimeMessage) {
        List<ReadStatus> readStatuses = new ArrayList<>(readList.size());
        for (ChatProtocol.OgMessageRead OgMessageRead : readList) {
            readStatuses.add(new ReadStatus(OgMessageRead.getMid(), OgMessageRead.getChatId(), MessageConstants.MSG_SINGLE_CHAT, OgMessageRead.getStatus()));
        }
        MessageCallback messageCallback = mCallback;
        if (messageCallback != null) messageCallback.onReadSync(readStatuses, isRealTimeMessage);
    }

    private void onReadMark(List<ChatProtocol.OgMessageRead> readList) {
        List<ReadStatus> readStatuses = new ArrayList<>(readList.size());
        for (ChatProtocol.OgMessageRead OgMessageRead : readList) {
            readStatuses.add(new ReadStatus(OgMessageRead.getMid(), OgMessageRead.getChatId(), MessageConstants.MSG_SINGLE_CHAT, OgMessageRead.getStatus()));
        }
        MessageCallback messageCallback = mCallback;
        if (messageCallback != null) messageCallback.onReadMark(readStatuses);
    }

    private void onDataSync(java.util.List<ChatProtocol.OgDataSync> hiDataSyncList) {
//        List<Integer> datas = new ArrayList<>(hiDataSyncList.size());
        TLog.info(TAG, "onDataSync");
        for (ChatProtocol.OgDataSync hiDataSync : hiDataSyncList) {
            BLog.e(TAG, "hiDataSync.getType() = " + hiDataSync.getType());
            if (ChatSyncTypeWhiteList.Companion.check(hiDataSync.getType())) {
                continue;
            }
            switch (hiDataSync.getType()) {
                case MessageConstants.MSG_DATA_SYNC_CONTACT:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONTACT_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_CONVERSATION:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONVERSATION_PROP_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_PROFILE:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_PROFILE_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_LINK_ROOM_OTHER_BAD_NETWORK:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_LINK_ROOM_BAD_NETWORK_SYNC, hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_LINK_ROOM_CHANGE_AUDIO:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_LINK_ROOM_CHANGE_AUDIO_SYNC, hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_TAB_CONVERSATION_SUMMARY:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_TAB_CONVERSATION_SUMMARY_INFO_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_TAB_MATCH:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_TAB_MATCH_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_SYSTEM_CONFIG:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_SYS_CONFIG_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_SOCIAL_UNREAD:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_SOCIAL_UNREAD);
                    break;
                case MessageConstants.MSG_DATA_SYNC_EMPATHY_MATCH_SUCCESS:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_EMPATHY_MATCH_SUCCESS, hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_INVITE_BIND://邀请绑定
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_SYNC_INVITE_BIND);
                    break;
                case MessageConstants.MSG_DATA_SYNC_BIND_INFO:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_PROFILE_SYNC);
                    LiveEventBusExtKt.sendBindStatusChangeEvent(AccountHelper.getInstance().getUserId());
                    break;
                case MessageConstants.MSG_DATA_SYNC_NOTICE_LIST://109-同步小秘书通知列表
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_NOTICE_LIST_SYNC);
                    break;
                case MessageConstants.MSG_DATA_SYNC_RESTRICT_CHAT: // 111-同步限制开聊
                    LiveEventBusExtKt.sendRestrictChatEvent();
                    break;
                case MessageConstants.MSG_DATA_SYNC_CHRISTMAS_TASK: // 112-同步圣诞活动任务曝光
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_TEMP_TASK);
                    break;
                case MessageConstants.MSG_DATA_SYNC_113: // 113
                    AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            String json = hiDataSync.getExtension();
                            try {
                                JSONObject jsonObject = new JSONObject(json);
                                new ActionHandlerK().handler10070x(jsonObject.getInt("type"));
                            } catch (JSONException e) {
                                throw new RuntimeException(e);
                            }

                        }
                    }, 500);
                    break;
                case MessageConstants.MSG_DATA_SYNC_MEETUP_CHAT_LIST:
                    BroadcastExtKt.sendRequestChatListEvent(LivedataKeyCommon.EVENT_KEY_REQUEST_CHAT_LIST);
                    break;
                case MessageConstants.MSG_SYNC_YUEYUE_CARD:
                    BroadcastExtKt.sendRequestChatListEvent(LivedataKeyCommon.EVENT_KEY_REQUEST_YUEYUE_CARD);
                    break;
                case MessageConstants.MSG_DATA_SYNC_SINGLE_CHAT_TOP_FLOATING_CARD:
                    //通知聊天顶部悬浮窗刷新
                    LiveEventBusExtKt.sendLocalMessageNotifySingleChatTopFresh();
                    break;
                case MessageConstants.MSG_DATA_SYNC_TOPIC_CARD_RECOMMEND://话题卡片推荐
                    /*
                        {
                          "content" : "喜欢打什么类型的游戏？",
                          "sourceId" : "UOhPQ3npd3-c5L4rY2cfiTAex2U1AmEQBEko1FtPwW8~",
                          "title" : "不知道聊什么？试试选个话题吧",
                          "userId" : "PEGuH1s5N1njxRJ7pQ4UmLikB9YvDFnEJTORAjTmlI8~"
                        }
                     */
                    LiveEventBusExtKt.setTopicCardRecommendEvent(hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_DYNAMIC_RECOMMEND: // 动态推荐：https://zhishu.zhipin.com/wiki/eC6RHVPJ8mQ
                    /*
                         {
                             "userId": "PEGuH1s5N1njxRJ7pQ4UmLikB9YvDFnEJTORAjTmlI8~",
                             "momentId": "zzzzzzz",
                             "content": "走着走着",
                             "createTime": 1657607439000,
                             "pictures": [{
                                 "url": "https://orange-qa.weizhipin.com/api/media/download/TsH7Q5gxELKzib1FaJ__S3_zPKZIGPksyGkh.jpeg",
                                 "width": 1920,
                                 "height": 1080
                             }]
                         }
                     */
                    LiveEventBusExtKt.sendDynamicRecommendEvent(hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_CHAT_ICON:
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CHAT_ICON_SYNC, hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_CHAT_SEND_STATUS:
                    //同步聊天发送状态
                    LiveEventBusExtKt.setChatSendStatusEvent(hiDataSync.getExtension());
                    break;
                case MessageConstants.MSG_DATA_SYNC_LOCAL_PUSH_NOTICE:
                    try {
                        TLog.info(TAG, "onDataSync 100201:" + hiDataSync.getExtension());
                        LocalNotifyMessageBean bean = GsonUtils.getGson().fromJson(hiDataSync.getExtension(), LocalNotifyMessageBean.class);
                        TLog.info(TAG, "onDataSync 100201:bean:" + bean.toString());
                        //相互喜欢

                        ExecutorFactory.execMainTask(new Runnable() {
                            @Override
                            public void run() {
                                SwipeNotificationManager.getInstance(BaseApplication.getApplication()).addNotification(bean);
                                if (!SwipeNotificationManager.getInstance(BaseApplication.getApplication()).isStop()) {
                                    if (bean.getType() == 2 || bean.getType() == 1) {
                                        HashMap<String, Object> params = new HashMap<>();
                                        params.put("userId", bean.getFriendId());
                                        HttpExecutor.requestSimplePost(URLConfig.URL_ORANGE_LIKE_EACH_OTHER_READ, params, null);
                                    }
                                }
                            }
                        });
                    } catch (Exception e) {
                        TLog.info(TAG, "onDataSync :" + e.toString());
                    }
                    break;
                case MessageConstants.DATA_SYNC_100701:
                    break;
                case MessageConstants.DATA_SYNC_100702:
                    break;
                case MessageConstants.DATA_SYNC_100703:
                    break;
                case MessageConstants.DATA_SYNC_100604:
                    AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            ProtocolHelper.parseProtocol("og://og.app/open?type=229&scene=1");
                        }
                    }, 500);

                    break;
                case MessageConstants.DATA_SYNC_100610:
                    //同步见面计划任务完成数
                    // {"chatId":"xxxx", "num": 3}
                    LiveEventBusExtKt.setMeetingPlanRecommendEvent(hiDataSync.getExtension());
                    break;

                case MessageConstants.DATA_SYNC_100620:
                    //同步好友位置
                    // DataSync新增类型：100620-好友位置，sync内容格式{"userId":"xxxx", "gps": "119.97372,31.76943","rotateAngle":30}，gps为经纬度英文逗号分割
                    LiveEventBusExtKt.setFriendLocationEvent(hiDataSync.getExtension());
                    break;
                default:
                    break;
            }
        }
//        MessageCallback messageCallback = mCallback;
//        if (messageCallback != null) messageCallback.onDataSync(datas);
    }
}
