package com.kanzhun.foundation.facade;

import com.kanzhun.foundation.model.message.MessageForAction;

/**
 * create by sunyangyang
 * on 2019-10-14
 */
public interface VideoMessageCallback {
    void invitationChat(MessageForAction messageForAction);

    void invitationCancel(MessageForAction messageForAction);

    void invitationRefuse(MessageForAction messageForAction);

    void videoChatOver(MessageForAction messageForAction);

    void videoChatBroken(MessageForAction messageForAction);

    void videoChatNoAnswer(MessageForAction messageForAction);

    void videoChatAccept(MessageForAction messageForAction);
}
