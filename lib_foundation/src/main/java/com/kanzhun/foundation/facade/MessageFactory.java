package com.kanzhun.foundation.facade;

import static com.kanzhun.foundation.model.message.MessageConstants.MSG_CHRISTMAS_CARD;

import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.kanzhun.common.model.ImageInfo;
import com.kanzhun.common.util.ImageUtils;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.ReplyBean;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.EmotionInfo;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForAction;
import com.kanzhun.foundation.model.message.MessageForActivityMedia;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForChristmasCard;
import com.kanzhun.foundation.model.message.MessageForCommonCard;
import com.kanzhun.foundation.model.message.MessageForDateCard;
import com.kanzhun.foundation.model.message.MessageForEmotion;
import com.kanzhun.foundation.model.message.MessageForEmpty;
import com.kanzhun.foundation.model.message.MessageForHint;
import com.kanzhun.foundation.model.message.MessageForLike;
import com.kanzhun.foundation.model.message.MessageForLinkCall;
import com.kanzhun.foundation.model.message.MessageForLoveCard;
import com.kanzhun.foundation.model.message.MessageForMood;
import com.kanzhun.foundation.model.message.MessageForNotifyCard;
import com.kanzhun.foundation.model.message.MessageForPic;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.foundation.model.message.MessageForVideo;
import com.techwolf.lib.tlog.TLog;

import java.io.File;

public class MessageFactory {
    private static long mLastTime;

    @NonNull
    public static ChatMessage createMessage(int msgType) {
        ChatMessage chatMessage;
        switch (msgType) {
            case MessageConstants.MSG_TEXT:
                chatMessage = new MessageForText();
                break;
            case MessageConstants.MSG_PIC:
                chatMessage = new MessageForPic();
                break;
            case MessageConstants.MSG_AUDIO:
                chatMessage = new MessageForAudio();
                break;
            case MessageConstants.MSG_VIDEO:
                chatMessage = new MessageForVideo();
                break;
            case MessageConstants.MSG_HINT:
                chatMessage = new MessageForHint();
                break;
            case MessageConstants.MSG_ACTION:
                chatMessage = new MessageForAction();
                break;
            case MessageConstants.MSG_LINK_CALL:
                chatMessage = new MessageForLinkCall();
                break;
            case MessageConstants.MSG_EMPTY:
                chatMessage = new MessageForEmpty();
                break;
            case MessageConstants.MSG_LIKE_CARD:
                chatMessage = new MessageForLike();
                break;
            case MessageConstants.MSG_LOVE_CARD:
                chatMessage = new MessageForLoveCard();
                break;
            case MessageConstants.MSG_DATE_CARD:
                chatMessage = new MessageForDateCard();
                break;
            case MessageConstants.MSG_NOTIFY_CARD:
                chatMessage = new MessageForNotifyCard();
                break;
            case MessageConstants.MSG_MOOD_CARD:
                chatMessage = new MessageForMood();
                break;
            case MessageConstants.MSG_TOPIC_GAME:
                chatMessage = new MessageForTopicGame();
                break;
            case MessageConstants.MSG_COMMON_CARD:
                chatMessage = new MessageForCommonCard();
                break;
            case MessageConstants.MSG_OFFICIAL_ACTIVITY_CARD:
                TLog.info("MessageFactory", "createMessage: MSG_OFFICIAL_ACTIVITY_CARD");
                chatMessage = new MessageForActivityMedia();
                break;
            case MessageConstants.MSG_STICKER:
                chatMessage = new MessageForEmotion();
                break;
            case MessageConstants.MSG_CHRISTMAS_CARD:
                chatMessage = new MessageForChristmasCard();
                chatMessage.setMediaType(MSG_CHRISTMAS_CARD);
                break;
            default:
                chatMessage = KtMessageFactoryKt.createMessage(msgType);
                break;
        }
        return chatMessage;
    }

    public static void createBaseInfo(ChatMessage message, String chatId, ReplyBean replyBean) {
        message.setChatId(chatId);
        message.setSender(AccountHelper.getInstance().getAccount().getUserId());
        long time = System.currentTimeMillis();
        if (time <= mLastTime) {
            time = ++mLastTime;
        } else {
            mLastTime = time;
        }
        message.setCmid(time);
        message.setTime(time);
        message.setStatus(MessageConstants.MSG_STATE_SENDING);
        if (replyBean != null) {
            if (replyBean.getReplyId() > 0) {
                message.setReplyId(replyBean.getReplyId());
            }
        }
    }

    public static MessageForText createTextMessage(String content, String chatId, int type, ReplyBean replyBean) {
        MessageForText msg = new MessageForText();
        if (!TextUtils.isEmpty(content)) {
            msg.setContent(content.trim());
        } else {
            msg.setContent(content);
        }
        msg.setType(type);
        createBaseInfo(msg, chatId, replyBean);
        return msg;
    }

    public static MessageForEmotion createEmotionMessage(String id, String packId,
                                                         String name,
                                                         ImageInfo tinyImage,
                                                         ImageInfo originalImage,
                                                         String chatId, int type, ReplyBean replyBean) {
        MessageForEmotion msg = new MessageForEmotion();
        EmotionInfo emotionInfo = new EmotionInfo(id, packId, name, tinyImage, originalImage);
        msg.setMEmotionInfo(emotionInfo);
        msg.setType(type);
        createBaseInfo(msg, chatId, replyBean);
        return msg;
    }

    public static MessageForPic createPicMessage(Uri uri, String chatId, int type, ReplyBean replyBean) {
        MessageForPic msg = new MessageForPic();
        ImageUtils.ImageSize imageSize = ImageUtils.chatSourceSize(uri);
        msg.setLocalUrl(new ImageInfo((int) imageSize.getWidth(), (int) imageSize.getHeight(), uri.toString()));
        msg.setType(type);
        createBaseInfo(msg, chatId, replyBean);
        return msg;
    }

    public static MessageForPic createPicMessage(ImageInfo imageInfo, String chatId, int type, ReplyBean replyBean) {
        MessageForPic msg = new MessageForPic();
        msg.setLocalUrl(imageInfo);
        msg.setType(type);
        createBaseInfo(msg, chatId, replyBean);
        return msg;
    }

    public static MessageForPic createPicMessage(ImageInfo originalInfo, ImageInfo tinyInfo, ImageInfo localInfo, String chatId, int type, ReplyBean replyBean) {
        MessageForPic msg = new MessageForPic();
        msg.setImage(originalInfo, tinyInfo);
        msg.setLocalUrl(localInfo);
        msg.setType(type);
        createBaseInfo(msg, chatId, replyBean);
        return msg;
    }


    public static MessageForVideo createVideoMessage(Uri uri, long size, int duration, String toId, int type, ReplyBean replyBean) {
        MessageForVideo messageForVideo = new MessageForVideo();
        MessageForVideo.VideoInfo videoInfo = new MessageForVideo.VideoInfo(size, duration, uri.toString());
        messageForVideo.setVideoInfo(videoInfo);
        messageForVideo.setType(type);
        createBaseInfo(messageForVideo, toId, replyBean);
        return messageForVideo;
    }

    public static MessageForVideo createVideoMessage(String url, String path, long size, int duration, ImageInfo info, String toId, int type, ReplyBean replyBean) {
        MessageForVideo messageForVideo = new MessageForVideo();
        MessageForVideo.VideoInfo videoInfo = new MessageForVideo.VideoInfo(url, path, size, duration, info);
        messageForVideo.setVideoInfo(videoInfo);
        messageForVideo.setType(type);
        createBaseInfo(messageForVideo, toId, replyBean);
        return messageForVideo;
    }

    public static MessageForAudio createAudioMessage(String url, String path, long duration, long size, int[] waveArray, String toId, int type, ReplyBean replyBean) {
        MessageForAudio messageForAudio = new MessageForAudio();
        MessageForAudio.AudioInfo audioInfo = new MessageForAudio.AudioInfo(url, path, duration, size, waveArray);
        messageForAudio.setAudioInfo(audioInfo);
        messageForAudio.setType(type);
        createBaseInfo(messageForAudio, toId, replyBean);
        return messageForAudio;
    }

    public static MessageForAudio createAudioMessage(File file, long duration, long size, int[] waveArray, String toId, int type, ReplyBean replyBean) {
        MessageForAudio messageForAudio = new MessageForAudio();
        MessageForAudio.AudioInfo audioInfo = new MessageForAudio.AudioInfo(duration, size, file.getAbsolutePath(), waveArray);
        messageForAudio.setAudioInfo(audioInfo);
        messageForAudio.setType(type);
        createBaseInfo(messageForAudio, toId, replyBean);
        return messageForAudio;
    }

    public static MessageForDateCard createDateCardMessage(MessageForDateCard.DateCardInfo info, String chatId, int type) {
        MessageForDateCard messageForDateCard = new MessageForDateCard();
        messageForDateCard.setDateCardInfo(info);
        messageForDateCard.setType(type);
        createBaseInfo(messageForDateCard, chatId, null);
        return messageForDateCard;
    }

    public static MessageForLoveCard createLoveCardMessage(MessageForLoveCard.LoveCardInfo info, String chatId, int type) {
        MessageForLoveCard messageForLoveCard = new MessageForLoveCard();
        messageForLoveCard.setLoveCardInfo(info);
        messageForLoveCard.setType(type);
        createBaseInfo(messageForLoveCard, chatId, null);
        return messageForLoveCard;
    }

    public static MessageForNotifyCard createNotifyCardMessage(MessageForNotifyCard.NotifyCardInfo info, String chatId, int type) {
        MessageForNotifyCard messageForNotifyCard = new MessageForNotifyCard();
        messageForNotifyCard.setNotifyCardInfo(info);
        messageForNotifyCard.setType(type);
        createBaseInfo(messageForNotifyCard, chatId, null);
        return messageForNotifyCard;
    }


    //    public static MessageForFile createFileMessage(File file, String url, String name, long size, long toId, int type, ReplyBean replyBean) {
//        MessageForFile msg = new MessageForFile();
//        msg.setLocalPath(file);
//        msg.setUrl(url);
//        msg.setName(name);
//        msg.setSize(size);
//        msg.setType(type);
//        createBaseInfo(msg, toId, replyBean);
//        return msg;
//    }
//
//    public static MessageForFile createFileMessage(String path, String url, String name, long size, long toId, int type, ReplyBean replyBean) {
//        MessageForFile msg = new MessageForFile();
//        msg.setLocalPath(path);
//        msg.setUrl(url);
//        msg.setName(name);
//        msg.setSize(size);
//        msg.setType(type);
//        createBaseInfo(msg, toId, replyBean);
//        return msg;
//    }
//
//    public static MessageForSticker createSticker(GifItem gifItem, long packageId, long toId, int type, ReplyBean replyBean) {
//        MessageForSticker sticker = new MessageForSticker();
//        ImageInfo tiny = createTinyImageInfo(gifItem);
//        ImageInfo origin = createOriginImageInfo(gifItem);
//        MessageForSticker.StickerInfo stickerInfo = new MessageForSticker.StickerInfo(gifItem.getId(), packageId, gifItem.getName(), tiny, origin);
//        sticker.setStickerInfo(stickerInfo);
//        sticker.setType(type);
//        createBaseInfo(sticker, toId, replyBean);
//        return sticker;
//    }
//
//    public static MessageForSticker createSticker(ImageInfo tiny, ImageInfo origin, long packageId, long gifId, String gifName, long toId, int type, ReplyBean replyBean) {
//        MessageForSticker sticker = new MessageForSticker();
//        MessageForSticker.StickerInfo stickerInfo = new MessageForSticker.StickerInfo(gifId, packageId, gifName, tiny, origin);
//        sticker.setStickerInfo(stickerInfo);
//        sticker.setType(type);
//        createBaseInfo(sticker, toId, replyBean);
//        return sticker;
//    }
//
//    private static ImageInfo createTinyImageInfo(GifItem gifItem) {
//        ImageInfo imageInfo = new ImageInfo();
//        imageInfo.setUrl(gifItem.getUri());
//        imageInfo.setWidth(gifItem.getTinyWidth());
//        imageInfo.setHeight(gifItem.getTinyHeight());
//        return imageInfo;
//    }
//
//    private static ImageInfo createOriginImageInfo(GifItem gifItem) {
//        ImageInfo imageInfo = new ImageInfo();
//        imageInfo.setUrl(gifItem.getUri());
//        imageInfo.setWidth(gifItem.getOrigWidth());
//        imageInfo.setHeight(gifItem.getOrigHeight());
//        return imageInfo;
//    }
//
//    public static ChatMessage createForward(ChatMessage origin, String shareId, long toId, int type, ReplyBean replyBean) {
//        ChatMessage chatMessage;
//        if (origin.getMediaType() != MessageConstants.MSG_AUDIO) {
//            chatMessage = MessageFactory.createMessage((byte) origin.getMediaType());
//        } else {
//            chatMessage = MessageFactory.copyMessageForSend(toId, type, origin);
//        }
//        MessageFactory.createBaseInfo(chatMessage, toId, replyBean);
//        chatMessage.setData(origin.getData());
//        chatMessage.setExtStr(origin.getExtStr());
//        if (origin.getMediaType() == chatMessage.getMediaType()) {
//            if (chatMessage instanceof MessageForText || chatMessage instanceof MessageForRichText) {//转发单挑文案消息时，过滤标示@特殊字符
//                chatMessage.setContent(MessageUtils.cleanAtStartEnd(origin.getContent()));
//            } else {
//                chatMessage.setContent(origin.getContent());
//            }
//        }
//
//        chatMessage.parseFromDB();
//
//        chatMessage.setType(type);
//        if (origin.getMediaType() == MessageConstants.MSG_CHAT_SHARE) {
//            ((MessageForChatShare) chatMessage).setShareId(shareId);
//            if (((MessageForChatShare) chatMessage).getChatShareInfo().getRootMid() <= 0) {//1.3版本之前没有rootmid
//                ((MessageForChatShare) chatMessage).getChatShareInfo().setRootMid(origin.getMid());
//            }
//        }
//        return chatMessage;
//    }
//
//    public static MessageForChatShare createChatShare(MessageForChatShare.ChatShareInfo chatShareInfo, long toId, int type, ReplyBean replyBean) {
//        MessageForChatShare chatShare = new MessageForChatShare();
//        chatShare.setChatShareInfo(chatShareInfo);
//        createBaseInfo(chatShare, toId, replyBean);
//        chatShare.setType(type);
//        return chatShare;
//    }
//
//    public static MessageForRedEnvelope createRedEnvelope(long mTicketId, int type, String note, long totalMoney, long toId, int chatType, ReplyBean replyBean, long themeId, String coverUrl) {
//        MessageForRedEnvelope envelope = new MessageForRedEnvelope();
//        MessageForRedEnvelope.RedBEnvelopeInfo info = new MessageForRedEnvelope.RedBEnvelopeInfo(mTicketId, type, note, totalMoney, themeId, coverUrl);
//        envelope.setType(chatType);
//        envelope.setRedEnvelope(info);
//        createBaseInfo(envelope, toId, replyBean);
//        return envelope;
//    }
//
//    public static MessageForLink createLinkMessage(MessageForLink.LinkInfo linkInfo, long chatId, int type, ReplyBean replyBean) {
//        MessageForLink msg = new MessageForLink();
//        msg.setLinkInfo(linkInfo);
//        msg.setType(type);
//        createBaseInfo(msg, chatId, replyBean);
//        return msg;
//    }
//
//    public static MessageForRichText createRichTextMessage(String title, String content, long chatId, int type, ReplyBean replyBean, List<Long> atIds) {
//        MessageForRichText msg = new MessageForRichText();
//        msg.setContent(content);
//        msg.setType(type);
//        msg.setRichInfo(title, content, atIds, new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
//        createBaseInfo(msg, chatId, replyBean);
//        return msg;
//    }
//
//    public static MessageForImageCard createImageCardMessage(MessageForImageCard.ImageCardInfo imageCardInfo, long chatId, int type) {
//        MessageForImageCard msg = new MessageForImageCard();
//        msg.setInfo(imageCardInfo.original, imageCardInfo.protocol, imageCardInfo.text);
//        msg.setType(type);
//        createBaseInfo(msg, chatId, null);
//        return msg;
//    }
//
//    public static void copeBaseInfo(ChatMessage message, ChatMessage oldMessage) {
//        message.setMid(oldMessage.getMid());
//        message.setSender(oldMessage.getSender());
//        message.setCmid(oldMessage.getCmid());
//        message.setTime(oldMessage.getTime());
//        message.setStatus(oldMessage.getStatus());
//        message.setContent(oldMessage.getContent());
//        message.setSeq(oldMessage.getSeq());
//        message.setType(oldMessage.getType());
//    }
//
    public static MessageForLinkCall createLinkCallMessage(MessageForLinkCall.LinkCallInfo info, String chatId, int type, ReplyBean replyBean) {
        MessageForLinkCall messageForLinkCall = new MessageForLinkCall();
        messageForLinkCall.setLinkTime(info.getTime());
        messageForLinkCall.setLinkType(info.getLinkType());
        createBaseInfo(messageForLinkCall, chatId, replyBean);
        return messageForLinkCall;
    }
//
//    public static MessageForOnlineFile createOnlineFileMessage(MessageForOnlineFile.OnlineFileInfo info, long chatId, int type, ReplyBean replyBean) {
//        MessageForOnlineFile msg = new MessageForOnlineFile();
//        msg.setFileInfo(info);
//        msg.setType(type);
//        createBaseInfo(msg, chatId, replyBean);
//        return msg;
//    }
}
