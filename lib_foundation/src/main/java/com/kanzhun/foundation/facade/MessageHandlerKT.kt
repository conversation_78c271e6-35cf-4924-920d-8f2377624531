package com.kanzhun.foundation.facade

import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.utils.L

class MessageHandlerKT {
    companion object{
        fun handlerMessage():List<Long>{
            var list = ServiceManager.getInstance().messageService.queryObsoleteMessage()
            var list2 = ServiceManager.getInstance().messageService.queryUpdateMessage()
            L.e("MessageHandlerKT list1",list.toString())
            L.e("MessageHandlerKT list2",list2.toString())
            val list3 = (list + list2).toSet().toList()
            L.e("MessageHandlerKT list3",list3.toString())
            return list3
        }
    }
}