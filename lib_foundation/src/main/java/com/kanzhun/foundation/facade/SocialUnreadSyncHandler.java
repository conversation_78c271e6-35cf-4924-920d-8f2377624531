package com.kanzhun.foundation.facade;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.SocialTotalUnreadResponse;
import com.kanzhun.foundation.api.model.SocialUnreadResponse;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.response.BaseResponse;

import io.reactivex.rxjava3.core.Observable;

public class SocialUnreadSyncHandler extends BaseHandler<SocialTotalUnreadResponse>{
    private MutableLiveData<SocialUnreadResponse> socialUnreadCount = new MutableLiveData<>();

    public SocialUnreadSyncHandler() {

    }

    @Override
    protected void syncData() {
        Observable<BaseResponse<SocialTotalUnreadResponse>> responseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).requestSocialUnreadCount();
        HttpExecutor.execute(responseObservable, this);
    }

    @Override
    public void handleInChildThread(SocialTotalUnreadResponse data) {
        super.handleInChildThread(data);
        if (data != null) {
            socialUnreadCount.postValue(data.unread);
        }
//        SocialUnreadResponse response = new SocialUnreadResponse();
//        response.unreadCount = 3;
//        response.interestUnreadCount = 1;
//        response.interactUnreadCount = 2;
//        socialUnreadCount.postValue(response);
    }

    public MutableLiveData<SocialUnreadResponse> getSocialUnreadCount() {
        return socialUnreadCount;
    }

    public void setSocialUnreadCount(SocialUnreadResponse response) {
        socialUnreadCount.postValue(response);
    }
}
