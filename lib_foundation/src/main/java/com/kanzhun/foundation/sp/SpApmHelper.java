package com.kanzhun.foundation.sp;


/**
 * <AUTHOR>
 * @date 2021/9/22
 **/
public class SpApmHelper {

    public static final String ACTION_MMKV_ERROR = "action_mmkv_error";
    public static final String TYPE_LENGTH_ERROR = "type_data_length_error";
    public static final String TYPE_DATA_CYC_FAILED = "type_data_cyc_failed";
    private boolean isApmInit = false;
    private final StringBuilder sb = new StringBuilder();

    // TODO: 2022/2/7 initAPm 调用
    public synchronized void onApmInit() {
        isApmInit = true;
        if (sb.length() > 0) {
            reportToApm(ACTION_MMKV_ERROR, sb.toString());
            sb.setLength(0);
        }
    }

    private SpApmHelper() {
    }

    private static class SingleTonHolder {
        private static final SpApmHelper INSTANCE = new SpApmHelper();
    }

    public static SpApmHelper getInstance() {
        return SingleTonHolder.INSTANCE;
    }

    public void reportToApm(String errorType, String mmapId) {

        if (isApmInit) {
            // TODO: 2022/2/7 APM介入
//            ApmAnalyzer.create().action(ACTION_MMKV_ERRO, errorType).
//                    param("p2", mmapId).
//                    report();
        } else {
            sb.append(errorType).append(" : ").append(errorType).append("\n");
        }


    }
}
