package com.kanzhun.foundation.photoselect.upload

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.api.model.ImageUploadModel
import com.kanzhun.foundation.utils.UploadFileUtil
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.upload.UploadRequestCallback

class CommonUploadStrategy : IUploadStrategy {

    override fun upload(liveData: MutableLiveData<UploadResult>, sceneType: UploadSceneType, uri: Uri,cropInfo: UploadCropInfo?) {
        UploadFileUtil.uploadImage(sceneType.value, uri, object : UploadRequestCallback<ImageUploadModel>() {
            override fun onSuccess(data: ImageUploadModel?) {
                if(data == null){
                    liveData.value = UploadResult(UploadStatus.FAIL)
                }else{
                    liveData.value = UploadResult(UploadStatus.SUCCESS,data)
                }
            }

            override fun dealFail(reason: ErrorReason?) {
                liveData.value = UploadResult(UploadStatus.FAIL)
            }
        })
    }
}