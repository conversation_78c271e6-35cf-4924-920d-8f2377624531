package com.kanzhun.foundation.photoselect;

import android.content.Context;

import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.IncapableCause;
import com.zhihu.matisse.internal.entity.Item;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/5.
 * @noinspection JavadocDeclaration
 */
public class VideoLengthFilter extends Filter {

    private final int videoLengthMin;
    private final int videoLengthMax;

    /**
     * @param videoLengthMin 单位：秒
     * @param videoLengthMax 单位：秒
     */
    public VideoLengthFilter(int videoLengthMin, int videoLengthMax) {
        this.videoLengthMin = videoLengthMin;
        this.videoLengthMax = videoLengthMax;
    }

    @Override
    protected Set<MimeType> constraintTypes() {
        return MimeType.ofVideo();
    }

    @Override
    public IncapableCause filter(Context context, Item item) {
        if (!needFiltering(context, item)) {
            return null;
        }

        long duration = item.duration;
        if (duration > videoLengthMax * 1000L || duration < videoLengthMin * 1000L) {
            String msg;
            if (videoLengthMin == 0) {
                msg = "视频时长需要小于" + videoLengthMax + "s哦";
            } else {
                // 请选择时长在3~20s之间的视频
                msg = "请选择时长在" + videoLengthMin + "~" + videoLengthMax + "s之间的视频";
            }
            return new IncapableCause(IncapableCause.TOAST, msg);
        }

        return null;
    }

}
