package com.kanzhun.foundation.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.kanzhun.foundation.R;
import com.kanzhun.foundation.api.bean.UserInfoStoryBean;
import com.kanzhun.foundation.databinding.CommonLayoutStoryVideoBinding;
import com.kanzhun.foundation.page.VideoAudioFloatPageManager;
import com.kanzhun.foundation.player.OPlayerHelper;
import com.kanzhun.foundation.player.OViewRenderer;
import com.kanzhun.utils.AudienceUtils;
import com.kanzhun.utils.T;
import com.kanzhun.utils.platform.Utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng
 * Date: 2022/7/4
 */
public class StoryVideoView extends ConstraintLayout {

    private CommonLayoutStoryVideoBinding binding;
    private UserInfoStoryBean videoStoryBean;
    private OPlayerHelper oPlayerHelper;


    public StoryVideoView(@NonNull Context context) {
        this(context, null);
    }

    public StoryVideoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public StoryVideoView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.common_layout_story_video, this, true);
    }

    public void setVideoStoryBean(UserInfoStoryBean videoStoryBean) {
        this.videoStoryBean = videoStoryBean;
        binding.setItem(this.videoStoryBean);
        setPlayerHelper(videoStoryBean.oPlayerHelper);
    }

    private OPlayerHelper getPlayerHelper() {
        return oPlayerHelper;
    }

    public void setPlayerHelper(OPlayerHelper oPlayerHelper){
        this.oPlayerHelper = oPlayerHelper;
    }

    public void setVideo() {
        if (this.oPlayerHelper != null) {
            this.oPlayerHelper.setCallBack(null);
            int currentPlayState = this.oPlayerHelper.getCurrentPlayState();
            if (currentPlayState == OPlayerHelper.STATE_PLAYING || currentPlayState == OPlayerHelper.STATE_PAUSED) {
                if (binding.svVideo.getVisibility() != View.VISIBLE) {
//                binding.ivPhoto.setVisibility(View.GONE);
                    binding.svVideo.setVisibility(View.VISIBLE);
                }
            }
            oPlayerHelper.setCallBack(new OPlayerHelper.CallBack() {
                @Override
                public void onFirstFrame() {
//                binding.ivPhoto.setVisibility(View.GONE);
                    binding.svVideo.setVisibility(View.VISIBLE);
                }
            });
            OViewRenderer viewRender = oPlayerHelper.getViewRender();
            if (viewRender == null) {
                viewRender = new OViewRenderer(Utils.getApp());
            }
            ViewParent viewRendererParent = viewRender.getParent();
            if (viewRendererParent != binding.svVideo) {
                if (viewRendererParent instanceof ViewGroup) {
                    viewRender.release();
                    ((ViewGroup) viewRendererParent).removeView(viewRender);
                }
                binding.svVideo.addView(viewRender, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            }
            oPlayerHelper.setPlayView(viewRender);
            oPlayerHelper.setMute(videoStoryBean.silence.get());
            binding.ivVoice.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean newVoiceStatus = !videoStoryBean.silence.get();
                    if (!VideoAudioFloatPageManager.getInstance().hasNoFloatPage() && !newVoiceStatus) {
                        T.ss("您正在通话中，请稍后再试");
                        return;
                    }
                    videoStoryBean.silence.set(newVoiceStatus);
                    getPlayerHelper().setMute(newVoiceStatus);
                }
            });
        }
    }

    public View getPhotoView() {
        return binding.ivPhoto;
    }

    public boolean isPlaying(){
        if(this.oPlayerHelper != null){
            return this.oPlayerHelper.isPlaying();
        }
        return false;
    }

    public void release(){
        OViewRenderer zpViewRenderer = null;
        if (oPlayerHelper != null) {
            oPlayerHelper.setCallBack(null);
            oPlayerHelper.stop();
            oPlayerHelper.onDestroy();
            zpViewRenderer = oPlayerHelper.getViewRender();
            oPlayerHelper = null;
        }
        if (zpViewRenderer != null) {
            AudienceUtils.removeFromParent(zpViewRenderer);
            zpViewRenderer.release();
            zpViewRenderer = null;
        }
    }
}
