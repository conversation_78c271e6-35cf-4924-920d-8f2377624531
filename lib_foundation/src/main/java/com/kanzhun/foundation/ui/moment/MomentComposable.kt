package com.kanzhun.foundation.ui.moment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.bean.MomentListItemBean
import com.kanzhun.foundation.api.bean.PictureListItemBean

@Composable
fun MomentItem(bean: MomentListItemBean) {
    Row {
        Text(
            text = buildAnnotatedString {
                withStyle(
                    style = SpanStyle(
                        fontSize = 28.sp,
                    )
                ) {
                    // month to 00 format
                    append("${bean.createMonth().toString().padStart(2, '0')}.")
                }

                withStyle(
                    style = SpanStyle(
                        fontSize = 18.sp,
                    )
                ) {
                    append(bean.createDayOfMonth().toString().padStart(2, '0'))
                }
            },
            style = TextStyle(
                fontSize = 28.sp,
                color = colorResource(R.color.common_color_191919),
                fontFamily = FontFamily(Font(resId = R.font.source_han_serif_cn_bold)),
                fontWeight = FontWeight(700),
            ),
        )

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = bean.content ?: "",
            maxLines = 3,
            overflow = TextOverflow.Ellipsis,
            style = TextStyle(
                fontSize = 14.sp,
                color = colorResource(R.color.common_color_B2B2B2),
                fontWeight = FontWeight(400)
            ),
            modifier = Modifier
                .weight(1f)
                .padding(top = 6.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        val pictures = bean.pictures
        if (!pictures.isNullOrEmpty()) {
            GridImage(pictures, modifier = Modifier.padding(top = 9.dp))
        }
    }
}

@Composable
fun GridImage(pictures: List<PictureListItemBean>, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .size(57.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        val size = pictures.size
        when (size) {
            1 -> {
                AsyncImage(
                    model = pictures.first().url,
                    contentScale = ContentScale.Crop,
                    contentDescription = null,
                    modifier = Modifier.background(Color.LightGray)
                )
            }

            2 -> {
                Row {
                    AsyncImage(
                        model = pictures[0].url,
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .background(Color.LightGray)
                            .weight(1f)
                    )

                    Spacer(modifier = Modifier.width(1.dp))

                    AsyncImage(
                        model = pictures[1].url,
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .background(Color.LightGray)
                            .weight(1f)
                    )
                }
            }

            3 -> {
                Row {
                    AsyncImage(
                        model = pictures[0].url,
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .background(Color.LightGray)
                            .weight(1f)
                    )

                    Spacer(modifier = Modifier.width(1.dp))

                    Column(modifier = Modifier.weight(1f)) {
                        AsyncImage(
                            model = pictures[1].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.height(1.dp))

                        AsyncImage(
                            model = pictures[2].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }
                }
            }

            4 -> {
                Column {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = pictures[0].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[1].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }

                    Spacer(modifier = Modifier.height(1.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = pictures[2].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[3].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }
                }
            }

            5 -> {
                Column {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(38.dp)
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = pictures[0].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[1].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }

                    Spacer(modifier = Modifier.height(1.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(18.dp)
                    ) {
                        AsyncImage(
                            model = pictures[2].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[3].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[4].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }
                }
            }

            6 -> {
                Column {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(38.dp)
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = pictures[0].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            AsyncImage(
                                model = pictures[1].url,
                                contentScale = ContentScale.Crop,
                                contentDescription = null,
                                modifier = Modifier
                                    .background(Color.LightGray)
                                    .weight(1f)
                            )

                            Spacer(modifier = Modifier.height(1.dp))

                            AsyncImage(
                                model = pictures[2].url,
                                contentScale = ContentScale.Crop,
                                contentDescription = null,
                                modifier = Modifier
                                    .background(Color.LightGray)
                                    .weight(1f)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(1.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(18.dp)
                    ) {
                        AsyncImage(
                            model = pictures[3].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[4].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )

                        Spacer(modifier = Modifier.width(1.dp))

                        AsyncImage(
                            model = pictures[5].url,
                            contentScale = ContentScale.Crop,
                            contentDescription = null,
                            modifier = Modifier
                                .background(Color.LightGray)
                                .weight(1f)
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewMomentItem() {
    MomentItem(MomentListItemBean().apply {
        createTime = 1730649600000
        content =
            "今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错今天天气不错"
        pictures = mutableListOf<PictureListItemBean>().apply {
            repeat(6) {
                add(PictureListItemBean().apply {
                    url =
                        "https://q9.itc.cn/q_70/images03/20240414/e5577ff4aed54c1190fe5b8505e232bc.jpeg"
                })
            }
        }
    })
}