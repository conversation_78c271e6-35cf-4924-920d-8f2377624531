package com.kanzhun.foundation.ui.popup;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.widget.ImageView;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * ZPUI弹窗的样式
 *
 * <AUTHOR>
 */
public class ZPUIPopupArrowView extends ConstraintLayout {
    private Context mContext;

    public ZPUIPopupArrowView(Context context) {
        this(context, null);
    }

    public ZPUIPopupArrowView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ZPUIPopupArrowView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
    }

    public void setContentView(@LayoutRes int viewIds) {
        inflate(getContext(),viewIds, this);
    }

    /**
     * 设置箭头的方向和位置
     *
     * @param gravity 使用 {@link Gravity}中的属性即可，只支持left,right,top,bottom
     * @param margin  根据gravity所设置的方向设置的边距。如果是Gravity.LEFT，就是LeftMargin，如果是Gravity.RIGHT，就是RightMargin
     */
    public ImageView setArrowPosition(int gravity, int margin,@IdRes int container) {
        ImageView ivArrow = new ImageView(mContext);
        addView(ivArrow);
        LayoutParams layoutParams = (LayoutParams) ivArrow.getLayoutParams();
        layoutParams.height = LayoutParams.WRAP_CONTENT;
        layoutParams.width = LayoutParams.WRAP_CONTENT;

        final int verticalGravity = gravity & Gravity.VERTICAL_GRAVITY_MASK;

        switch (gravity & Gravity.HORIZONTAL_GRAVITY_MASK) {
            case Gravity.RIGHT:
                // 靠右
                layoutParams.rightMargin = margin;
                layoutParams.rightToRight = container;
                break;
            case Gravity.LEFT:
            default:
                // 靠左
                layoutParams.leftMargin = margin;
                layoutParams.leftToLeft = container;
                break;
        }

        switch (verticalGravity) {
            case Gravity.BOTTOM:
                // 在锚点的上方
                layoutParams.bottomToTop = container;
                break;
            case Gravity.TOP:
                // 在锚点的下方
                layoutParams.topToBottom = container;
                break;
            default:
                break;
        }
        ivArrow.setLayoutParams(layoutParams);
        return ivArrow;
    }

}
