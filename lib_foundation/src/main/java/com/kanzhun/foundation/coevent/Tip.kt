package com.kanzhun.foundation.coevent

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.foundation.R
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.MessageForTacitTestCard
import com.kanzhun.foundation.model.message.TYPE_TACIT_TEST_CARD_UNLOCK
import com.kanzhun.foundation.model.message.TacitTestCard
import kotlinx.coroutines.delay

@Composable
fun CoEventTip(showText: String = "") {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .border(width = 3.dp, color = Color.White, shape = RoundedCornerShape(16.dp))
            .background(
                brush = Brush.horizontalGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFF61EEFF),
                        0.5f to Color(0xFFFFFFFF),
                        1.0f to Color(0xFFFE6FDD),
                    )
                ),
                shape = RoundedCornerShape(16.dp)
            ),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.mipmap.f_ic_coevent_tag),
            contentDescription = "image description",
            modifier = Modifier
                .padding(start = 16.dp)
                .size(20.dp),
        )

        /*
            展示时间及文案：
                活动开始前（取活动开始时间T）：Ta即将和你参加同场活动
                活动开始后的7日内（取活动开始时间T～T+7）：Ta和你参与了同场活动
         */
        Text(
            text = showText,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF292929),
            ),
            modifier = Modifier
                .weight(1f)
        )

        Image(
            painter = painterResource(id = R.mipmap.f_ic_coevent_arrow_cover),
            contentDescription = "image description",
            modifier = Modifier.aspectRatio(564f / 269)
        )
    }
}

@Preview
@Composable
private fun PreviewCoEventTip() {
    CoEventTip(showText = "Ta即将和你参与同一场活动")
}

