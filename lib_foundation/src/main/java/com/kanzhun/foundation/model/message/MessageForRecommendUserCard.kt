package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.techwolf.lib.tlog.TLog
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 推荐用户卡片消息
 */
private const val TAG = "MessageForRecommendUser"

class MessageForRecommendUserCard : ChatMessage() {
    var recommendUserCard: RecommendUserCard? = null

    init {
        mediaType = MessageConstants.MSG_MEDIA_TYPE_RECOMMEND_USER_CARD
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val recommendUserCardMedia = message.body?.recommendUserCard ?: return
            // Parse from protocol buffer message
            // Implementation depends on the protocol buffer definition
            recommendUserCard = RecommendUserCard(
                recommendUserId = recommendUserCardMedia.recommendUserId,
                securityId = recommendUserCardMedia.securityId,
                userInfo = try {
                    GsonUtils.getGson()
                        .fromJson(recommendUserCardMedia.userInfoJson, UserCard::class.java)
                } catch (e: Exception) {
                    TLog.error(TAG, "Failed to parse user info JSON: ${e.message}")
                    null
                }
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val card = GsonUtils.getGson().fromJson(content, RecommendUserCard::class.java)
        if (card != null) {
            recommendUserCard = card
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(recommendUserCard)
    }

    override fun getSummary(): String {
        return "[嘉宾卡]" + (recommendUserCard?.userInfo?.nickName ?: "") // 展示「嘉宾卡」用户昵称
    }
}

data class RecommendUserCard(
    val recommendUserId: String? = null, // 推荐用户ID
    val securityId: String? = null, // 安全ID
    val userInfo: UserCard? = null, // 推荐用户卡片
) : Serializable

/**
 * 推荐用户卡片数据结构
 */
/*

    {
      "addressCode" : "yrkYoLdrANDMhAlh3EtDHh8yFl1iHGqbmJ7mpVX4wdE~",
      "addressLevel1" : "北京",
      "addressLevel2" : "朝阳区",
      "age" : 28,
      "annualIncome" : 1,
      "annualIncomeInfo" : "5万以下",
      "avatar" : "https://lengjing-cdn.zhipin.com/azeroth/avatar/250510/4W6WoEeONc2LNy8dUd17iLVrTWFYW0xDZPlfg4qazpOSarSX98gkJ-uZyJluXKZHMFQXpX0amQq6nKSTBLlgXYl6s1OgQF8JjYvYHLX0iZQ~.jpg",
      "birthday" : "1996-09-13",
      "carHold" : 1,
      "career" : "检察官",
      "careerCode" : "gglIYKi_5QgmwVZRkUT3h5BHQQxnOtY3995S3MK8hZM~",
      "certInfo" : {
        "certPassCount" : 2,
        "certTagList" : [ {
          "certType" : 3,
          "content" : "本科·北京航空航天大学",
          "showIcon" : 1
        }, {
          "certType" : 4,
          "content" : "公司·任县财政监督检查局",
          "showIcon" : 1
        }, {
          "certType" : 7,
          "content" : "年薪5万以下",
          "showIcon" : 0
        } ]
      },
      "companyName" : "任县财政监督检查局",
      "companyNameShort" : "任县财政监督检查局",
      "completeTime" : 1744187915000,
      "createTime" : 1706238229000,
      "degree" : 50,
      "degreeInfo" : "本科",
      "gender" : 1,
      "height" : 178,
      "hometownCode" : "ZcGK1hXJKRZaeBQQI-MCD4OWBfCSn8J-AQkGFEt3x6k~",
      "hometownLevel1" : "浙江",
      "hometownLevel2" : "湖州",
      "houseHold" : 1,
      "hukouCode" : "ZcGK1hXJKRZaeBQQI-MCD4OWBfCSn8J-AQkGFEt3x6k~",
      "hukouLevel1" : "浙江",
      "hukouLevel2" : "湖州",
      "industry" : "事业单位",
      "industryCode" : "RIHh_fjCnl2ZgNgke_oujgxb7omBv-vzp9Xw8mz9Gto~",
      "intro" : "额忽大忽小好想好想好好打还查查查查查出发哈",
      "nickName" : "14015",
      "phase" : 6,
      "school" : "北京航空航天大学",
      "schoolTag" : [ 10, 11, 21, 30 ],
      "schoolTime" : 1,
      "schoolType" : 1,
      "securityId" : "H862Gy1k7WWVPDL7S9-QiYqx97QGWlxt5IKnzfQURqmNo7-j-TLRePy-gVYqWrehW51KocX0KbeUriIuHIclsR_i44ysfY1wUvxZTAZEYtOdqWx5ZRQ",
      "status" : 1,
      "tinyAvatar" : "https://lengjing-cdn.zhipin.com/azeroth/avatar/250510/4W6WoEeONc2LNy8dUd17iLVrTWFYW0xDZPlfg4qazpOSarSX98gkJ-uZyJluXKZHMFQXpX0amQq6nKSTBLlgXYl6s1OgQF8JjYvYHLX0iZQ~.jpg",
      "userId" : "UWfQ0D_lkCfgbN8-JkrDMGSKZvknYvwGLbgDwz7MYAY~"
    }

 */
data class UserCard(
    val avatar: String? = null,           // 头像地址
    val tinyAvatar: String? = null,       // 缩略图头像地址
    val nickName: String? = null,         // 昵称
    val age: Int? = null,                 // 年龄
    val gender: Int? = null,              // 性别 1:男 2:女
    val school: String? = null,           // 学校名称
    val degree: Int? = null,              // 学历 10 初中以下 20 中专 30 高中 40 大专 50 本科 60 硕士 70 博士
    val degreeInfo: String? = null,       // 学历信息
    val addressCode: String? = null,      // 现居地码 二级code
    val addressLevel1: String? = null,    // 现居地 省/直辖市
    val addressLevel2: String? = null,    // 现居地 市/直辖市的区
    val height: Int? = null,              // 身高 cm
    val industryCode: String? = null,     // 行业码
    val industry: String? = null,         // 行业
    val careerCode: String? = null,       // 职业码，空为自定义输入内容
    val career: String? = null,           // 职业
    val intro: String? = null,            // 个人简介
    val certInfo: CertInfo? = null        // 认证信息
) : Serializable

/**
 * 认证信息
 */
data class CertInfo(
    val certPassCount: Int? = null,       // 认证通过数量
    val certTagList: List<CertTag>? = null // 认证标签列表
) : Serializable

/**
 * 认证标签
 */
data class CertTag(
    val showIcon: Int? = null,            // 是否展示V图标，1-展示，0-不展示
    val content: String? = null,           // 认证内容
    val certType: Int? = null              // 1-人脸，2-头像，3-学历，4-工作，5-房产，6-车产
) : Serializable