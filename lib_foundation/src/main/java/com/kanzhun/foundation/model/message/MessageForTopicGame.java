package com.kanzhun.foundation.model.message;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.kanzhun.foundation.api.bean.ChatTopicGameAnswer;
import com.kanzhun.foundation.api.bean.ChatTopicGameQuestion;
import com.kanzhun.foundation.api.bean.TopicGameOptionBean;
import com.kanzhun.foundation.utils.StringUtil;
import com.xxjz.orange.protocol.codec.ChatProtocol;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.base.LList;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/15
 */
public class MessageForTopicGame extends ChatMessage {
    private TopicGameInfo topicGameInfo = new TopicGameInfo();

    public MessageForTopicGame() {
        setMediaType(MessageConstants.MSG_TOPIC_GAME);
    }

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgTopicGameMedia topicGame = message.getBody().getTopicGame();
        String question = topicGame.getQuestion();
        topicGameInfo.status = topicGame.getStatus();
        if (!TextUtils.isEmpty(question)) {
            topicGameInfo.question = GsonUtils.getGson().fromJson(question, ChatTopicGameQuestion.class);
        }

        String senderAnswer = topicGame.getSenderAnswer();
        if (!TextUtils.isEmpty(senderAnswer)) {
            topicGameInfo.senderAnswers = GsonUtils.getGson().fromJson(senderAnswer, new TypeToken<List<ChatTopicGameAnswer>>() {
            }.getType());
        }

        String receiverAnswer = topicGame.getReceiverAnswer();
        if (!TextUtils.isEmpty(receiverAnswer)) {
            topicGameInfo.receiverAnswers = GsonUtils.getGson().fromJson(receiverAnswer, new TypeToken<List<ChatTopicGameAnswer>>() {
            }.getType());
        }
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        TopicGameInfo gameInfo = GsonUtils.getGson().fromJson(getContent(), TopicGameInfo.class);
        if (gameInfo != null) {
            topicGameInfo = gameInfo;
        }
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        String content = GsonUtils.getGson().toJson(topicGameInfo);
        setContent(content);
    }

    @Override
    public String getSummary() {
        return "[话题游戏]";
    }

    public String getTitle() {
        return getQuestion().title;
    }

    public String getSenderContent() {
        String content = "";
        ChatTopicGameAnswer senderAnswer = getSenderAnswer();
        if (senderAnswer != null) {
            List<TopicGameOptionBean> optionList = getQuestion().optionList;
            if (!LList.isEmpty(optionList)) {
                for (TopicGameOptionBean topicGameOptionBean : optionList) {
                    if (TextUtils.equals(topicGameOptionBean.id, senderAnswer.optionId)) {
                        content = topicGameOptionBean.content;
                        break;
                    }
                }
            }
        }
        return content;
    }

    public String getSenderLetter() {
        String content = "";
        ChatTopicGameAnswer senderAnswer = getSenderAnswer();
        if (senderAnswer != null) {
            List<TopicGameOptionBean> optionList = getQuestion().optionList;
            if (!LList.isEmpty(optionList)) {
                for (int i = 0; i < optionList.size(); i++) {
                    TopicGameOptionBean topicGameOptionBean = optionList.get(i);
                    if (TextUtils.equals(topicGameOptionBean.id, senderAnswer.optionId)) {
                        content = StringUtil.getTopicGameOptionTitle(i);
                        break;
                    }
                }
            }
        }
        return content;
    }

    public String getReplyLetter() {
        String content = "";
        ChatTopicGameAnswer receiverAnswer = getReceiverAnswer();
        if (receiverAnswer != null) {
            List<TopicGameOptionBean> optionList = getQuestion().optionList;
            if (!LList.isEmpty(optionList)) {
                for (int i = 0; i < optionList.size(); i++) {
                    TopicGameOptionBean topicGameOptionBean = optionList.get(i);
                    if (TextUtils.equals(topicGameOptionBean.id, receiverAnswer.optionId)) {
                        content = StringUtil.getTopicGameOptionTitle(i);
                        break;
                    }
                }
            }
        }
        return content;
    }

    public String getReplyContent() {
        String content = "";
        ChatTopicGameAnswer receiverAnswer = getReceiverAnswer();
        if (receiverAnswer != null) {
            List<TopicGameOptionBean> optionList = getQuestion().optionList;
            if (!LList.isEmpty(optionList)) {
                for (TopicGameOptionBean topicGameOptionBean : optionList) {
                    if (TextUtils.equals(topicGameOptionBean.id, receiverAnswer.optionId)) {
                        content = topicGameOptionBean.content;
                        break;
                    }
                }
            }
        }
        return content;
    }

    public TopicGameInfo getTopicGameInfo() {
        return topicGameInfo;
    }

    public int getTopicGameStatus() {
        return topicGameInfo.getStatus();
    }

    public ChatTopicGameQuestion getQuestion() {
        return topicGameInfo.getQuestion();
    }

    public ChatTopicGameAnswer getReceiverAnswer() {
        List<ChatTopicGameAnswer> receiverAnswers = topicGameInfo.getReceiverAnswers();
        if (!LList.isEmpty(receiverAnswers)) {
            return receiverAnswers.get(0);
        }
        return new ChatTopicGameAnswer();
    }

    public ChatTopicGameAnswer getSenderAnswer() {
        List<ChatTopicGameAnswer> senderAnswers = topicGameInfo.getSenderAnswers();
        if (!LList.isEmpty(senderAnswers)) {
            return senderAnswers.get(0);
        }
        return new ChatTopicGameAnswer();
    }


    public static class TopicGameInfo implements Serializable {
        public static final int STATUS_OTHER_NO = 1;
        public static final int STATUS_OTHER_DO = 2;
        private static final long serialVersionUID = 7258582191324971443L;
        private int status; //  1-对方待作答, 2-对方已作答
        private ChatTopicGameQuestion question;
        private List<ChatTopicGameAnswer> senderAnswers;
        private List<ChatTopicGameAnswer> receiverAnswers;

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public ChatTopicGameQuestion getQuestion() {
            return question;
        }

        public void setQuestion(ChatTopicGameQuestion question) {
            this.question = question;
        }

        public List<ChatTopicGameAnswer> getSenderAnswers() {
            return senderAnswers;
        }

        public List<ChatTopicGameAnswer> getReceiverAnswers() {
            return receiverAnswers;
        }
    }
}
