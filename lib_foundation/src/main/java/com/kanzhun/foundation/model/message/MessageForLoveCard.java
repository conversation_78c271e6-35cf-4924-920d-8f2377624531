package com.kanzhun.foundation.model.message;

import com.xxjz.orange.protocol.codec.ChatProtocol;
import com.kanzhun.utils.GsonUtils;

import java.io.Serializable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/31
 */
public class MessageForLoveCard extends ChatMessage {

    private MessageForLoveCard.LoveCardInfo mLoveCardInfo = new MessageForLoveCard.LoveCardInfo();

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgLoveCardMedia loveCard = message.getBody().getLoveCard();
        setLoveCardInfo(new LoveCardInfo(loveCard.getReplyStatus()));
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        mLoveCardInfo = GsonUtils.getGson().fromJson(getContent(), MessageForLoveCard.LoveCardInfo.class);
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        String content = GsonUtils.getGson().toJson(mLoveCardInfo);
        setContent(content);
    }

    public LoveCardInfo getLoveCardInfo() {
        return mLoveCardInfo;
    }

    public void setLoveCardInfo(LoveCardInfo mLoveCardInfo) {
        this.mLoveCardInfo = mLoveCardInfo;
    }

    public MessageForLoveCard() {
        setMediaType(MessageConstants.MSG_LOVE_CARD);
    }

    @Override
    public String getSummary() {
        return "[表白信]";
    }

    public static class LoveCardInfo implements Serializable {
        private static final long serialVersionUID = -1300604908728850722L;
        private int replyStatus;// 表白信状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时

        public LoveCardInfo() {
        }

        public LoveCardInfo(int replyStatus) {
            this.replyStatus = replyStatus;
        }

        public int getReplyStatus() {
            return replyStatus;
        }

        public void setReplyStatus(int replyStatus) {
            this.replyStatus = replyStatus;
        }
    }
}
