package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.techwolf.lib.tlog.TLog
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

private const val TAG = "MessageForActivityMedia"

/**
 * 公共卡片： 1 话题游戏提示卡片
 */
class MessageForActivityMedia : ChatMessage() {

    var ogActivityMedia: OgActivityMedia? = null

    init {
        mediaType = MessageConstants.MSG_OFFICIAL_ACTIVITY_CARD
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val activity = message.body?.activity ?: return
            ogActivityMedia = OgActivityMedia(
                name = activity.name,
                title = activity.title,
                img = activity.img,
                content = activity.content,
                linkTitle = activity.linkTitle,
                linkUrl = activity.linkUrl,
                endTime = activity.endTime,
                status = activity.status,
                adminPushId = activity.adminPushId,
                pushType = activity.pushType,
                expireTip = activity.expireTip
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val cardInfo = try {
            GsonUtils.getGson().fromJson(content, OgActivityMedia::class.java)
        } catch (e: Throwable) {
            TLog.error(TAG, "parseFromDB: $e")
            null
        }

        if (cardInfo != null) {
            ogActivityMedia = cardInfo
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(ogActivityMedia)
    }

    override fun getSummary(): String {
        if (ogActivityMedia == null) {
            return "\uD83D\uDCE2 收到了一个新的活动邀请！ \uD83C\uDF89点击查看，不要错过这个精彩活动！\uD83D\uDC40"
        }
        if (ogActivityMedia!!.title?.isNotEmpty() == true) {
            return ogActivityMedia?.title.toString()
        }
        if (ogActivityMedia!!.content?.isNotEmpty() == true) {
            return ogActivityMedia?.content.toString()
        }
        return "\uD83D\uDCE2 收到了一个新的活动邀请！ \uD83C\uDF89点击查看，不要错过这个精彩活动！\uD83D\uDC40"
    }
}

data class OgActivityMedia(
    val name: String? = null,//名称
    val title: String? = null,//标题
    val img: String? = null,//图片
    val content: String? = null,//描述
    val linkTitle: String? = null,//链接文案
    val linkUrl: String? = null,//链接地址
    val endTime: Long,//结束时间
    var status: Int,//状态 0:未读  1:已读
    var adminPushId: Long,
    var pushType: Int,
    val expireTip: String? = null
) : Serializable