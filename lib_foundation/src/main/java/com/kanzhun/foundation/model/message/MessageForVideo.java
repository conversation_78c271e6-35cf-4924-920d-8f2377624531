package com.kanzhun.foundation.model.message;

import android.text.TextUtils;
import android.util.Log;

import com.kanzhun.common.model.ImageInfo;
import com.kanzhun.utils.GsonUtils;
import com.xxjz.orange.protocol.codec.ChatProtocol;

import java.io.Serializable;

public class MessageForVideo extends ChatMessage {
    private static final String TAG = "MessageForVideo";

    private VideoInfo mVideoInfo;

    public MessageForVideo() {
        setMediaType(MessageConstants.MSG_VIDEO);
    }

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgVideoMedia hiVideoMedia = message.getBody().getVideo();
        setVideoInfo(new VideoInfo(hiVideoMedia.getUrl(), hiVideoMedia.getSize(), hiVideoMedia.getDuration(),
                new ImageInfo(hiVideoMedia.getCoverImage().getWidth(), hiVideoMedia.getCoverImage().getHeight()
                        , hiVideoMedia.getCoverImage().getUrl())));
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        // TODO: 2022/5/31
        VideoInfo videoInfo = GsonUtils.getGson().fromJson(getContent(), VideoInfo.class);
        if (videoInfo != null) {
            mVideoInfo = videoInfo;
        }
    }

    public VideoInfo getVideoInfo() {
        return mVideoInfo;
    }

    public void setVideoInfo(VideoInfo videoInfo) {
        mVideoInfo = videoInfo;
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        String content = GsonUtils.getGson().toJson(mVideoInfo);
        Log.d(TAG, "prepare2DB() called:" + content);
        setContent(content);
    }

    @Override
    public String getSummary() {
        return "[视频]";
    }


    public String getUrl() {
        if (null == mVideoInfo || TextUtils.isEmpty(mVideoInfo.getUrl())) {
            return "";
        }
        return mVideoInfo.getUrl();
    }

    public String getLocalPath() {
        if (null == mVideoInfo || TextUtils.isEmpty(mVideoInfo.getLocalPath())) {
            return "";
        }
        return mVideoInfo.getLocalPath();
    }

    public String getVideoPlayPath() {
        if (null == mVideoInfo) {
            return "";
        }
        return TextUtils.isEmpty(mVideoInfo.getLocalPath()) ? mVideoInfo.getUrl() : mVideoInfo.getLocalPath();
    }

    public long getSize() {
        if (null == mVideoInfo) {
            return 0;
        }
        return mVideoInfo.getSize();
    }

    public int getDuration() {
        if (null == mVideoInfo) {
            return 0;
        }
        return mVideoInfo.getDuration();
    }

    public ImageInfo getThumbnail() {
        if (null == mVideoInfo) {
            return null;
        }
        return mVideoInfo.getThumbnail();
    }

    public static class VideoInfo implements Serializable {
        private static final long serialVersionUID = -4813725358720473873L;
        private String url;
        private long size;
        private int duration;
        private String localPath;
        private ImageInfo thumbnail;

        public VideoInfo() {
        }

        public VideoInfo(String url, long size, int duration, ImageInfo thumbnail) {
            this.url = url;
            this.size = size;
            this.duration = duration;
            this.thumbnail = thumbnail;
        }

        public VideoInfo(long size, int duration, String localPath) {
            this.size = size;
            this.duration = duration;
            this.localPath = localPath;
        }

        public VideoInfo(String url, String localPath, long size, int duration, ImageInfo thumbnail) {
            this.url = url;
            this.size = size;
            this.duration = duration;
            this.thumbnail = thumbnail;
            this.localPath = localPath;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public long getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public String getLocalPath() {
            return localPath;
        }

        public void setLocalPath(String localPath) {
            this.localPath = localPath;
        }

        public ImageInfo getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(ImageInfo thumbnail) {
            this.thumbnail = thumbnail;
        }
    }
}
