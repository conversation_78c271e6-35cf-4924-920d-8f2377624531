package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 推荐开聊消息
 */
private const val TAG = "MessageForRecommendOpenChat"

class MessageForRecommendOpenChat : ChatMessage() {
    var recommendOpenChat: RecommendOpenChat? = null

    init {
        mediaType = MessageConstants.MSG_MEDIA_TYPE_RECOMMEND_OPEN_CHAT
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val openChatMedia = message.body?.recommendOpenChat ?: return
            // Parse from protocol buffer message
            recommendOpenChat = RecommendOpenChat(
                recommendUserId = openChatMedia.recommendUserId,
                securityId = openChatMedia.securityId,
                content = openChatMedia.content,
                relationStatus = openChatMedia.relationStatus
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val openChat = GsonUtils.getGson().fromJson(content, RecommendOpenChat::class.java)
        if (openChat != null) {
            recommendOpenChat = openChat
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(recommendOpenChat)
    }

    override fun getSummary(): String {
        return recommendOpenChat?.content ?: "[开聊消息]"
    }
}

/**
 * 推荐开聊消息数据结构
 */
data class RecommendOpenChat(
    val recommendUserId: String? = null,   // 推荐用户id
    val securityId: String? = null,         // 安全ID
    val content: String? = null,           // 推荐开聊文案
    val relationStatus: Int = 0            // 关系：0 无关系 11-你喜欢TA，12-TA喜欢你，20-普通好友，30-约会中，40-情侣，50-历史，60-删除
) : Serializable
