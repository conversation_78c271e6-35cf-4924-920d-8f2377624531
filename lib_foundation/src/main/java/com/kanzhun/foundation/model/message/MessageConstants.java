package com.kanzhun.foundation.model.message;

public final class MessageConstants {
    //消息媒体类型 0:空 1:文本 2:图片 3:语音 4:小视频 5:表情 6:文件 7:灰条 8:动作 9:连麦 10:喜欢卡片 11:相识卡 12:表白信
    public static final int MSG_UPDATE = -1;//需要升级
    public static final int MSG_USER_CARD = 1001;//本地插入的用户卡片
    public static final int MSG_EMPTY = 0;//空消息
    public static final int MSG_TEXT = 1;//文本
    public static final int MSG_PIC = 2;//图片
    public static final int MSG_AUDIO = 3;//语音
    public static final int MSG_VIDEO = 4;//小视频
    public static final int MSG_STICKER = 5;//动态表情
    public static final int MSG_FILE = 6;//文件
    public static final int MSG_HINT = 7;//灰条
    public static final int MSG_ACTION = 8;//动作
    public static final int MSG_LINK_CALL = 9;//语音视频聊天
    public static final int MSG_LIKE_CARD = 10;//喜欢头像，故事，AB面等的消息

    public static final int MSG_DATE_CARD = 11;//相识卡片消息
    public static final int MSG_LOVE_CARD = 12;//表白卡片消息
    public static final int MSG_NOTIFY_CARD = 13;//相识卡片、表白卡片系统消息
    public static final int MSG_TOPIC_GAME = 14;//话题游戏
    public static final int MSG_COMMON_CARD = 15;//通用卡片模型
    public static final int MSG_OFFICIAL_ACTIVITY_CARD = 16;//官方活动
    public static final int MSG_MOOD_CARD = 17;//心情

    public static final int MSG_CHRISTMAS_CARD = 18;//圣诞活动
    public static final int MSG_FEEDBACK_REPLAY = 19;//反馈回复
    public static final int MSG_TACIT_TEST = 20;//默契考验
    public static final int MSG_MEDIA_TYPE_MEETUP_TASK = 21;//见面计划任务
    public static final int MSG_MEDIA_TYPE_MEETUP_INVITE = 22;//见面计划邀请
    public static final int MSG_MEDIA_TYPE_MEET_PROTECT = 23;//见面守护计划消息

    //region AI专属红娘
    public static final int MSG_MEDIA_TYPE_RECOMMEND_USER_CARD = 24;//推荐用户卡片
    public static final int MSG_MEDIA_TYPE_RECOMMEND_OPEN_CHAT = 25;//推荐开聊建议
    public static final int MSG_MEDIA_TYPE_RECOMMEND_WELCOME = 26;//月月首次开聊语
    //endregion

    /**
     * 1000以后为local message type
     */
    public static final int MSG_TIME = 1000;//时间

    public static final int MSG_TYPE_PRESENCE = 1;
    public static final int MSG_TYPE_MESSAGE = 2;
    public static final int MSG_TYPE_READ_MARK = 3;//已读，别人读了消息后给你发的
    public static final int MSG_TYPE_READ_SYNC = 4;//已读同步，是你读了别人的消息后，其他端同步给你的
    public static final int MSG_TYPE_DATA_SYNC = 5;
    public static final int MSG_TYPE_CONVERSATION_SYNC = 6;
    public static final int MSG_TYPE_IN_CHAT = 7;//inchat,进入聊天

    public static final int MSG_STATE_NONE = -2;
    public static final int MSG_STATE_SENDING = MSG_STATE_NONE + 1;
    public static final int MSG_STATE_FAILURE = MSG_STATE_SENDING + 1;
    public static final int MSG_STATE_DELIVERY = MSG_STATE_FAILURE + 1;
    public static final int MSG_STATE_PART_READ = MSG_STATE_DELIVERY + 1;
    public static final int MSG_STATE_READ = MSG_STATE_PART_READ + 1;

    public static final int COMMON_DEL_FLAG = 1;

    public static final int MSG_SCENE_PART = 3;

    public static final int MSG_MESSAGE_BOX = -1;
    public static final int MSG_SINGLE_CHAT = 1;
    public static final int MSG_GROUP_CHAT = 2;
    public static final int MSG_REPLY_CHAT = Integer.MAX_VALUE;
    public static final int MSG_PROJIECT_ITEM_CHAT = Integer.MAX_VALUE - 1;

    public static final int MSG_PAGE_SIZE = 50;
    public static final int MSG_PIC_SIZE = 100;
    public static final int MSG_FILE_SIZE = 30;

    public static final long MSG_SEQ_STARTING_VALUE = 1001;//每个会话的seq开始值，但不一定是可展示的消息，如发起视频语音时会先发action
    public static final int MSG_SYNC_PAGE_SIZE = 99;


    public static final int MSG_WITHDRAW = 12;//撤回
    public static final int MSG_AT_ALL = -1;//@所有人
    public static final int MSG_AT_ALL_REWRITE_TIME = 300000;//撤回可重新编辑时间


    public static final long MSG_MID_STARTING_VALUE = 1l << 62;//本地发送的消息id开始值

    public static final long MSG_SHOW_TIME_INTERVAL = 5 * 60 * 1000;

    public static final int MSG_DATA_SYNC_PROFILE = 100;//同步个人信息
    public static final int MSG_DATA_SYNC_CONVERSATION = 101;//同步消息列表
    public static final int MSG_DATA_SYNC_TAB_CONVERSATION_SUMMARY = 102;//同步喜欢我、我喜欢、历史会话展示
    public static final int MSG_DATA_SYNC_TAB_MATCH = 103;//同步匹配页
    public static final int MSG_DATA_SYNC_CONTACT = 104;//同步好友信息
    public static final int MSG_DATA_SYNC_SYSTEM_CONFIG = 105;//系统配置更新
    public static final int MSG_DATA_SYNC_SOCIAL_UNREAD = 106;//同步社区通知红泡
    public static final int MSG_DATA_SYNC_INVITE_BIND = 107;//同步绑定邀请信息
    public static final int MSG_DATA_SYNC_BIND_INFO = 108;//同步绑定信息
    public static final int MSG_DATA_SYNC_NOTICE_LIST = 109;//同步小秘书通知列表

    public static final int MSG_DATA_SYNC_RESTRICT_CHAT = 111;// 111=同步限制开聊
    public static final int MSG_DATA_SYNC_CHRISTMAS_TASK = 112;// 112=同步圣诞活动任务曝光
    public static final int MSG_DATA_SYNC_113 = 113;//
    public static final int MSG_DATA_SYNC_MEETUP_CHAT_LIST = 114; // 114-见面计划深度聊天接口
    public static final int MSG_SYNC_YUEYUE_CARD = 115; // 115-同步月月头部卡
    public static final int MSG_DATA_SYNC_SINGLE_CHAT_TOP_FLOATING_CARD = 116; // 116-见面守护计划聊天卡片，刷新头部卡片信息


    public static final int MSG_DATA_SYNC_LINK_ROOM_OTHER_BAD_NETWORK = 100101;//音视频通话，对方网络不佳
    public static final int MSG_DATA_SYNC_LINK_ROOM_CHANGE_AUDIO = 100102;//视频通话切换成音频
    public static final int MSG_DATA_SYNC_LOCAL_PUSH_NOTICE = 100201;//内部推送

    public static final int MSG_DATA_SYNC_EMPATHY_MATCH_SUCCESS = 100501;//心情共鸣匹配成功同步
    public static final int MSG_DATA_SYNC_CHAT_ICON = 100601;//同步聊天icon
    public static final int MSG_DATA_SYNC_TOPIC_CARD_RECOMMEND = 100602;//话题卡片推荐
    public static final int MSG_DATA_SYNC_DYNAMIC_RECOMMEND = 100603; // 动态推荐：https://zhishu.zhipin.com/wiki/eC6RHVPJ8mQ
    public static final int MSG_DATA_SYNC_CHAT_SEND_STATUS = 100151; // 同步聊天发送状态：https://zhishu.zhipin.com/wiki/pxmeBMX7ICs

    public static final int MSG_DATA_SYNC_NOTICE = 110001;//通知小秘书id-点赞列表
    public static final int MSG_DATA_SYNC_NOTICE_FILM = 110002;//通知小秘书id-胶片列表

    public static final int SYS_ID_OFFICIAL_KZ = 100001;
    public static final int SYS_ID_OFFICIAL_KZ_ACTIVITY = 100002;
    public static final int SYS_ID_OFFICIAL_CHRISTMAS = 100003;
    public static final int SYS_ID_OFFICIAL_YUEYUE = 100007; // 看准小红娘月月

    public static final int DATA_SYNC_100701 = 100701;
    public static final int DATA_SYNC_100702 = 100702;
    public static final int DATA_SYNC_100703 = 100703;
    public static final int DATA_SYNC_100604 = 100604;
    public static final int DATA_SYNC_100610 = 100610;

    // 100620-好友位置，sync内容格式{"userId":"xxxx", "gps": "119.97372,31.76943"}，gps为经纬度英文逗号分割
    public static final int DATA_SYNC_100620 = 100620;

    public static final int MSG_PLATFORM_IOS = 1;
    public static final int MSG_PLATFORM_ANDROID = 2;
    public static final int MSG_PLATFORM_PC = 3;

    public static final int MSG_PRESENCE_ONLINE = 0x001;
    public static final int MSG_PRESENCE_OFFLINE = 0x002;
    public static final int MSG_PRESENCE_VISIBLE = 0x0200;
    public static final int MSG_PRESENCE_GONE = 0x0400;

    public static final int MSG_PRESENCE_APP_ID = 11001;

    public static final int MSG_CONTACT_TYPE_USER = 1;//普通用户
    public static final int MSG_CONTACT_TYPE_SYS = 2;//机器人

    public static final int SEND_READ_MAX_SIZE = 20;//最大发送已读数

    public static final String ACTION_NEW_MESSAGE = "new_message";

    public static final String DATA_NEW_MESSAGE = "data_message";
    public static final String DATA_NEW_MESSAGE_CHAT_ID = "data_message_chat_id";
    public static final String DATA_NEW_MESSAGE_TYPE = "data_message_type";
    public static final String DATA_NEW_MESSAGE_UNREAD_COUNT = "data_message_unread_count";
    public static final String DATA_NEW_MESSAGE_TITLE = "data_message_title";
    public static final String DATA_PUSH_TYPE = "data_push_type";

    public static final String CALENDARINFO = "calendarInfo";
    public static final String APPLYINFO = "applyInfo";
    public static final String WORKABSENCE = "workAbsence";
    public static final String MONTHABSENCE = "monthAbsence";
    public static final String URL = "url";
    public static final String FRIEND_APPLY_LIST = "friendApplyList";
    public static final String QR_SCAN = "qrScan";
    public static final String TASK_INFO = "taskInfo";
    public static final String SHIMO = "shimo";
    public static final String SHINING = "shining";
    public static final String CLOCK = "clock";
    public static final String MSG = "msg";
    public static final String POST = "post";

    public static final String OC = "oc";
    public static final String HI_APP_ID = "hiAppId";
    public static final String OT = "ot";
    public static final String KAN_ZHUN = "kanzhun";
    public static final String PARAMS_URL = "url";


    public static final int LINK_CALL_CANCEL = 1;//发起方取消
    public static final int LINK_CALL_REFUSE = 2;//接收方拒绝
    public static final int LINK_CALL_OVER = 3;//通话结束
    public static final int LINK_CALL_NOT_CONNECT = 4;//无应答
    public static final int LINK_CALL_NOT_SUPPORT = 5;//版本低
    public static final int LINK_CALL_BUSY = 6;//忙线中

    public static final int LINK_CALL_TIME_OUT = 30000;

    public static final int VIDEO_SEND_RETRY_COUNT = 3;

    //    关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除
    public static final int MATCHING_STATUS_MY_LIKE = 11;
    public static final int MATCHING_STATUS_LIKE_YOU = 12;
    public static final int MATCHING_STATUS_FRIEND = 20;
    public static final int MATCHING_STATUS_DATING = 30;
    public static final int MATCHING_STATUS_LOVERS = 40;
    public static final int MATCHING_STATUS_HISTORY = 50;
    public static final int MATCHING_STATUS_DELETE = 60;

    //公共卡片类型：话题游戏提示卡片
    public static final int MSG_COMMON_CARD_TYPE_TOPIC_GAME = 1;
    //公共卡片类型：动态推荐
    public static final int MSG_COMMON_CARD_TYPE_DYNAMIC_RECOMMEND = 2;

    //公共卡片类型：20001 微信卡
    public static final int MSG_COMMON_CARD_TYPE_WECHAT_EXCHANGE = 20001;

}
