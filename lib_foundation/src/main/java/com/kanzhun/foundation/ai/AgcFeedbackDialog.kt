package com.kanzhun.foundation.ai

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastMap
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.SCENE_IDEAL
import com.kanzhun.foundation.api.response.Questionnaire
import com.kanzhun.foundation.databinding.FoundationCommonDialogBinding
import com.kanzhun.foundation.dialog.FixedBottomSheetDialogFragment
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.utils.T
import com.kanzhun.utils.ui.ActivityUtils
import com.petterp.floatingx.imp.FxAppLifecycleProvider
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


private const val TAG = "AgcFeedbackDialog"

class AgcFeedbackDialog(
    private val scene: String?, // 0:兴趣爱好 2:我的理想型
    private val getStringScene: String?, // 1 开聊语
) : FixedBottomSheetDialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return FoundationCommonDialogBinding.inflate(inflater, container, false).apply {
            composeView.onSetWindowContent {
                val dismissDialog = { dismissAllowingStateLoss() }
                val viewModel: AiViewModel = viewModel()

                LaunchedEffect(Unit) {
                    viewModel.initQuestionnaire(getStringScene)
                }

                val questionnaire = viewModel.questionnaire
                val localBeanGroup = viewModel.localBeanGroup
                AgcFeedbackContent(
                    questionnaire = questionnaire,
                    localBeanGroup = localBeanGroup,
                    onSubmit = { selectedRate ->
                        reportPoint("aigenerate-feedback-popup-submit") {
                            actionp2 =
                                if (scene == SCENE_IDEAL) "我的理想型" else if("1" == getStringScene) "开聊语" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                            actionp3 =
                                getRate(selectedRate + 1).text // 记录提交时的选项：很失望、不太好、一般般、还不错、非常好

                            if (selectedRate in listOf(0, 1)) { // 选择：很失望、不太好, 记录选项和用户输入的
                                actionp4 =
                                    (questionnaire.serverOptionsItem ?: localBeanGroup)
                                        .fastFilter { it.isSelected.value }
                                        .fastMap { it.name }
                                        .joinToString(";") // 记录不满意选项，多个选项用英文分号;隔开
                                actionp5 =
                                    (questionnaire.serverOptionsItem ?: localBeanGroup)
                                        .firstOrNull { it.isSelected.value && it.name == "其他" }?.otherReason?.value
                                        ?: "" // 记录自定义文本（若有）
                            }
                        }

                        dismissDialog()
                    }
                )
            }
        }.root
    }

    /**
     * 点击提交成功自我介绍 / 我的理想型时，退出编辑页后，触发弹窗收集
     */
    companion object {
        var shouldShowAgcFeedback = false

        @JvmStatic
        fun shouldShow(
            activity: FragmentActivity,
            scene: String = "", // 0:兴趣爱好 2:我的理想型
            getStringScene: String = "",
        ): AgcFeedbackDialog {
            val dialog = AgcFeedbackDialog(scene = scene,getStringScene = getStringScene)
            dialog.isCancelable = false
            dialog.show(activity.supportFragmentManager, TAG)

            return dialog
        }

        /**
         * 触发时机：点击提交成功自我介绍 / 我的理想型时，退出编辑页后，触发弹窗收集
         */
        @OptIn(DelicateCoroutinesApi::class)
        fun shouldShowDelayed(
            scene: String, // 0:兴趣爱好 2:我的理想型
        ) {
            if (shouldShowAgcFeedback) {
                shouldShowAgcFeedback = false

                GlobalScope.launch(Dispatchers.Main) {
                    delay(1000)

                    val topActivity = FxAppLifecycleProvider.getTopActivity()
                    if (topActivity is FragmentActivity && ActivityUtils.isValid(topActivity)) {
                        shouldShow(activity = topActivity, scene = scene)

                        reportPoint("aigenerate-feedback-popup-expo") {
                            actionp2 =
                                if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun AgcFeedbackContent(
    questionnaire: Questionnaire = Questionnaire(),
    onSubmit: (selectedRate: Int) -> Unit = {},
    localBeanGroup: List<Bean> = emptyList(),
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(
                shape = RoundedCornerShape(
                    topStart = 32.dp,
                    topEnd = 32.dp,
                    bottomEnd = 0.dp,
                    bottomStart = 0.dp
                )
            )
            .background(color = Color.White)
    ) {
        Image(
            painter = painterResource(id = R.mipmap.f_ic_header_bg),
            contentDescription = "image description",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(375f / 300)
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 68.dp, bottom = 32.dp)
                .padding(horizontal = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = "对本次生成的内容满意吗？",
                style = TextStyle(
                    fontSize = 24.sp,
                    fontFamily = boldFontFamily(),
                    fontWeight = FontWeight(900),
                    color = Color(0xFF292929),
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "欢迎留下你的意见与建议，我们将努力做得更好",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF5E5E5E),
                )
            )

            Spacer(modifier = Modifier.height(36.dp))

            var selectedRate by remember { mutableIntStateOf(-1) }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                rateList.forEachIndexed { index, rate ->
                    Column(
                        modifier = Modifier.noRippleClickable {
                            selectedRate = if (selectedRate == index) -1 else index
                        },
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        val isSelectedRate = index == selectedRate
                        if (isSelectedRate) {
                            val rateComposition by rememberLottieComposition(
                                spec = LottieCompositionSpec.Asset("rate/f_ic_sel_${index + 1}.json"),
                            )
                            val rateProgress by animateLottieCompositionAsState(
                                rateComposition
                            )
                            LottieAnimation(
                                composition = rateComposition,
                                progress = { rateProgress },
                                modifier = Modifier.size(48.dp)
                            )
                        } else {
                            Image(
                                painter = painterResource(id = rate.iconUnsel),
                                contentDescription = "image description",
                                modifier = Modifier.size(48.dp)
                            )
                        }

                        Text(
                            text = rate.text,
                            style = TextStyle(
                                fontSize = 13.sp,
                                fontWeight = FontWeight(400),
                                color = if (isSelectedRate) Color(0xFF000000) else Color(0xFFB2B2B2),
                                textAlign = TextAlign.Center,
                            )
                        )
                    }
                }
            }

            var otherSelectedReason by remember {
                mutableStateOf(
                    (questionnaire.serverOptionsItem ?: localBeanGroup)
                        .firstOrNull { it.isSelected.value && it.name == "其他" }
                )
            }

            AnimatedVisibility(selectedRate in 0..1) {
                Column(modifier = Modifier.fillMaxWidth()) {
                    Spacer(modifier = Modifier.height(40.dp))

                    // 对于选择了1星、2星再增加展示不满意选项，选项由服务端配置

                    val focusRequester = remember { FocusRequester() }

                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(10.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        (questionnaire.serverOptionsItem ?: localBeanGroup)
                            .forEach { bean ->
                                Keyword(bean = bean, onSelectChange = {
                                    bean.isSelected.value = !bean.isSelected.value

                                    if (bean.name == "其他") { // 用户是否点击了“其他”
                                        otherSelectedReason =
                                            if (bean.isSelected.value) {
                                                bean
                                            } else {
                                                null
                                            }
                                    }
                                })
                            }
                    }

                    var textState by remember {
                        val otherReason = otherSelectedReason?.otherReason?.value ?: ""
                        mutableStateOf(
                            TextFieldValue(
                                text = otherReason,
                                selection = TextRange(otherReason.length)
                            )
                        )
                    }

                    LaunchedEffect(otherSelectedReason) {
                        if (otherSelectedReason != null) { // 唤起键盘
                            delay(600)
                            focusRequester.requestFocus()
                        }
                    }

                    val coroutineScope = rememberCoroutineScope()
                    AnimatedVisibility(otherSelectedReason != null) {
                        Column(modifier = Modifier.fillMaxWidth()) {
                            Spacer(modifier = Modifier.height(16.dp))

                            TextField(
                                value = textState,
                                placeholder = {
                                    Text(
                                        text = "✨您的建议，会让AI更懂您～",
                                        fontSize = 14.sp,
                                        color = Color(0xFFB2B2B2)
                                    )
                                },
                                onValueChange = { newValue ->
                                    if (newValue.text.length <= 50) { // 自定义限制50字
                                        textState = newValue.copy(
                                            selection = TextRange(newValue.text.length)
                                        )

                                        // 保存用户输入的其他原因
                                        otherSelectedReason?.otherReason?.value = newValue.text
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            T.ss("最多填写50字")
                                        }
                                    }
                                },
                                maxLines = 3,
                                colors = TextFieldDefaults.colors(
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedContainerColor = Color.Transparent,
                                ),
                                textStyle = TextStyle(color = Color.Black, fontSize = 14.sp),
                                modifier = Modifier
                                    .background(
                                        color = Color(0xFFF4F4F6),
                                        shape = RoundedCornerShape(size = 25.dp)
                                    )
                                    .fillMaxWidth()
                                    .focusRequester(focusRequester)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(56.dp))

            var isEnabled = false
            if (selectedRate > 1) { // 选择：一般般~、还不错~、非常好！
                isEnabled = true
            } else { // 选择：很失望、不太好
                if (otherSelectedReason != null) { // 选择：其他。其他选项必填，且最多填写50字
                    if (otherSelectedReason?.otherReason?.value?.isNotEmpty() == true) {
                        isEnabled = true
                    }
                } else if ((questionnaire.serverOptionsItem
                        ?: localBeanGroup).firstOrNull { it.isSelected.value && it.name != "其他" } != null
                ) { // 选择了非其他的标签
                    isEnabled = true
                }
            }
            O2Button(
                modifier = Modifier.fillMaxWidth(),
                text = "提交",
                // 选择1、2星时必须选不满意的选项才能高亮可点击
                // 其他（自定义文本框，必填，最多填写50字；与其他选项不互斥）
                enabled = isEnabled,
            ) {
                onSubmit(selectedRate)
            }
        }
    }
}

@Composable
private fun Keyword(bean: Bean, onSelectChange: () -> Unit = {}) {
    val isSelected by bean.isSelected
    Text(
        text = bean.name,
        style = TextStyle(
            fontSize = 14.sp,
            fontWeight = FontWeight(400),
            color = Color(0xFF292929),
        ),
        modifier = Modifier
            .conditional(!isSelected) {
                border(
                    width = 2.dp,
                    color = Color(0xFFF5F5F5),
                    shape = RoundedCornerShape(size = 20.dp)
                )
            }
            .conditional(isSelected) {
                background(
                    color = if (isSelected) Color(0xFFE9F1FF) else Color.Transparent,
                    shape = RoundedCornerShape(size = 20.dp)
                )
            }
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .noRippleClickable {
                onSelectChange()
            }
    )
}

data class Bean(
    var id: Int,
    var name: String,
    var isSelected: MutableState<Boolean> = mutableStateOf(false),
    var otherReason: MutableState<String> = mutableStateOf(""),
)

data class Rate(
    val rate: Int,
    @DrawableRes val iconUnsel: Int,
    @DrawableRes val iconSel: Int,
    val text: String
)

private val rateList = listOf(
    Rate(
        rate = 1,
        iconUnsel = R.mipmap.f_ic_unsel_1,
        iconSel = R.mipmap.f_ic_sel_1,
        text = "很失望"
    ),
    Rate(
        rate = 2,
        iconUnsel = R.mipmap.f_ic_unsel_2,
        iconSel = R.mipmap.f_ic_sel_2,
        text = "不太好"
    ),
    Rate(
        rate = 3,
        iconUnsel = R.mipmap.f_ic_unsel_3,
        iconSel = R.mipmap.f_ic_sel_3,
        text = "一般般~"
    ),
    Rate(
        rate = 4,
        iconUnsel = R.mipmap.f_ic_unsel_4,
        iconSel = R.mipmap.f_ic_sel_4,
        text = "还不错~"
    ),
    Rate(
        rate = 5,
        iconUnsel = R.mipmap.f_ic_unsel_5,
        iconSel = R.mipmap.f_ic_sel_5,
        text = "非常好！"
    )
)

private fun getRate(rate: Int): Rate {
    return rateList.first { it.rate == rate }
}

@Preview
@Composable
private fun PreviewAgcFeedbackContent() {
    val viewModel = viewModel<AiViewModel>()
    AgcFeedbackContent(questionnaire = viewModel.questionnaire.apply {
        serverOptionsItem = viewModel.localBeanGroup.apply {
            get(0).name = "就是不喜欢"
            get(1).name = "就是很不喜欢"
            get(2).name = "就是超级不喜欢"
        }
    }, localBeanGroup = viewModel.localBeanGroup)
}