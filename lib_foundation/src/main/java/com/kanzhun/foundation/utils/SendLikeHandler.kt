package com.kanzhun.foundation.utils

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.model.matching.UserGuideBlockModel
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason

class SendLikeHandler {

    fun sendLike(context: Context, pageSource: PageSource,
                 userName:String?,resourceType:String = "0",uid:String?,
                 block:(UserGuideBlockModel)->Unit){
        val observable =
            RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java)
                .checkBeforeSendLike(resourceType)
        HttpExecutor.execute<UserGuideBlockModel>(
            observable,
            object : BaseRequestCallback<UserGuideBlockModel?>(false) {
                override fun onSuccess(it: UserGuideBlockModel?) {
                    if (it?.scene == 1) {
                        MatchingPageRouter.showSendLikeExaminingBlockDialog(
                            context,
                            it.blockInfo.also { bean ->
                                bean?.localLikeUserName = userName
                            },
                            pageSource
                        )
                    } else {
                        if (it?.isUserGuideBlock() == true) { //需要阻断，弹出阻断弹框
                            MePageRouter.showSendLikeBlockDialog(
                                context,
                                it.blockInfo.also { bean ->
                                    bean?.localLikeUserName = userName
                                },
                                pageSource,uid
                            )
                        } else {
                            block(it!!)
                        }

                    }
                }

                override fun dealFail(reason: ErrorReason?) {

                }
            })
    }

}