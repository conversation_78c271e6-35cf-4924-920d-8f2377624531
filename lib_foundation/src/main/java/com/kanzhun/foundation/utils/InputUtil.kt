package com.kanzhun.foundation.utils

import android.text.TextUtils

class InputUtil {

    companion object{
        fun getTextCount(s: String): Int {
            if (TextUtils.isEmpty(s)) {
                return 0
            }
            var count = 0 // 记录非空格字符的个数
            for (c in s.toCharArray()) {
                if (!Character.isWhitespace(c)) {
                    count++
                }
            }
            return count
        }
    }

}