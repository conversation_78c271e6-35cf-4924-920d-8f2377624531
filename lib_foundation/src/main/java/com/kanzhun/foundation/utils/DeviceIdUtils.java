package com.kanzhun.foundation.utils;

import static android.content.Context.TELEPHONY_SERVICE;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import androidx.core.app.ActivityCompat;

import com.techwolf.lib.tlog.TLog;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.utils.L;
import com.kanzhun.utils.process.ProcessUtil;

import java.util.UUID;

/**
 * Created by wang<PERSON> on 2017/4/13.
 */

public class DeviceIdUtils {
    private static final String TAG = "DeviceIdUtils";
    private static final String DEVICE_ID_KEY = "com.kanzhun.orange.IMEI_CODE";
    private static final String DID_KEY = "com.kanzhun.orange.DID_CODE";
    private static final String OAID_KEY = "com.kanzhun.orange.OAID_KEY";
    private static final String HMSO_KEY = "com.hpbr.bosszhipin.HMSO_KEY";
    private static String did = null;
    private static String msaId = null;
    private static String mIMEI = null;

    private static String msaHMId = null;
    private static boolean mHasPhoneStatePermission = false;
    private static String deviceId = null;

    public static void putDid(String did) {
        SpManager.putGlobalString(DID_KEY, did);
    }

    public static String getDid() {
        if (TextUtils.isEmpty(did)) {
            String localSp = SpManager.get().global().getString(DID_KEY, "");
            if (TextUtils.isEmpty(localSp)) {
                did = "";
            } else {
                did = localSp;
            }
        }
        return did;
    }

    public static void putOAID(String msaId) {
        SpManager.putGlobalString(OAID_KEY, msaId);
    }

    public static void putHMSOID(String msaId) {
        SpManager.putGlobalString(HMSO_KEY, msaId);
    }

    public static String getOAID() {
        if (msaId == null) {
            String localSp = SpManager.get().global().getString(OAID_KEY, "");
            if (TextUtils.isEmpty(localSp)) {

            } else {
                msaId = localSp;
            }
        }
        return msaId;
    }

    public static String getSystem() {
        StringBuilder system = new StringBuilder("Android");
        system.append("_");
        system.append(Build.VERSION.RELEASE);
        return system.toString();
    }

    /**
     * 获取手机型号
     *
     * @return 手机型号
     */
    public static String getSystemModel() {
        return Build.MODEL;
    }

    public static String getFullModel() {
        return Build.MANUFACTURER + "||" + Build.BRAND + "||" + Build.MODEL;
    }

    public static String getHMOAID() {
        if (msaHMId == null) {
            String localSp = SpManager.get().global().getString(HMSO_KEY,"");
            if (TextUtils.isEmpty(localSp)) {

            } else {
                msaHMId = localSp;
            }
        }
        return msaHMId;
    }

    /**
     * 获取手机型号
     *
     * @return
     */
    public static String getDeviceType() {
        return Build.BRAND + "||" + Build.MODEL;
    }

    /**
     * 获取UniqId
     */
    public static String getUniqId(Context context) {
        if (deviceId == null) {
            String localSp = SpManager.get().global().getString(DEVICE_ID_KEY,"");
            if (TextUtils.isEmpty(localSp)) {
                deviceId = UUID.randomUUID().toString();
                if (ProcessUtil.isMainProcess(context)) {
                    SpManager.get().global().edit().putString(DEVICE_ID_KEY, deviceId).apply();
                }
            } else {
                deviceId = localSp;
            }
        }
        return deviceId;
    }

}
