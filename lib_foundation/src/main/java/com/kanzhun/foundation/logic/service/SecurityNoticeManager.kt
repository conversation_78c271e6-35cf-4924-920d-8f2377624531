package com.kanzhun.foundation.logic.service

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.text.TextUtils
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.common.dialog.createOneButtonDialog
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.appendParametersToUrl
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.SecurityNoticeResponse
import com.kanzhun.foundation.router.AppPageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.error.SecurityNoticeException
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.localNotify.SwipeNotificationManager
import com.techwolf.lib.tlog.TLog

//安全框架
object SecurityNoticeManager {

    private var lastInvokeTime = 0L

    private fun safeInvoke(block: () -> Unit) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastInvokeTime >= 700) {
            lastInvokeTime = currentTime
            block()
        }
    }

    var dialog:Dialog? = null
    var isEnterIng:Boolean = false
    var isBlock = false

    fun reset(){
        isEnterIng = false
        isBlock = false
    }

    fun <T> handelSecurityNotice(response: BaseResponse<T>) {
        handelSecurityNotice(
            SecurityNoticeException(
                response.code,
                response.msg
            )
        )
    }

    @JvmStatic
    fun handelSecurityNotice(e: SecurityNoticeException) {
        if (!BaseApplication.getApplication().isForeground || AppPageRouter.topIsWebViewActivity() || AppPageRouter.hasWebViewActivity() || isEnterIng) {
            TLog.info("SecurityNoticeManager", "WebViewActivity，topIsWebViewActivity = %s，hasWebViewActivity = %s", AppPageRouter.topIsWebViewActivity(), AppPageRouter.hasWebViewActivity())
            return
        }
        isBlock = true
        isEnterIng = true
        SwipeNotificationManager.getInstance(BaseApplication.getApplication()).stop()

        if(e.errCode == BaseResponse.IP_ERROR){
            TLog.info("SecurityNoticeManager", "start IP_ERROR："+e.errCode)
            val context: Context = BaseApplication.getApplication().topContext
            if(context is Activity && dialog == null){
                dialog = context.createOneButtonDialog("温馨提示","检测到您的网络服务异常，请将手机切换至正常的4G/5G网络使用", cancelable = false,
                    canceledOnTouchOutside = false,"我知道了"){
                    dialog = null
                }
                dialog?.show()

            }
            isEnterIng = false
            return
        }

        TLog.info("SecurityNoticeManager", "start NET："+e.errCode)
        safeInvoke {
            val response = RetrofitManager.getInstance().createApi(FoundationApi::class.java).getSecurityNoticeInfo();
            HttpExecutor.execute(response, object : BaseRequestCallback<SecurityNoticeResponse?>() {
                override fun onSuccess(data: SecurityNoticeResponse?) {

                    if (data != null && data.haveHandling && !TextUtils.isEmpty(data.handlingUrl)) {
                        parseProtocol(data.handlingUrl.appendParametersToUrl(mapOf("isInterceptNavBackEvent" to "1")))
                        TLog.info("SecurityNoticeManager", "SecurityNoticeManager:handlingUrl=%s", data.handlingUrl)
                        //handlingUrl=https://orange-qa.weizhipin.com/h5/cert.html#/face-auth/index

                        LiveEventBus.get<Boolean>(LivedataKeyCommon.CHECK_APP_UPDATE)
                            .postDelay(true, 2000)
                    }
                    AppThreadFactory.getMainHandler().postDelayed({
                        isEnterIng = false
                    },600)
                }

                override fun dealFail(reason: ErrorReason?) {
                    isEnterIng = false
                }
            })
        }


    }
}