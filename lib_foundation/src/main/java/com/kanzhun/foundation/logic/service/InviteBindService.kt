package com.kanzhun.foundation.logic.service

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import com.kanzhun.foundation.api.bean.InviteBindResp
import com.kanzhun.foundation.facade.InviteBindRepository
import com.kanzhun.foundation.kernel.core.BaseService

class InviteBindService : BaseService() {

    private var mInviteBindRepository: InviteBindRepository? = null
    override fun onAccountInitialized() {
        if (mInviteBindRepository == null) {
            mInviteBindRepository = InviteBindRepository()
        }
    }

    override fun onAccountRelease() {
        mInviteBindRepository?.destroy()
        mInviteBindRepository = null
    }

    fun getInviteBindList(): LiveData<InviteBindResp?> {
        val inviteBindRepository: InviteBindRepository? = mInviteBindRepository
        return inviteBindRepository?.getInviteBindLiveData() ?: MediatorLiveData<InviteBindResp>()
    }



    fun blockInviteEvent(){
        mInviteBindRepository?.getInviteBindLiveData()?.value = null
    }
}