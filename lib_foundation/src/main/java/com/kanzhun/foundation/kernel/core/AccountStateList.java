package com.kanzhun.foundation.kernel.core;

import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.kernel.account.AccountLifecycle;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.utils.L;
import com.kanzhun.utils.platform.Utils;
import com.kanzhun.utils.process.ProcessUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/9
 */
public final class AccountStateList implements AccountLifecycle {
    private static final String TAG = "AccountStateList";
    private volatile int mCurrentState = -1;
    private List<AccountState> mAccountLifecycles = new ArrayList<>();
    private ReentrantLock mReentrantLock = new ReentrantLock();

    public void addAccountLifecycle(AccountState lifecycle) {
        if (!ProcessUtil.isMainProcess(Utils.getApp())) {
            L.e(TAG, "addAccountLifecycle: lifecycle is only suport main process. AccountState = " + lifecycle);
            return;
        }

        if (lifecycle != null) {
            try {
                if (mReentrantLock.tryLock(2, TimeUnit.SECONDS)) {
                    mAccountLifecycles.add(lifecycle);
                    if (mCurrentState == LIFECYCLE_INIT) {
                        lifecycle.onAccountInitialized();
                    }
                } else {
                    throw new RuntimeException("");
                }
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            } finally {
                mReentrantLock.unlock();
            }
        }
    }

    @Override
    public void onAccountInitialized() {
        MessageUtils.setCurrentUid(AccountHelper.getInstance().getUserId());
        notifyInit(LIFECYCLE_INIT);
    }

    @Override
    public void onAccountRelease() {
        notifyRelease(LIFECYCLE_RELEASE);
    }

    @Override
    public void onUserInfoCompletedFirstTime() {

    }


    private void notifyInit(int state) {
        try {
            mReentrantLock.lock();
            Iterator<AccountState> iterable = mAccountLifecycles.iterator();
            while (iterable.hasNext()) {
                AccountState lifecycle = iterable.next();
                switch (state) {
                    case LIFECYCLE_INIT:
                        lifecycle.onAccountInitialized();
                        break;
                    default:
                        break;
                }
            }
            mCurrentState = state;
        } catch (Exception e) {
            L.e("addAccountLifecycle",e.toString());
            e.printStackTrace();
            // TODO: 2022/2/9
//            CrashReport.postCatchedException(e);
//            if (e instanceof DataInitException || e instanceof com.tencent.wcdb.database.SQLiteDatabaseCorruptException) {
//                throw new DataInitException(e);
//            }
        } finally {
            mReentrantLock.unlock();
        }
    }

    private void notifyRelease(int state) {
        try {
            mReentrantLock.lock();
            ListIterator<AccountState> iterable = mAccountLifecycles.listIterator(mAccountLifecycles.size());
            while (iterable.hasPrevious()) {
                AccountState lifecycle = iterable.previous();
                switch (state) {
                    case LIFECYCLE_RELEASE:
                        lifecycle.onAccountRelease();
                        break;
                    default:
                        break;
                }
            }
            mCurrentState = state;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            mReentrantLock.unlock();
        }
    }

}
