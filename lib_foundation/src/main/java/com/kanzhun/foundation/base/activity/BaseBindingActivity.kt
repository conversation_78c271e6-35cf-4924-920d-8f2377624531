package com.kanzhun.foundation.base.activity

import android.content.Intent
import android.os.Bundle
import androidx.viewbinding.ViewBinding
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ktx.viewbinding.ActivityBindingModel
import com.kanzhun.common.kotlin.ktx.viewbinding.ActivityBindingModelDelegate
import com.kanzhun.common.kotlin.ui.activity.BaseActivity
import com.kanzhun.common.kotlin.ui.statelayout.IStatePage
import com.kanzhun.common.kotlin.ui.statelayout.StateLayoutManager
import com.kanzhun.common.kotlin.ui.statusbar.useBlackTextStatusBar
import com.kanzhun.foundation.base.activity.preformance.CommonGlobalPerformanceManager
import com.kanzhun.common.base.PageSource


/**
 * <AUTHOR>
 */
abstract class BaseBindingActivity<VB : ViewBinding, M : BaseViewModel> : BaseActivity(),
    ActivityBindingModel<VB, M> by ActivityBindingModelDelegate(), IStatePage,IBaseActivity{

    private val parentPerformManager : CommonGlobalPerformanceManager by lazy {
        CommonGlobalPerformanceManager(this)
    }

    private val stateLayoutManager: StateLayoutManager by lazy {
        StateLayoutManager()
    }
    //设置状态栏，可通过重写该方法来设置状态栏
    protected open var setStatusBar    = {
        //默认使用白底黑字状态栏
        useBlackTextStatusBar()
    }

    private var pageSource:PageSource? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initViewBindingAndViewModel()
        //监听loading dialog 事件
        observerLoadingDialogLiveData()
        setStatusBar()
        //监听页面状态
        attachStateLayout()
        intent?.run {
            preInit(this)
        }
        initView()
        initData()
        addCommonPerformance()
    }

    private fun addCommonPerformance(){
        parentPerformManager.init()
    }

    override fun isSenderOneVOneChat(senderId: String?): Boolean {
        return false
    }

    abstract fun preInit(intent: Intent)

    abstract fun initView()

    abstract fun initData()

    abstract fun onRetry()

    private fun observerLoadingDialogLiveData() {
        loadingDialogDelegate.observerLoadingLiveData(this, mViewModel.loadingDialog)
    }

    private fun attachStateLayout() {
        getStateLayout()?.run {
            stateLayoutManager.observerStateLayout(this, this@BaseBindingActivity, mViewModel.pageState,this@BaseBindingActivity::onRetry)
        }
    }

}