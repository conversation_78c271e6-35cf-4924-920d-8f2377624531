package com.kanzhun.foundation.kotlin.ui.dialog

import android.text.TextUtils
import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.techwolf.lib.tlog.TLog
import com.kanzhun.common.dialog.LinkageDialog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.views.wheel.pick.LinkagePickerOption
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.AreaBean
import com.kanzhun.foundation.api.model.CommonAddressResponse
import com.kanzhun.foundation.model.SelectLocationModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.utils.T

class LocationSelectDialog(val activity: FragmentActivity, val type: LocationSelectType, val title: String? = "") {
    private val tag = this.javaClass.simpleName

    private val observer = Observer<Boolean> {
        TLog.debug(tag, "Observer innerShow")
        innerShow()
    }
    private val mViewModel: LocationSelectDialogViewModel by lazy {
        ViewModelProvider(activity)[LocationSelectDialogViewModel::class.java].also {
            it.mType = type
        }
    }

    private var mCallback: (SelectLocationModel) -> Unit = {}


    init {
        //每次注册都会收到上一次的数据
        mViewModel.dataResult.observe(activity, observer)
    }


    fun show(callback: (SelectLocationModel) -> Unit): LocationSelectDialog {
        mCallback = callback
        if (mViewModel.areaOptions1Items.isEmpty()) {
            mViewModel.requestAreaData(true)
        }
        return this
    }

    private fun innerShow() {
        if (mViewModel.areaOptions1Items.isEmpty()) {
            return
        }
        val linkagePickerOption = LinkagePickerOption<AreaBean>()
        linkagePickerOption.isRestoreItem = true
        linkagePickerOption.mOptions1Items = mViewModel.areaOptions1Items
        linkagePickerOption.mOptions2Items = mViewModel.areaOptions2Items
        mViewModel.findWheelPosition()
        linkagePickerOption.options1 = mViewModel.areaOptions1position
        linkagePickerOption.options2 = mViewModel.areaOptions2position
        val dialog = LinkageDialog.Builder(activity).setLayoutId(R.layout.common_dialog_linkage_default)
            .setDisplayTextById(R.id.tv_title, title)
            .setLinkageId(R.id.linkage_view)
            .setSureId(R.id.tv_sure)
            .setPickerOptions(linkagePickerOption)
            .setOnItemSureClickListener { dialog, view, options1, options2, options3 ->
                setAreaSelect(options1, options2)
                dialog.dismiss()
            }
            .add(R.id.tv_cancel)
            .setOnItemClickListener { dialog, view -> dialog.dismiss() }
            .setPadding(0, 0)
            .setGravity(Gravity.BOTTOM)
            .setCancelable(true)
            .setCanceledOnTouchOutside(true)
            .create()
        dialog.setOnDismissListener {
            TLog.debug(tag, "Dismiss")
            mViewModel.dataResult.removeObserver(observer)
        }
        dialog.show()
    }

    private fun setAreaSelect(positionOne: Int, positionTwo: Int) {
        mViewModel.areaOptions1position = positionOne
        mViewModel.areaOptions2position = positionTwo
        var areaBeanOne: AreaBean? = null
        var areaBeanTow: AreaBean? = null
        if (mViewModel.areaOptions1Items.size > positionOne && positionOne >= 0) {
            areaBeanOne = mViewModel.areaOptions1Items[positionOne]
        }
        if (mViewModel.areaOptions2Items.size > positionOne && positionOne >= 0) {
            val areaBeans: List<AreaBean> = mViewModel.areaOptions2Items[positionOne]
            if (areaBeans.size > positionTwo && positionTwo >= 0) {
                areaBeanTow = areaBeans[positionTwo]
            }
        }
        setProvinceAndCity(areaBeanOne, areaBeanTow)
    }

    private fun setProvinceAndCity(provinceBean: AreaBean?, cityBean: AreaBean?) {


        if (provinceBean != null) {
            mViewModel.current.levelCode1 = provinceBean.code
            mViewModel.current.levelName1 = provinceBean.name
            mViewModel.current.specialProvince = provinceBean.specialProvince == AreaBean.SPECIAL_PROVINCE
        } else {
            mViewModel.current.levelCode1 = null
            mViewModel.current.levelName1 = null
        }
        if (cityBean != null) {
            mViewModel.current.levelCode2 = cityBean.code
            mViewModel.current.levelName2 = cityBean.name
        } else {
            mViewModel.current.levelCode2 = null
            mViewModel.current.levelName2 = null
        }
        mCallback(mViewModel.current)
    }

}

class LocationSelectDialogViewModel : BaseViewModel() {

    var mType: LocationSelectType = LocationSelectType.ONLY_AREA

    val current: SelectLocationModel = SelectLocationModel()

    var areaOptions1Items: MutableList<AreaBean> = mutableListOf()
    var areaOptions2Items: MutableList<List<AreaBean>> = mutableListOf()

    var areaOptions1position = 0
    var areaOptions2position = 0

    //如果一开始没有
    var dataResult: MutableLiveData<Boolean> = MutableLiveData()

    var isLoading: Boolean = false


    fun requestAreaData(showDialog: Boolean) {
        if (areaOptions1Items.isNotEmpty() || isLoading) {
            return
        }
        isLoading = true
        val commonAddressObservable = RetrofitManager.getInstance().createApi(FoundationApi::class.java).getCommonAddress(mType.value)
        HttpExecutor.execute<CommonAddressResponse>(commonAddressObservable, object : BaseRequestCallback<CommonAddressResponse?>() {
            override fun onSuccess(data: CommonAddressResponse?) {

            }

            override fun handleInChildThread(data: CommonAddressResponse?) {
                super.handleInChildThread(data)
                val list = data?.result ?: listOf()
                if (list.isEmpty()) {
                    return
                }
                areaOptions1Items.clear()
                areaOptions1Items.addAll(list)
                areaOptions2Items.clear()
                for (i in areaOptions1Items.indices) {
                    val child: List<AreaBean>? = areaOptions1Items[i].children
                    if (child == null) {
                        areaOptions2Items.add(listOf<AreaBean>())
                    } else {
                        areaOptions2Items.add(child)
                    }
                }
                dataResult.postValue(showDialog)
            }

            override fun onComplete() {
                super.onComplete()
                isLoading = false
            }

            override fun dealFail(reason: ErrorReason) {
                T.ss(reason.errReason)
            }
        })
    }


    /**
     * 通过code码获取两个滚轮的初始位置
     */
    fun findWheelPosition() {
        if (areaOptions1position <= 0) {
            for (i in areaOptions1Items.indices) {
                val areaBean = areaOptions1Items[i] ?: continue
                if (TextUtils.equals(areaBean.code, current.levelCode1)) {
                    areaOptions1position = i
                    val children = areaBean.children ?: return
                    for (j in children.indices) {
                        val child = children[j] ?: continue
                        if (TextUtils.equals(child.code, current.levelCode2)) {
                            areaOptions2position = j
                            return
                        }
                    }
                }
            }
        }
    }
}

//1-直辖市下级只返回区；2-直辖市下级只返回市；3-直辖市下级只返回市，增加不限的选项
enum class LocationSelectType(val value: Int) {
    ONLY_AREA(1),
    ONLY_CITY(2),
    ONLY_CITY_ADD_UNLIMITED(3)
}