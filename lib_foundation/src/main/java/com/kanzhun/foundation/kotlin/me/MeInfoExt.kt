package com.kanzhun.foundation.kotlin.me

import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.model.ProfileInfoModel

fun ProfileInfoModel.QuestionAnswer.getAnswerOptionString(): CharSequence {
    if (options.isNullOrEmpty()) {
        return ""
    }
    return buildSpannedString {
        val heightLightColor = R.color.common_color_005EFF.toResourceColor()
        for (index in options.indices) {
            val option = options[index]
            if (option.same) {
                color(heightLightColor) {
                    append(option.name)
                }
            } else {
                append(option.name)

            }
            if (index != options.size - 1) {
                append("、")
            }
        }
    }
}

fun ProfileInfoModel.QuestionAnswer.getOptionAnswerString2(): CharSequence {
    return buildSpannedString {
        options?.apply {
            for (index in options.indices) {
                val option = options[index]
                append(option.name)

                if (index != options.size - 1) {
                    append("、")
                }
            }
        }

        if (isNotEmpty()) {
            append("\n")
        }

        append(answer)
    }
}

//学历码转中文 1:本科 2:硕士 3:博士
fun Int?.degreeToString():String{
    return when(this){
        10 -> "初中及以下"
        20 -> "中专/中技"
        30 -> "高中"
        40 -> "大专"
        50 -> "本科"
        60 -> "硕士"
        70 -> "博士"
        else -> ""
    }
}

//1：全日制 2:非全日制
fun Int?.schoolTimeToString():String{
    return when(this){
        1 -> "全日制"
        2 -> "非全日制"
        else -> ""
    }
}