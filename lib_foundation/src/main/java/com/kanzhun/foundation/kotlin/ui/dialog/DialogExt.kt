package com.kanzhun.foundation.kotlin.ui.dialog

import android.content.Context
import com.kanzhun.common.dialog.showBottomListDialog
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.foundation.R
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.UserReportSource

/**
 * 举报和反馈弹框
 * @param source see UserReportSource
 */
fun Context.showReportOrFeedbackDialog(userId: String?,relationStatus:Int, @UserReportSource.State source: Int, resourceId: String? = "", callback: (position: Int) -> Unit = {}) {
    if (userId.isNullOrBlank()) {
        return
    }
    showBottomListDialog(listOf(R.string.common_report.toResourceString(), R.string.common_feedback.toResourceString())) {
        callback(it)
        when (it) {
            0 -> {//举报
                MePageRouter.jumpToUserReportActivity(this, userId, relationStatus,source, resourceId, RequestCodeConstants.REQUEST_CODE_0)
            }

            1 -> {//反馈
                MePageRouter.jumpToUnfitReasonActivity(this, userId)
            }

            else -> {

            }
        }
    }

}