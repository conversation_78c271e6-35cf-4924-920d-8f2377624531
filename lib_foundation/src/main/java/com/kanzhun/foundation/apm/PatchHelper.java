package com.kanzhun.foundation.apm;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.utils.process.ProcessUtil;
import com.tencent.bugly.beta.tinker.TinkerApplicationLike;
import com.tencent.bugly.beta.tinker.TinkerManager;
import com.tencent.bugly.beta.tinker.TinkerReport;
import com.tencent.tinker.lib.tinker.Tinker;
import com.tencent.tinker.loader.shareutil.ShareTinkerInternals;

import java.util.Set;

public final class PatchHelper {
    private static final String TAG = "Tinker.PatchHelper";
    private static final String KEY_CODE_MAIN = "zp_tinker_main";
    private static final String KEY_CODE_PATCH = "zp_tinker_patch";
    private static final String KEY_CODE_OTHER = "zp_tinker_other";
    private static final String KEY_TXT = "zp_tinker_txt";
    private static final String KEY_ACTIVE_INFO = "zp_tinker_active_info";
    private static final String KEY_PATCH_BLACK_LIST = "key_patch_black_list";

    public static void init(Context context) {
        String processName = ProcessUtil.getCurProcessName(context);
        TinkerManager.installTinker(TinkerApplicationLike.getTinkerPatchApplicationLike());
        TinkerManager.getInstance().setTinkerReport(new TinkerReport.Reporter() {
            @Override
            public void onReport(int key) {
                Log.d(TAG, "onReport key=" + key + "，processName=" + processName);
                if (key == TinkerReport.KEY_CRASH_FAST_PROTECT) {
                    onCrashFastProtect();
                }
            }

            @Override
            public void onReport(String s) {
                Log.d(TAG, "onReport s=" + s);
            }
        });
    }

    public static boolean isTinkerEnable() {
        return ShareTinkerInternals.isTinkerEnableWithSharedPreferences(getApp());
    }

    public static void onCrashFastProtect() {
        if (!Tinker.isTinkerInstalled() || !Tinker.with(getApp()).isTinkerLoaded()) {
            return;
        }

        String tinkerId = TinkerManager.getTinkerId();
        String newTinkerId = TinkerManager.getNewTinkerId();
        //保存黑名单
        if (!TextUtils.isEmpty(newTinkerId)) {
            String preText = SpManager.get().global().getString(KEY_PATCH_BLACK_LIST, "");
            String nextText = TextUtils.isEmpty(preText) ? newTinkerId : String.format("%s%s%s", preText, StringUtil.SPLIT_CHAR_KEYWORD, newTinkerId);
            SpManager.get().global().edit().putString(KEY_PATCH_BLACK_LIST, nextText).apply();
        }
        //目前只做上报
        ApmAnalyzer.create()
                .action(ApmAnalyticsAction.ACTION_ZP_TINKER_REPORT, ApmAnalyticsAction.ACTION_ZP_TINKER_REPORT_CRASH_PROTECT)
                .param("p2", tinkerId)
                .param("p3", newTinkerId)
                .report();
    }


    public static boolean isPatchInBlackList(String pendingPatchId) {
        String preText = SpManager.get().global().getString(KEY_PATCH_BLACK_LIST, "");
        if (TextUtils.isEmpty(preText)) {
            return false;
        }
        Set<String> sets = StringUtil.splitRemoveDuplicateKeywords(preText);
        return sets.contains(pendingPatchId);
    }

    public static Application getApp(){
        return BaseApplication.getApplication();
    }
}