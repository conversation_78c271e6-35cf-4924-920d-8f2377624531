package com.kanzhun.foundation.apm;

import com.kanzhun.foundation.api.base.HostConfig;

/**
 * <AUTHOR>
 * @date 2020/7/20.
 */
public class ApmKeyUtil {
    public static final String RELEASE_KEY = "Y5LTHZ5237qbk08U";
    public static final String RELEASE_PARAMETER = "w8gKDGAigayI4Hoi";
    public static final String DEBUG_KEY = "GDQzoeSQWbMpCIGF";
    public static final String DEBUG_PARAMETER = "Kuydbmirvqu9YsVB";


    public static String getApmKey() {
        return isOnline() ? RELEASE_KEY : DEBUG_KEY;
    }

    public static String getApmParameter() {
        return isOnline() ? RELEASE_PARAMETER : DEBUG_PARAMETER;
    }

    public static boolean isOnline() {
        HostConfig.Addr config = HostConfig.getCONFIG();
        if (config != null) {
            return config == HostConfig.Addr.ONLINE || config == HostConfig.Addr.PRE;
        }
        return false;
    }

}
