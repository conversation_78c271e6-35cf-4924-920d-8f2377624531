{"v": "5.7.11", "fr": 25, "ip": 0, "op": 100, "w": 1125, "h": 2001, "nm": "安卓导出", "ddd": 0, "assets": [{"id": "image_0", "w": 594, "h": 51, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 321, "h": 51, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 798, "h": 106, "u": "images/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 660, "h": 271, "u": "images/", "p": "img_3.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 2, "ty": 0, "nm": "内容", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [562.5, 1218, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [562.5, 1218, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1125, "h": 2436, "ip": 0, "op": 250, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "一个严肃的婚恋交友平台.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"t": 37.9166666666667, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [418.081, 815.322, 0], "to": [0, -5.334, 0], "ti": [0, 5.334, 0]}, {"t": 37.9166666666667, "s": [418.081, 783.32, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [297, 25.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "欢迎来到看准.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12.5, "s": [0]}, {"t": 25.4166666666667, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [282.174, 721.531, 0], "to": [0, -6.668, 0], "ti": [0, 6.668, 0]}, {"t": 25.4166666666667, "s": [282.174, 681.525, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [160.5, 25.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "容器 382.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 12.5, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [514.084, 515.47, 0], "to": [0, -10.001, 0], "ti": [0, 10.001, 0]}, {"t": 12.5, "s": [514.084, 455.465, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399, 53, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "路径 178", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [275.25, 536.305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.829, -7.383], [14.5, 3], [7.333, -3.166]], "o": [[-18.5, -7], [-3.829, 7.383], [-11.6, -2.4], [0, 0]], "v": [[50.75, -0.102], [-16.75, 1.898], [-12.342, -3.602], [-50.75, 1.898]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 2.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.965, 0.443, 1, 0.5, 0.637, 0.504, 1, 1, 0.31, 0.565, 1], "ix": 8}}, "s": {"a": 0, "k": [-47.471, 1.298], "ix": 4}, "e": {"a": 0, "k": [37.962, -0.035], "ix": 5}, "t": 1, "lc": 1, "lj": 2, "bm": 0, "nm": "gradient stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 178", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.438], "y": [0.192]}, "o": {"x": [0.4], "y": [0.575]}, "t": 50, "s": [100]}, {"t": 62.9166666666667, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 125, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "线 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [546.063, 1550.914, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-25.133, -5.607], [-12.132, 22.149], [7.337, 2.052], [5.451, -14.1], [-13.363, -6.189], [-5.71, 10.864], [4.613, 1.112], [2.637, -12.703], [-17.389, -14.227], [-14.087, 22.713], [26.335, 37.425], [0, 39.731], [-48.821, 4.874]], "o": [[-8.5, 23.001], [25.133, 5.606], [12.132, -22.15], [-7.337, -2.052], [-5.451, 14.1], [13.363, 6.189], [5.71, -10.864], [-4.613, -1.111], [-2.637, 12.703], [17.388, 14.227], [14.088, -22.714], [-26.334, -37.426], [0, -39.731], [0, 0]], "v": [[-80.521, 70.528], [-59.388, 129.636], [-5.409, 89.898], [0.218, 49.345], [-23.519, 77.846], [-22.113, 126.053], [12.794, 106.625], [14.284, 84.36], [1.8, 103.578], [14.284, 155.205], [76.391, 144.743], [60.365, 54.465], [-2.215, -70.983], [76.391, -164.221]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 2.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.965, 0.443, 1, 0.5, 0.637, 0.504, 1, 1, 0.31, 0.565, 1], "ix": 8}}, "s": {"a": 0, "k": [-78.971, 63.338], "ix": 4}, "e": {"a": 0, "k": [63.905, 27.6], "ix": 5}, "t": 1, "lc": 1, "lj": 2, "bm": 0, "nm": "gradient stroke 2", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "线", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "girl", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [562.5, 1218, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [562.5, 1218, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1125, "h": 2436, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "boy", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [562.5, 1218, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [562.5, 1218, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1125, "h": 2436, "ip": 0, "op": 250, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "路径 12备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [148.344, 1297.694, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.79, -2.58], [3.05, -8.28], [5.92, 7.61], [-6.6, 7.61], [-2.35, 5.02], [-10.82, 14.24], [-9.85, -4.25], [-2.72, -11.56]], "o": [[-1.69, -26.55], [8.8, 2.57], [-3.04, 8.29], [-5.92, -7.61], [6.6, -7.61], [2.34, -5.01], [10.82, -14.24], [18.23, 7.87], [0, 0]], "v": [[-26.643, 35.263], [-26.643, 4.653], [-19.033, 43.203], [-50.313, 43.203], [-50.313, 7.693], [-32.383, -2.457], [-22.073, -31.707], [21.387, -43.207], [50.317, -7.977]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 12备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "路径 177", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [40.274, 1372.017, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.46, 3.81], [-10.66, -7.61], [2.65, -6.47], [5.41, 7.26]], "o": [[2.46, -3.8], [10.65, 7.61], [-2.65, 6.47], [-7.42, -9.97]], "v": [[-15.473, -15.561], [10.067, -20.631], [15.477, 20.629], [-14.123, 17.929]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.976470589638, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 177", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "路径", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-43.696, 1680.629, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.61, 0.69], [-1.05, -4.3], [11.8, 3.03]], "o": [[8.42, -8.1], [1.01, 4.12], [0.56, -0.75]], "v": [[-42.64, -60.537], [-24.26, -64.837], [-44.4, -58.367]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.51, -0.13]], "o": [[0.52, 0.16], [0, 0]], "v": [[-45.944, -58.812], [-44.404, -58.372]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.38, -6.34]], "o": [[-5.39, 7.2], [0, 0]], "v": [[-44.4, -58.37], [-44.31, -35.21]], "c": false}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [-21.5, -9.66]], "o": [[9.26, 7], [0, 0]], "v": [[-44.308, -35.21], [3.892, -21.7]], "c": false}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-12.01, -52.87]], "o": [[21.5, 9.66], [0, 0]], "v": [[3.887, -21.7], [53.437, 86.97]], "c": false}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.59, -3.33]], "o": [[-11.5, 7.27], [0, 0]], "v": [[-44.304, -86.975], [-45.944, -58.815]], "c": false}, "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "路径 7备份 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [200.411, 1421.303, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [5.08, -10.14], [8.62, 0], [0, 0]], "o": [[0, 0], [-5.07, 10.15], [-8.63, 0], [0, 0]], "v": [[23.367, -27.2], [17.727, 9.44], [-5.773, 27.2], [-23.363, 19.93]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 7备份 5", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "路径 14备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [321.833, 1291.122, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.04, 0], [-2.54, -2.22], [0, 0]], "o": [[0, 0], [5.04, 0], [2.53, 2.23], [0, 0]], "v": [[-11.128, -3.405], [-2.958, -5.205], [7.782, -1.555], [11.132, 5.205]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 14备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "路径 13备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [317.02, 1364.674, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.29, 1.6], [0, 12.49], [3.72, 3.01], [3, 1.47], [-2.63, 1.06], [-4.96, -7.25], [-0.05, -5.36], [0, 0]], "o": [[2.02, 0], [5.29, -1.6], [0, -12.48], [-3.72, -3], [-3, -1.47], [2.63, -1.06], [4.97, 7.24], [0.06, 5.35], [0, 0]], "v": [[-18.197, 24.212], [-7.067, 22.472], [8.053, 2.842], [4.333, -13.958], [-6.367, -17.148], [-5.897, -24.208], [13.173, -18.468], [18.193, 5.212], [18.193, 20.112]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 13备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "路径 5备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [135.396, 1791.894, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.57, 11.74], [0, -6.78], [2.05, 0], [0, 11.55]], "o": [[3.3, 12.22], [0, 12.3], [-2.51, 0], [0, -6.62]], "v": [[10.002, 3.629], [13.302, 33.309], [9.612, 52.759], [5.962, 33.309]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [6.96, -17.49]], "o": [[0, 0], [0, 0]], "v": [[32.129, -52.761], [17.409, -21.591]], "c": false}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.91, -8.76]], "o": [[-2.98, 7.47], [0, 0]], "v": [[17.412, -21.589], [10.002, 3.631]], "c": false}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [26.48, 16.65]], "o": [[-4.61, -17.06], [0, 0]], "v": [[10, 3.626], [-32.13, -50.164]], "c": false}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 5备份 2", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "mouth", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [185.425, 1440.988, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75.417, "s": [{"i": [[0, 0], [-1.343, 1.424]], "o": [[2.09, 0.387], [0, 0]], "v": [[-4.718, 1.805], [1.245, -0.077]], "c": false}]}, {"t": 84.1666666666667, "s": [{"i": [[0, 0], [-2.245, 2.26]], "o": [[3.378, 0.297], [0, 0]], "v": [[-4.843, 1.814], [4.517, -1.663]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -356, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "路径", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [224.688, 1368.331, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.58, -0.34], [6.12, 8.1], [3.52, -0.98]], "o": [[-3.3, 2.32], [-0.87, 0.5], [-4.31, -5.7], [0, 0]], "v": [[13.153, 9.976], [6.353, 14.316], [1.443, -9.684], [-13.157, -14.314]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "路径 90备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149.402, 1324.596, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.84, -6.5]], "o": [[0, 0], [0, 0]], "v": [[-10.203, -1.414], [10.207, 1.416]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 90备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "eye", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [237.443, 1372.297, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [-100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 77.083, "s": [-100, 20, 100]}, {"t": 84.1666666666667, "s": [-100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.75, 0], [0, -4.24], [-0.71, 0], [0, 4.21]], "o": [[-0.75, 0], [0, 4.24], [0.71, 0], [0, -4.21]], "v": [[0.067, -4.662], [-2.393, -0.102], [0.067, 4.658], [2.397, -0.002]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 97备份 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "eye", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [158.489, 1368.828, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 70, "s": [-100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 77.083, "s": [-100, 20, 100]}, {"t": 84.1666666666667, "s": [-100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.75, 0], [0, -4.24], [-0.71, 0], [0, 4.21]], "o": [[-0.75, 0], [0, 4.24], [0.71, 0], [0, -4.21]], "v": [[0.067, -4.662], [-2.393, -0.102], [0.067, 4.658], [2.397, -0.002]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 97备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 2, "nm": "粉-2000-2.png", "cl": "png", "td": 1, "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 10, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.333, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25.833, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26.25, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.083, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.5, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28.333, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45.833, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54.583, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55.833, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56.25, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57.083, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65.833, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74.583, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83.333, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83.75, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84.583, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85.833, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94.583, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103.333, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112.083, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112.5, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113.333, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113.75, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114.583, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123.333, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140.833, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141.25, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142.083, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142.5, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.333, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160.833, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169.583, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170.833, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171.25, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.083, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180.833, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189.583, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198.333, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198.75, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199.583, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200.833, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209.583, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218.333, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227.083, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227.5, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228.333, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228.75, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229.583, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230.833, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231.25, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232.083, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232.5, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233.333, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233.75, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234.583, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235.833, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236.25, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237.083, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237.5, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238.333, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255.833, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256.25, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257.083, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257.5, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258.333, "s": [450, 1604, 0], "to": [-65.667, -15.333, 0], "ti": [65.667, 15.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258.75, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259.583, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260.833, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261.25, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262.083, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262.5, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263.333, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263.75, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264.583, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265.833, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266.25, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267.083, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267.5, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268.333, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268.75, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269.583, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270.833, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271.25, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272.083, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272.5, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273.333, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273.75, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274.583, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275.833, "s": [276, 1576, 0], "to": [-36.667, -10.667, 0], "ti": [36.667, 10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276.25, "s": [56, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277.083, "s": [56, 1512, 0], "to": [13.667, 0.333, 0], "ti": [-13.667, -0.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277.5, "s": [138, 1514, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278.333, "s": [138, 1514, 0], "to": [18.667, 6.333, 0], "ti": [-18.667, -6.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278.75, "s": [250, 1552, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 279.583, "s": [250, 1552, 0], "to": [-43.333, -10.333, 0], "ti": [43.333, 10.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280, "s": [-10, 1490, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 280.833, "s": [-10, 1490, 0], "to": [70, 12.667, 0], "ti": [-70, -12.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 281.25, "s": [410, 1566, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282.083, "s": [410, 1566, 0], "to": [-58.333, -9, 0], "ti": [58.333, 9, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 282.5, "s": [60, 1512, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283.333, "s": [60, 1512, 0], "to": [36, 10.667, 0], "ti": [-36, -10.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 283.75, "s": [276, 1576, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284.583, "s": [276, 1576, 0], "to": [-30.333, -7.667, 0], "ti": [30.333, 7.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285, "s": [94, 1530, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 285.833, "s": [94, 1530, 0], "to": [59.333, 12.333, 0], "ti": [-59.333, -12.333, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 286.25, "s": [450, 1604, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 287.083333333333, "s": [450, 1604, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [330, 135.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [156.636, 156.636, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "形状结合 2", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.899, 1412.978, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.45, 13.86], [-22.54, -1.16], [-3.92, -16.56], [2.03, -8.12], [6.6, 6.43], [2.7, 0], [6.36, -2.03], [-4.56, -7.27], [-3.89, -3.72], [0, -9.98], [3.83, -2.25], [4.74, -7.44], [12.52, -5.75], [5.92, -5.41], [0, 12.9], [0, 0], [-9.31, 19.45], [0, 0]], "o": [[8.46, -13.87], [14.73, 0.76], [1.76, 7.43], [-13.87, -0.68], [-9.64, 18.54], [-2.71, 0], [-7.95, 2.54], [4.57, 7.27], [3.89, 3.72], [0, 7.98], [3.38, 5.75], [-4.73, 7.44], [-7.29, 3.35], [-4.33, -1.57], [0, -30.95], [0, 0], [9.3, -19.45], [0, 0]], "v": [[-39.699, -60.663], [10.021, -83.893], [46.382, -51.593], [46.382, -26.843], [17.121, -46.293], [-17.708, -26.843], [-27.859, -32.993], [-33.439, -11.683], [-19.738, 1.227], [-9.089, 18.647], [-17.708, 31.327], [-16.529, 52.977], [-64.389, 66.167], [-81.299, 80.707], [-93.809, 56.527], [-64.389, 17.797], [-72.159, -17.713], [-47.469, -34.963]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-4.89, 11.63], [-3.21, -12.48], [14.12, 0.67], [19.17, 15.58], [-1.07, 14.65], [-2.96, 7.66]], "o": [[5.33, 2.43], [4.63, 18.02], [8.78, 23.04], [-12.17, -9.89], [5.96, -5.6], [12.57, 0.54]], "v": [[65.177, -7.975], [92.757, 24.795], [71.907, 55.245], [32.787, 77.565], [24.437, 26.255], [37.337, 8.075]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 2, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.392447886747, 0.964038145776, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状结合", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "形状结合", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.899, 1412.978, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.45, 13.86], [-22.54, -1.16], [-3.92, -16.56], [2.03, -8.12], [6.6, 6.43], [2.7, 0], [6.36, -2.03], [-4.56, -7.27], [-3.89, -3.72], [0, -9.98], [3.83, -2.25], [4.74, -7.44], [12.52, -5.75], [5.92, -5.41], [0, 12.9], [0, 0], [-9.31, 19.45], [0, 0]], "o": [[8.46, -13.87], [14.73, 0.76], [1.76, 7.43], [-13.87, -0.68], [-9.64, 18.54], [-2.71, 0], [-7.95, 2.54], [4.57, 7.27], [3.89, 3.72], [0, 7.98], [3.38, 5.75], [-4.73, 7.44], [-7.29, 3.35], [-4.33, -1.57], [0, -30.95], [0, 0], [9.3, -19.45], [0, 0]], "v": [[-39.699, -60.663], [10.021, -83.893], [46.382, -51.593], [46.382, -26.843], [17.121, -46.293], [-17.708, -26.843], [-27.859, -32.993], [-33.439, -11.683], [-19.738, 1.227], [-9.089, 18.647], [-17.708, 31.327], [-16.529, 52.977], [-64.389, 66.167], [-81.299, 80.707], [-93.809, 56.527], [-64.389, 17.797], [-72.159, -17.713], [-47.469, -34.963]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-4.89, 11.63], [-3.21, -12.48], [14.12, 0.67], [19.17, 15.58], [-1.07, 14.65], [-2.96, 7.66]], "o": [[5.33, 2.43], [4.63, 18.02], [8.78, 23.04], [-12.17, -9.89], [5.96, -5.6], [12.57, 0.54]], "v": [[65.177, -7.975], [92.757, 24.795], [71.907, 55.245], [32.787, 77.565], [24.437, 26.255], [37.337, 8.075]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 2, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.976, 0.588, 1, 0.5, 0.978, 0.647, 1, 1, 0.98, 0.706, 1], "ix": 9}}, "s": {"a": 0, "k": [-22.973, -83.981], "ix": 5}, "e": {"a": 0, "k": [58.128, 83.981], "ix": 6}, "t": 1, "nm": "gradient fill 5", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状结合", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "路径 7备份 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.304, 1526.761, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [14.04, -12.18], [-0.76, -15.68], [5.97, -23.67], [59.427, 20.237], [-3.42, 10.65], [2.28, 3.83], [-1.01, 6.59], [4.08, 2.04], [9.27, 3.38], [-0.33, 5.31], [0, 0]], "o": [[0, 0], [-1.37, 22.4], [-1, 15.37], [-9.35, 19.29], [-2.15, -6.48], [-2.078, -0.708], [2.73, -8.52], [2.77, -0.34], [1.01, -6.6], [-2.03, -1.02], [-7.41, -2.71], [0, 0], [0, 0]], "v": [[12.805, -91.821], [41.695, -69.751], [26.115, -13.531], [30.175, 33.989], [6.835, 91.819], [-60.861, 26.843], [-14.465, 10.479], [-17.715, -7.441], [-9.565, -17.251], [-16.155, -34.331], [-26.985, -44.031], [-35.265, -63.351], [-17.715, -87.081]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [7.285, -14.563], "ix": 5}, "e": {"a": 0, "k": [5.573, 66.86], "ix": 6}, "t": 1, "nm": "gradient fill 2", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 7备份 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 265.833333333333, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "路径备份 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1061.113, 967.804, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.62, -2.95], [7.39, 0], [3.57, 3.92], [0.79, 14.03], [-0.27, 21.34], [-4.58, 0], [-5.52, 5.75], [0.41, 2.08], [2.14, -2.7], [-1.98, -3.04], [-5.2, 3.7], [-2.05, 4.33], [2.22, 1.3], [2.35, -5.27], [-5.56, -4.71], [-4.11, 2.03]], "o": [[-2.13, 2.36], [-5.42, 4.43], [-7.38, 0], [-3.56, -3.91], [-0.53, -9.35], [-0.14, 6.22], [6.88, 0], [2.61, -2.72], [-0.45, -2.32], [-4.05, 5.12], [1.98, 3.04], [5.2, -3.7], [2.05, -4.34], [-2.23, -1.3], [-2.34, 5.26], [5.57, 4.71], [0, 0]], "v": [[28.222, 33.594], [19.602, 41.564], [0.212, 49.285], [-17.218, 41.725], [-27.828, 16.624], [-28.218, -29.415], [-21.558, -20.085], [-4.568, -30.675], [-1.148, -39.215], [-6.088, -39.215], [-6.548, -22.505], [6.362, -22.505], [18.172, -36.915], [20.182, -49.285], [11.582, -41.235], [11.582, -21.425], [27.342, -19.876]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径备份 6", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "路径 18备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1030.687, 895.446, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.08, 11.97], [-2.6, 3.07], [-1.01, 1.47], [-2.36, 5.78], [-15.31, 6.55], [-6.09, 0]], "o": [[0, 0], [-3.08, -11.97], [2.6, -3.07], [1.02, -1.47], [2.35, -5.78], [15.31, -6.55], [0, 0]], "v": [[-20.571, 43.396], [-37.971, 32.196], [-35.801, 7.496], [-27.931, 2.206], [-23.821, -11.214], [0.879, -38.054], [37.969, -43.394]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 18备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "路径 17备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [896.005, 913.045, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.42, 0.68], [0, 0]], "o": [[0, 0], [2.41, -0.68], [0, 0]], "v": [[-4.714, 1.628], [-0.214, -1.632], [4.716, -0.682]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 17备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "路径 16备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [844.597, 1081.699, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.06, 19.91], [-2.64, 7.11], [-2.56, 2.02], [2.69, -5.38], [1.09, -7.18], [0, 0]], "o": [[0, 0], [2.07, -19.92], [2.63, -7.12], [2.57, -2.02], [-2.7, 5.38], [-1.09, 7.17], [0, 0]], "v": [[-14.128, 53.883], [-10.108, 3.583], [-1.518, -41.157], [7.312, -53.887], [14.132, -49.127], [7.312, -31.087], [7.312, -19.117]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 16备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "mouth", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1057.509, 1048.941, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.773, 0.876]], "o": [[2.28, 1.59], [0, 0]], "v": [[-2.726, 0.007], [5.038, 0.845]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -351.999, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "路径 100", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1019.244, 966.625, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.99, -9.22], [0, -0.89], [0, 0]], "o": [[0, 0], [-3.98, 9.23], [0, 0.9], [0, 0]], "v": [[11.698, -18.031], [-3.182, -5.511], [-3.052, 18.029], [-11.702, 15.489]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -351.999, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 100", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "路径 99", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1100.978, 927.43, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.51, 1.43], [0, 0]], "o": [[0, 0], [6.51, -1.43], [0, 0]], "v": [[-11.268, 2.203], [-0.708, -2.207], [11.272, -2.207]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -351.999, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 99", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "粉-2000-2.png", "cl": "png", "td": 1, "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 240.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 241.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 242.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 243.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 244.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 245.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 246.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 247.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 248.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 249.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 250.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 251.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 252.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 253.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 255.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 256.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 257.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 258.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 259.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 260.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 261.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 262.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 263.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 264.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 265.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 266.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 267.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 268.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269.583, "s": [1282.5, 1418, 0], "to": [-160, 0, 0], "ti": [160, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [322.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270.833, "s": [322.5, 1418, 0], "to": [98.667, 0, 0], "ti": [-98.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 271.25, "s": [914.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272.083, "s": [914.5, 1418, 0], "to": [90, 0, 0], "ti": [-90, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 272.5, "s": [1454.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273.333, "s": [1454.5, 1418, 0], "to": [-101.333, 0, 0], "ti": [101.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 273.75, "s": [846.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 274.583, "s": [846.5, 1418, 0], "to": [58.667, 0, 0], "ti": [-58.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275, "s": [1198.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 275.833, "s": [1198.5, 1418, 0], "to": [45.333, 0, 0], "ti": [-45.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 276.25, "s": [1470.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277.083, "s": [1470.5, 1418, 0], "to": [-116, 0, 0], "ti": [116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 277.5, "s": [774.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278.333, "s": [774.5, 1418, 0], "to": [84.667, 0, 0], "ti": [-84.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 278.75, "s": [1282.5, 1418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 279.583333333333, "s": [1282.5, 1418, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [330, 135.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [224, 224, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "shirt 2", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [917.858, 1417.05, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.26, -15.9], [20.52, 8.32], [0.37, 30.6], [-5.78, 13.4], [-8, 3.24]], "o": [[0, 1.83], [0, 0], [0, 0], [0, 0], [0, 0], [-25.4, 10.42], [-22.13, -8.96], [-0.26, -21.35], [9.81, -22.78], [8, -3.25]], "v": [[44.662, -93.982], [44.662, -78.762], [15.352, -64.472], [23.632, -40.862], [-2.388, -28.392], [39.642, 81.608], [-21.718, 93.978], [-44.658, 14.648], [-38.648, -44.232], [19.062, -83.392]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.247386393828, 0.515260464537, 0.97121629902, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "蒙版", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "shirt", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [917.858, 1417.05, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.26, -15.9], [20.52, 8.32], [0.37, 30.6], [-5.78, 13.4], [-8, 3.24]], "o": [[0, 1.83], [0, 0], [0, 0], [0, 0], [0, 0], [-25.4, 10.42], [-22.13, -8.96], [-0.26, -21.35], [9.81, -22.78], [8, -3.25]], "v": [[44.662, -93.982], [44.662, -78.762], [15.352, -64.472], [23.632, -40.862], [-2.388, -28.392], [39.642, 81.608], [-21.718, 93.978], [-44.658, 14.648], [-38.648, -44.232], [19.062, -83.392]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.501960813999, 0.686274528503, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "蒙版", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "eye", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1005.95, 972.393, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55.833, "s": [-100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60.417, "s": [-100, 20, 100]}, {"t": 65.4166666666667, "s": [-100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.2, -0.36], [-0.7, -3.59], [-1.26, 0.25], [0.58, 3.12]], "o": [[-1.21, 0.36], [0.7, 3.59], [1.25, -0.24], [-0.57, -3.12]], "v": [[-1.241, -4.613], [-2.221, 0.618], [1.249, 4.607], [2.219, -0.312]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -344.999, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 103", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "eye", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1091.279, 969.752, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.39, -0.33], [-0.98, -3.66], [-0.74, 0.91], [0.6, 2.37]], "o": [[-1.4, 0.33], [0.98, 3.66], [0.73, -0.91], [-0.61, -2.37]], "v": [[-1.178, -4.225], [-2.308, 1.245], [2.022, 4.225], [2.312, -0.365]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.098039217293, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": -344.999, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 102", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "路径 20备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1064.677, 847.953, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.81, 5.78], [-18.06, 0.72], [-1.34, -1.44], [0, 0], [1.86, -0.51], [1.12, 1.5], [0, 0], [2.06, -1.29], [1.7, 2.88], [0, 0], [3.7, -2.42], [1.77, 1.29], [0, 0]], "o": [[0, 0], [2.8, -5.78], [18.05, -0.72], [1.33, 1.44], [0, 0], [-1.87, 0.51], [-1.13, -1.5], [0, 0], [-2.06, 1.29], [-1.69, -2.87], [0, 0], [-3.7, 2.42], [-1.78, -1.29], [0, 0]], "v": [[-29.801, 11.324], [-27.171, -3.796], [4.069, -21.596], [28.769, -17.006], [29.799, 18.574], [23.569, 21.594], [12.059, 20.074], [7.959, 15.394], [2.579, 18.574], [-7.741, 17.444], [-9.281, 13.974], [-15.131, 17.444], [-25.011, 19.864], [-29.631, 13.974]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.764705896378, 0.764705896378, 0.764705896378, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 20备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "路径 19备份 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1016.332, 1269.641, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.01, 0.13], [3.07, 3.36], [0.68, 14.75], [0, 0], [-6.88, 0], [-2.91, 0.39], [-0.54, -0.82], [-5.2, 3.7], [-5.56, -4.72], [-4.11, 2.04]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.18, -0.82], [-3.56, -3.91], [-0.69, -14.75], [0, 0], [5.01, 0], [1.08, -0.14], [1.98, 3.04], [5.2, -3.7], [5.56, 4.71], [0, 23.66]], "v": [[42.272, 130.026], [5.752, 130.026], [-42.268, 17.087], [-14.418, 5.036], [-24.138, -16.473], [11.922, -38.624], [11.692, -50.753], [-3.068, -58.104], [-12.898, -83.984], [-12.898, -130.023], [-6.628, -120.703], [6.832, -126.594], [8.382, -123.113], [21.292, -123.113], [26.512, -122.033], [42.272, -120.493]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [13.185, -47.622], "ix": 5}, "e": {"a": 0, "k": [14.687, 130.216], "ix": 6}, "t": 1, "nm": "gradient fill 4", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 19备份 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 272.083333333333, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "适配", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [562.5, 1016.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [562.5, 1218, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1125, "h": 2436, "ip": 0, "op": 250, "st": 0, "bm": 0}], "markers": []}