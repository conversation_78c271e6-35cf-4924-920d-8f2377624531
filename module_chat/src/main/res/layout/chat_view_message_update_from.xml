<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:chatItemBackground="@{msg}"
        android:orientation="vertical"
     >

        <com.kanzhun.marry.chat.views.MLinkEmotionTextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="16dp"
            android:paddingTop="10dp"
            android:paddingRight="16dp"
            android:paddingBottom="10dp"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_16"
            android:text="需升级新版本以查看此消息" />
    </LinearLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForUpdate" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>