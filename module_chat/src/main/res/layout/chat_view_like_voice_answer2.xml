<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="title"
            type="String" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="friend"
            type="Boolean" />
    </data>

    <RelativeLayout
        android:id="@+id/ll_voice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/chat_bg_message3"
        android:padding="16dp"
        tools:ignore="SpUsage,ContentDescription">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{title}"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="最难忘的一次约会是最难忘的一次约会是最难忘的一次约会是最难忘的一次约会是" />

        <ImageView
            android:id="@+id/iv_voice_play_status"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_below="@+id/tv_title"
            android:layout_marginTop="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:voiceLikePlayStatus="@{msg.voiceStatus}"
            tools:src="@drawable/me_ic_icon_info_preview_voice_start" />

        <com.kanzhun.common.views.AudioRecorderPlayView
            android:id="@+id/record_play_view"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_alignTop="@+id/iv_voice_play_status"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="18dp"
            android:layout_toEndOf="@+id/iv_voice_play_status"
            app:layout_constraintBottom_toBottomOf="@+id/iv_voice_play_status"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_voice_play_status"
            app:layout_constraintTop_toTopOf="@+id/iv_voice_play_status"
            app:likeVoiceWave="@{msg}" />

    </RelativeLayout>

</layout>