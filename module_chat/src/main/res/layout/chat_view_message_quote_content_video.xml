<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.facebook.drawee.view.SimpleDraweeView
            android:layout_width="34dp"
            android:layout_height="34dp"
            app:roundedCornerRadius="2dp"
            android:layout_marginTop="2dp"
            app:imageCenterCropUrl="@{msg.thumbnail.url}"
            android:scaleType="centerCrop"/>

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:src="@mipmap/chat_icon_chat_video_play"/>
    </FrameLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForVideo" />

        <variable
            name="friend"
            type="Boolean" />

    </data>
</layout>