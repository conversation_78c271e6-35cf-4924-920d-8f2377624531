<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    tools:background="@color/common_color_F5F5F5"
    tools:padding="16dp">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/mEmotionView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="4dp"
            android:background="@color/color_white"
            android:scaleType="centerCrop"
            app:common_radius="12dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <TextView
        android:id="@+id/mTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/common_color_9999A3"
        android:textSize="12sp"
        tools:text="好的" />

</LinearLayout>