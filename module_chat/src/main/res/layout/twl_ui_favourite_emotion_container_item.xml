<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center">


    <ImageView
        android:id="@+id/mEmotionView"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_margin="4dp"
        tools:background="@color/common_color_5E5E5E" />

    <TextView
        android:id="@+id/mTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/common_color_5E5E5E"
        android:textSize="12sp"
        tools:text="好的" />

</LinearLayout>