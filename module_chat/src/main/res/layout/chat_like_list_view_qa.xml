<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="ContentDescription,SpUsage">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="112dp"
        android:layout_height="wrap_content"
        android:background="@color/common_color_F5F5F5"
        android:paddingBottom="8dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_radius="8dp">

        <ImageView
            android:layout_width="26dp"
            android:layout_height="33dp"
            android:background="@mipmap/me_qa_section_bg"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/me_icon_preview_qa"
            app:layout_constraintBottom_toBottomOf="@+id/tvQuestion"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvQuestion" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvQuestion"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_color_191919"
            android:textSize="12dp"
            app:layout_constraintLeft_toRightOf="@+id/ivIcon"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="十年后我的理想生活" />

        <TextView
            android:id="@+id/tvAnswer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/common_color_5E5E5E"
            android:textSize="8dp"
            app:layout_constraintEnd_toStartOf="@+id/compose_view_answer_images"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvQuestion"
            tools:text="时的他们面对镜头采访难免会有些紧张，于是动作拘谨、表情略显僵" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/compose_view_answer_images"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvAnswer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvAnswer"
            tools:composableName="com.kanzhun.marry.me.info.adapter.AnswerImagesKt.AnswerImagesPreview1" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>