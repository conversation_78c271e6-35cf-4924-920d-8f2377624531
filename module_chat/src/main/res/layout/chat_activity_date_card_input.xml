<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".DateCardInputActivity">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.DateCardInputViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.DateCardInputCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@mipmap/chat_bg_date_card"
            app:layout_constraintDimensionRatio="h,375:230"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true">



            <include
                android:id="@+id/title_bar"
                layout="@layout/common_layout_only_left_title_bar"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                app:callback="@{callback}" />

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginBottom="10dp"
                android:scrollbars="none"
                app:layout_constraintBottom_toTopOf="@+id/cl_bottom"
                app:layout_constraintTop_toBottomOf="@+id/title_bar">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="205dp"
                        android:layout_height="47dp"
                        android:layout_marginEnd="56dp"
                        android:background="@mipmap/chat_bg_date_card_letter"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_on_date_card"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28dp"
                        android:layout_marginTop="10dp"
                        android:text="@string/chat_on_date_card"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_18"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_leave_a_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28dp"
                        android:layout_marginTop="3dp"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/chat_date_card_leave_a_message"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_28"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_on_date_card" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginLeft="28dp"
                        android:layout_marginTop="37dp"
                        android:layout_marginRight="28dp"
                        android:layout_marginBottom="10dp"
                        android:background="@mipmap/chat_bg_date_card_unlock"
                        app:layout_constraintDimensionRatio="h,319:204"
                        app:layout_constraintTop_toBottomOf="@+id/tv_leave_a_message">

                        <com.coorchice.library.SuperTextView
                            android:layout_width="match_parent"
                            app:stv_corner="12dp"
                            app:stv_shaderEnable="true"
                            app:stv_shaderMode="topToBottom"
                            app:stv_shaderStartColor="#00FFFFFF"
                            app:stv_shaderEndColor="#8571D1FF"
                            android:layout_height="match_parent">

                        </com.coorchice.library.SuperTextView>

                        <com.kanzhun.common.views.OEditText
                            android:layout_margin="18dp"
                            android:id="@+id/edit_text"
                            android:layout_width="match_parent"
                            android:includeFontPadding="false"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:hint="@string/chat_would_you_like_date_with_me"
                            android:focusable="true"
                            android:lineSpacingMultiplier="1.2"
                            android:textColor="@color/chat_color_004970"
                            android:textColorHint="@color/chat_color_004970_40"
                            android:textCursorDrawable="@drawable/chat_color_date_card_text_cursor"
                            android:textSize="@dimen/common_text_sp_24"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:id="@+id/tv_tip"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginEnd="30dp"
                    android:layout_marginBottom="26dp"
                    android:drawableLeft="@drawable/me_ic_fail_small"
                    android:drawablePadding="5dp"
                    android:text="@{viewModel.errorObservable}"
                    android:textColor="@color/common_color_FF3F4B"
                    android:textSize="@dimen/common_text_sp_12"
                    app:layout_constraintBottom_toTopOf="@+id/btn_submit"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:visibleGone="@{viewModel.errorObservable}"
                    tools:text="如发送违规消息，可能会被举报封号哦" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btn_submit"
                    style="@style/common_blue_button_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    android:enabled="@{TextUtils.isEmpty(viewModel.errorObservable)}"
                    android:onClick="@{(view)->callback.clickSubmit(view)}"
                    android:text="@string/chat_card_send"
                    app:greyDisabledStyle="@{TextUtils.isEmpty(viewModel.errorObservable)}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>