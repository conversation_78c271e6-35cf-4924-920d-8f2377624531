<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.kanzhun.marry.chat.R" />

        <import type="androidx.core.content.ContextCompat" />

        <import type="android.text.TextUtils" />

        <variable
            name="type"
            type="int" />

        <variable
            name="item"
            type="com.kanzhun.marry.chat.api.response.ChatCommonResponse.ChatCommonBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_bg_corner_10_color_white"
        android:paddingBottom="16dp">

        <com.kanzhun.foundation.views.RoundProgressBar
            android:id="@+id/rpb_self"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_marginTop="11dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:original_progress="100"
            app:progress_width="4dp"
            app:round_progress_color="@color/common_color_C9CDFF" />


        <com.kanzhun.common.views.image.OAvatarImageView
            android:id="@+id/ov_photo"
            android:layout_width="72dp"
            android:layout_height="72dp"
            app:common_circle="true"
            app:imageUrl="@{item.avatar}"
            app:layout_constraintBottom_toBottomOf="@+id/rpb_self"
            app:layout_constraintEnd_toEndOf="@+id/rpb_self"
            app:layout_constraintStart_toStartOf="@+id/rpb_self"
            app:layout_constraintTop_toTopOf="@+id/rpb_self" />

        <TextView
            android:id="@+id/tv_nick_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:gravity="center"
            android:text="@{TextUtils.isEmpty(item.nickName) ? @string/common_user_cancel : item.nickName}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rpb_self"
            tools:text="把" />

        <TextView
            android:id="@+id/tv_last_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:drawablePadding="6dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_color_B7B7B7"
            android:textSize="@dimen/common_text_sp_12"
            app:gender="@{item.gender}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_nick_name"
            app:likeSource="@{item.lastVisibleMessage}" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:layout_marginRight="6dp"
            android:textColor="@color/common_color_B7B7B7"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:setTime="@{item.lastVisibleMessage.time}"
            tools:text="13:03" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/chat_bg_item_related_msg"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="12dp"
            android:paddingTop="6dp"
            android:paddingRight="12dp"
            android:paddingBottom="6dp"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_last_msg"
            app:likeReason="@{item.lastVisibleMessage}"
            tools:text="把手机号的" />

        <View
            visibleGone="@{item.viewLikeMe == 1}"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/chat_mantle_corner_12_color_white_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>