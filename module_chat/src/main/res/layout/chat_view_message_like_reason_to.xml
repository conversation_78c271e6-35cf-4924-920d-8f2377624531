<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:visibility="@{TextUtils.isEmpty(msg.getLikeInfo().likeReason) ? View.GONE : View.VISIBLE}">

        <com.kanzhun.marry.chat.views.MLinkEmotionTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:paddingLeft="16dp"
            android:paddingTop="10dp"
            android:paddingRight="16dp"
            android:paddingBottom="10dp"
            android:text="@{msg.getLikeInfo().likeReason}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_16"
            app:chatItemBackground="@{msg}"
            app:chatListener="@{chatListener}"
            app:chatMessage="@{msg}"
            app:msgContentLayout="@{msg}"
            tools:background="@drawable/chat_bg_message_to4"
            tools:ignore="SpUsage"
            tools:layout_marginStart="12dp"
            tools:text="喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的" />

        <ImageView
            android:id="@+id/iv_heart"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@mipmap/chat_ic_hearts"
            android:visibility="gone"
            app:heartVisibility="@{msg}"
            tools:ignore="ContentDescription"
            tools:visibility="visible" />

    </FrameLayout>

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="chatListener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

    </data>

</layout>