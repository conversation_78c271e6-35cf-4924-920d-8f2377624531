<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">


    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/ll_you_like_me"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="left"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:singleLine="true"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold"
                app:nickName="@{msg.chatId}"
                tools:text="Cathy" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@{msg.getLikeInfo().getLikeModuleFromNameShow()}"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                tools:text="评论了你的照片" />
        </LinearLayout>

        <LinearLayout
            app:visibleGone="@{msg.getLikeInfo().showResourceInfo()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/ll_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:chatListener="@{listener}"
                app:chatMessage="@{msg}">

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/iv_pic"
                    android:layout_width="160dp"
                    android:layout_height="213dp"
                    android:scaleType="fitXY"
                    app:common_radius="10dp"
                    app:imageUrl="@{msg.getLikeInfo().abFace!=null?msg.getLikeInfo().abFace.subType==1? msg.getLikeInfo().abFace.photoA:msg.getLikeInfo().abFace.photoB:null}"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_A_B_FACE}" />

                <FrameLayout
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    app:layout_constrainedWidth="true"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_A_B_FACE}">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/common_bg_user_info_ab_face_desc"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="29dp"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="5dp"
                            app:abFaceIcon="@{msg.getLikeInfo().abFace.subType}"
                            tools:src="@mipmap/me_a_b_b_tips" />

                        <TextView
                            android:id="@+id/tv_content_b"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="2dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="16dp"
                            android:layout_marginBottom="10dp"
                            android:fontFamily="sans-serif-medium"
                            android:text="@{msg.getLikeInfo().abFace.subType==1? msg.getLikeInfo().abFace.titleA :  msg.getLikeInfo().abFace.titleB}"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_12"
                            tools:text="工作中工作中工作中工作中" />
                    </LinearLayout>

                </FrameLayout>

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="160dp"
                    android:layout_height="213dp"
                    android:scaleType="fitXY"
                    app:common_radius="10dp"
                    app:imageUrl="@{msg.getLikeInfo().likeAvatar!=null?msg.getLikeInfo().likeAvatar.avatar:null}"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_AVATAR}"
                    tools:visibility="gone" />

                <com.facebook.drawee.view.SimpleDraweeView
                    android:layout_width="160dp"
                    android:layout_height="213dp"
                    android:scaleType="centerCrop"
                    app:roundedCornerRadius="10dp"
                    app:imageCenterCropUrl="@{msg.getLikeInfo().story!=null?msg.getLikeInfo().story.photo:null}"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_STORY}" />

                <include
                    android:id="@+id/message_like_text_answer"
                    layout="@layout/chat_view_like_text_answer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:content="@{msg.getLikeInfo().answer.answer}"
                    app:title="@{msg.getLikeInfo().answer.question}"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_TEXT}"
                    tools:visibility="gone" />

                <include
                    layout="@layout/chat_view_like_voice_answer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:friend="@{true}"
                    app:msg="@{msg}"
                    app:title="@{msg.getLikeInfo().answer.question}"
                    app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_VOICE}"
                    tools:visibility="visible" />

            </FrameLayout>

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/iv_heart"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="bottom"
                android:layout_marginStart="10dp"
                android:src="@drawable/matching_ic_like_each_other_heart" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/chat_bg_message_from"
            android:paddingLeft="16dp"
            android:paddingTop="10dp"
            android:paddingRight="16dp"
            android:paddingBottom="10dp"
            android:text="@{msg.getLikeInfo().likeReason}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_16"
            android:visibility="@{TextUtils.isEmpty(msg.getLikeInfo().likeReason) ? View.GONE : View.VISIBLE}"
            tools:text="喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的" />

    </LinearLayout>

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import type="com.kanzhun.foundation.Constants" />

        <import type="com.kanzhun.foundation.model.message.MessageForLike.LikeInfo" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>