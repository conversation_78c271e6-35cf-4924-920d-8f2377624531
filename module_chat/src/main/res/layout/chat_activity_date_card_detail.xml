<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ChatDateCardDetailActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.DateCardDetailViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.DateCardDetailCallback" />
    </data>

    <FrameLayout
        android:id="@+id/fragment_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white"/>

</layout>