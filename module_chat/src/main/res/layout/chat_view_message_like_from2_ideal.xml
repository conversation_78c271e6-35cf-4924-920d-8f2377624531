<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom">

        <include
            android:id="@+id/ic_avatar"
            layout="@layout/chat_view_message_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:listener="@{listener}"
            app:msg="@{msg}" />

        <LinearLayout
            android:id="@+id/ll_you_like_me"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/chat_message_item_avatar_content_margin"
            android:gravity="start"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ic_avatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage,ContentDescription">

            <include
                layout="@layout/chat_view_message_like_from2_nickname"
                app:msg="@{msg}" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="4dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="6dp"
                    android:background="@drawable/chat_bg_rect_gray_radius_4dp"
                    app:visibleGone="@{msg.resourceType!=Constants.TYPE_LIKE_USER}"
                    tools:visibility="visible" />

                <FrameLayout
                    android:id="@+id/ll_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:chatListener="@{listener}"
                    app:chatMessage="@{msg}"
                    app:visibleGone="@{msg.getLikeInfo().showResourceInfo()}">

                    <!-- 32-理想型 -->
                    <include
                        android:id="@+id/message_like_ideal"
                        layout="@layout/chat_view_like_ideal"
                        android:layout_width="@dimen/chat_view_message_like_card_width"
                        android:layout_height="wrap_content"
                        app:content="@{msg.getLikeInfo().idealPartner.idealPartnerDesc}"
                        app:title="@{msg.getLikeInfo().likeModuleName}"
                        app:visibleGone="@{msg.resourceType==Constants.TYPE_LIKE_IDEAL_PARTNER_DESC}"
                        tools:visibility="visible" />

                </FrameLayout>

            </LinearLayout>

            <include
                layout="@layout/chat_view_message_like_reason_from"
                app:chatListener="@{listener}"
                app:msg="@{msg}" />

        </LinearLayout>

    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import type="com.kanzhun.foundation.Constants" />

        <import type="com.kanzhun.foundation.model.message.MessageForLike.LikeInfo" />

        <import type="com.kanzhun.foundation.kotlin.me.MeInfoExtKt" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

    </data>

</layout>