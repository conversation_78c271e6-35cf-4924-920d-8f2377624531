<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.kanzhun.foundation.model.message.MessageConstants" />

        <import type="android.text.TextUtils" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.ChatMessage" />

        <variable
            name="showReply"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/chat_bg_message_reply"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:paddingEnd="10dp"
        android:paddingBottom="5dp"
        android:visibility="@{(msg.replyId > 0 &amp;&amp; showReply != null &amp;&amp; showReply) ? View.VISIBLE : View.GONE}">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="1.0" />

        <TextView
            android:id="@+id/tv_quote_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/fl_quote_content"
            app:layout_constraintTop_toTopOf="parent"
            app:quoteToMsgName='@{msg}'
            app:visibleGone="@{TextUtils.isEmpty(msg.quoteMessage.illegalText) &amp;&amp;  (msg.quoteMessage.mediaType != MessageConstants.MSG_TEXT &amp;&amp; msg.quoteMessage.mediaType != MessageConstants.MSG_HINT) &amp;&amp; !msg.quoteMessage.deleted }"
            tools:singleLine="true"
            tools:text="huifu" />

        <FrameLayout
            android:id="@+id/fl_quote_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:friend="@{true}"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toRightOf="@+id/tv_quote_name"
            app:layout_constraintRight_toLeftOf="@+id/gl"
            app:layout_constraintTop_toTopOf="parent"
            app:quoteToMsg="@{msg}" />


        <!--        <TextView-->
        <!--            android:id="@+id/tv_content"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:text="ni好烦的是非得nini好烦的是非得nini好烦的是非得ni"-->
        <!--            app:layout_constrainedWidth="true"-->
        <!--            app:layout_constraintHorizontal_bias="0"-->
        <!--            app:layout_constraintLeft_toRightOf="@+id/tv_quote_name"-->
        <!--            app:layout_constraintRight_toLeftOf="@+id/gl"-->
        <!--            app:layout_constraintTop_toTopOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>