<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        app:deleteQuoteMsg="@{msg.extension}"
        android:textColor="@color/common_color_707070"
        android:textSize="@dimen/common_text_sp_14"
        tools:textColor="@color/common_white"
        tools:background="@color/common_color_191919"
        tools:text="好呀哈哈下次尝尝这款好呀哈哈下次尝尝这款好呀哈哈下次尝尝这款好呀哈哈下次尝尝这款好呀哈哈下次尝尝这款好呀哈哈下次尝尝这款" />

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.ChatMessage" />

        <variable
            name="friend"
            type="Boolean" />

    </data>
</layout>