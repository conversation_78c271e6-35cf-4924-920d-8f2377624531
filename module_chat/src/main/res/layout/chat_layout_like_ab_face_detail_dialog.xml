<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginLeft="32dp"
        android:layout_marginRight="32dp"
        android:layout_marginTop="88dp"
        android:layout_marginBottom="120dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


    <com.kanzhun.foundation.views.ABImpressionView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/faceView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>