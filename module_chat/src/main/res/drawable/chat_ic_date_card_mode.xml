<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="14dp"
    android:height="15dp"
    android:viewportWidth="14"
    android:viewportHeight="15">
  <path
      android:pathData="M-218.5,-512.5l375,0l0,812l-375,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="-31"
          android:startY="-106.5"
          android:endX="-31"
          android:endY="299.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M-221,-513h375v812h-375z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="-33.5"
          android:startY="-513"
          android:endX="-33.5"
          android:endY="-297.128"
          android:type="linear">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7,7.46m-6.5,0a6.5,6.5 0,1 1,13 0a6.5,6.5 0,1 1,-13 0"
      android:strokeWidth="1"
      android:fillColor="#00000000"
      android:strokeColor="#0D88C1"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M7.314,9.101L7.314,8.885C7.314,8.632 7.37,8.407 7.492,8.201C7.586,8.032 7.727,7.863 7.933,7.685C8.346,7.319 8.599,7.075 8.693,6.963C8.927,6.653 9.049,6.288 9.049,5.866C9.049,5.303 8.871,4.862 8.524,4.544C8.158,4.206 7.68,4.047 7.08,4.047C6.405,4.047 5.879,4.253 5.504,4.665C5.129,5.059 4.951,5.584 4.951,6.241L5.692,6.241C5.692,5.781 5.795,5.425 6.001,5.162C6.226,4.853 6.573,4.703 7.042,4.703C7.436,4.703 7.745,4.806 7.961,5.031C8.167,5.237 8.28,5.528 8.28,5.903C8.28,6.166 8.186,6.41 7.999,6.644C7.942,6.719 7.83,6.832 7.68,6.982C7.173,7.432 6.864,7.788 6.733,8.069C6.62,8.304 6.564,8.576 6.564,8.885L6.564,9.101L7.314,9.101ZM6.939,10.873C7.098,10.873 7.23,10.817 7.342,10.714C7.455,10.61 7.511,10.479 7.511,10.31C7.511,10.151 7.455,10.02 7.352,9.916C7.239,9.804 7.098,9.757 6.939,9.757C6.78,9.757 6.648,9.804 6.536,9.916C6.423,10.02 6.376,10.151 6.376,10.31C6.376,10.47 6.423,10.601 6.536,10.714C6.648,10.817 6.78,10.873 6.939,10.873Z"
      android:strokeWidth="0.4"
      android:fillColor="#0D88C1"
      android:fillType="nonZero"
      android:strokeColor="#0D88C1"/>
</vector>
