<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
    
    <!-- Green chat bubble -->
    <path
        android:pathData="M30,20C19.07,20 10,29.07 10,40C10,50.93 19.07,60 30,60C30.88,60 31.74,59.93 32.58,59.8L50,70V52.06C54.39,47.2 57,43.84 57,40C57,29.07 47.93,20 37,20H30Z"
        android:fillColor="#A7E8BA"/>
    
    <!-- Black circle with "Hi" text -->
    <path
        android:pathData="M80,30m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0"
        android:fillColor="#333333"/>
    
    <!-- Blue chat bubble -->
    <path
        android:pathData="M80,50C69.07,50 60,59.07 60,70C60,80.93 69.07,90 80,90C80.88,90 81.74,89.93 82.58,89.8L100,100V82.06C104.39,77.2 107,73.84 107,70C107,59.07 97.93,50 87,50H80Z"
        android:fillColor="#91A4FC"/>

    <!-- Smile in green bubble -->
    <path
        android:pathData="M30,40m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0"
        android:fillColor="#FFFFFF"/>
    <path
        android:pathData="M30,42C32.5,42 35,41 35,38"
        android:strokeWidth="1.5"
        android:strokeColor="#FFFFFF"
        android:strokeLineCap="round"/>

    <!-- Smile in blue bubble -->
    <path
        android:pathData="M80,70m-5,0a5,5 0,1 1,10 0a5,5 0,1 1,-10 0"
        android:fillColor="#FFFFFF"/>
    <path
        android:pathData="M80,72C82.5,72 85,71 85,68"
        android:strokeWidth="1.5"
        android:strokeColor="#FFFFFF"
        android:strokeLineCap="round"/>
    
    <!-- "Hi" text in black circle -->
    <path
        android:pathData="M77,30L77,30A1,1 0,0 1,78 31L78,35A1,1 0,0 1,77 36L77,36A1,1 0,0 1,76 35L76,31A1,1 0,0 1,77 30z"
        android:fillColor="#FFFFFF"/>
    <path
        android:pathData="M82,30L82,30A1,1 0,0 1,83 31L83,35A1,1 0,0 1,82 36L82,36A1,1 0,0 1,81 35L81,31A1,1 0,0 1,82 30z"
        android:fillColor="#FFFFFF"/>
    <path
        android:pathData="M77,33L83,33"
        android:strokeWidth="1"
        android:strokeColor="#FFFFFF"/>
    
    <!-- Decorative elements -->
    <path
        android:pathData="M15,15L20,10"
        android:strokeWidth="2"
        android:strokeColor="#FFCE54"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105,25L110,20"
        android:strokeWidth="2"
        android:strokeColor="#FC9EA8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100,105L105,100"
        android:strokeWidth="2"
        android:strokeColor="#FFCE54"
        android:strokeLineCap="round"/>
</vector> 