package com.kanzhun.marry.chat.views

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.attachOwners
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon.Companion.CHAT_EVENT_KEY_SHOW_ALBUM
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon.Companion.CHAT_EVENT_KEY_SHOW_KEYBOARD
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon.Companion.CHAT_EVENT_KEY_TACIT_TEST
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.kotlin.ext.sendStringLiveEvent
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.simpleGet
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.MessageForMeetingPlanTaskCard
import com.kanzhun.imageviewer.tools.ScreenUtils
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.activity.SingleChatActivity
import com.kanzhun.marry.chat.fragment.singlechat.SingleChatFragment
import com.techwolf.lib.tlog.TLog


fun bindMeetingPlanTip(
    composeView: ComposeView,
    message: MessageForMeetingPlanTaskCard,
) {
    val meetupTask = message.meetupTask

    val user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue()
    val contact: Contact? =
        ServiceManager.getInstance().getContactService().getContactById(message.chatId)
    var leftAvatarUrl = ""
    var rightAvatarUrl = ""
    if (user?.isMan == true) {
        leftAvatarUrl = user.tinyAvatar ?: ""
        rightAvatarUrl = contact?.tinyAvatar ?: ""
    } else {
        leftAvatarUrl = contact?.tinyAvatar ?: ""
        rightAvatarUrl = user?.tinyAvatar ?: ""
    }
    var leftEnd = false
    var rightEnd = false

    val jumpProto = if (AccountHelper.getInstance().userId.equals(meetupTask?.maleUserId)) {
        if (meetupTask?.maleUserStatus == 1) {
            leftEnd = true
            rightEnd = meetupTask?.femaleUserStatus == 1
        } else {
            leftEnd = false
            rightEnd = false
        }
        meetupTask?.maleJumpProto
    } else {
        if (meetupTask?.femaleUserStatus == 1) {
            leftEnd = meetupTask?.maleUserStatus == 1
            rightEnd = true
        } else {
            leftEnd = false
            rightEnd = false
        }
        meetupTask?.femaleJumpProto
    }

    composeView.apply {
        onSetWindowContent {
            MeetingPlanCard(
                style = (meetupTask?.type ?: 1) - 1,
                content = meetupTask?.content ?: "",
                isLeftEnd = leftEnd,
                isRightEnd = rightEnd,
                leftAvatarUrl = leftAvatarUrl,
                rightAvatarUrl = rightAvatarUrl,
                jumpProto = jumpProto ?: "",
                msgId = message.mid.toString()
            )
        }

        val activity = context as? ComponentActivity
        activity?.let { owner ->
            attachOwners(owner = owner, targetView = composeView)
        }
    }
}

@Preview
@Composable
private fun PreviewMeetingPlanTip() {
    MeetingPlanCard()
}

@Preview
@Composable
private fun PreviewMeetingPlanTip1() {
    MeetingPlanCard(style = 1)
}

@Preview
@Composable
private fun PreviewMeetingPlanTip2() {
    MeetingPlanCard(style = 2)
}

@Preview
@Composable
private fun PreviewMeetingPlanTip3() {
    MeetingPlanCard(style = 3)
}

@Preview
@Composable
private fun PreviewMeetingPlanTip4() {
    MeetingPlanCard(style = 4)
}

@Preview
@Composable
private fun PreviewMeetingPlanTipEnd() {
    MeetingPlanCard(isLeftEnd = true)
}

@Preview
@Composable
private fun PreviewMeetingPlanTip1End() {
    MeetingPlanCard(style = 1, isRightEnd = true)
}

@Preview
@Composable
private fun PreviewMeetingPlanTip2End() {
    MeetingPlanCard(style = 2, isLeftEnd = true, isRightEnd = true)
}


@Preview
@Composable
fun MeetingPlanTip() {
    MeetingPlanTiptop()
}

@OptIn(ExperimentalComposeUiApi::class)
@SuppressLint("ClickableViewAccessibility")
@Composable
fun MeetingPlanCard(
    style: Int = 0,
    content: String = "欢迎开启这段旅程，我们在接下来的聊天中为你们设计了不同任务来破冰畅聊。现在开始你们的聊天吧~",
    isLeftEnd: Boolean = false,
    isRightEnd: Boolean = false,
    leftAvatarUrl: String = "",
    rightAvatarUrl: String = "",
    jumpProto: String = "",
    msgId: String = ""
) {
    val context = LocalContext.current
    val handler = remember { Handler(Looper.getMainLooper()) }

    // 跟踪录音状态
    var isRecording by remember { mutableStateOf(false) }
    var cY by remember { mutableStateOf(0f) }

    // 获取 Fragment 的引用
    val fragmentRef = remember { mutableStateOf<SingleChatFragment?>(null) }
    LaunchedEffect(Unit) {
        val activity = context as? FragmentActivity
        activity?.let { act ->
            val fragment = act.supportFragmentManager.findFragmentByTag(
                SingleChatActivity.TAG
            ) as? SingleChatFragment
            fragmentRef.value = fragment
        }
    }

    ConstraintLayout(
        modifier = Modifier
            .noRippleClickable {
                if (style == 4) {
                    "orange/meetup/chat/unLockClick".simpleGet(mapOf("msgId" to msgId))
                    ProtocolHelper.parseProtocol(jumpProto)
                }
            }
            .fillMaxWidth()
    ) {
        val (imageBg, leftIcon, resText, resBtn) = createRefs()
        Image(
            painter = when (style) {
                0 -> R.drawable.image_meeting_plan_bg_level_0.painterResource()
                1 -> R.drawable.image_meeting_plan_bg_level_1.painterResource()
                2 -> R.drawable.image_meeting_plan_bg_level_2.painterResource()
                3 -> R.drawable.image_meeting_plan_bg_level_3.painterResource()
                else -> R.drawable.image_meeting_plan_bg_level_4.painterResource()
            },
            contentDescription = "",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .constrainAs(imageBg) {
                    top.linkTo(leftIcon.top, 20.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(resBtn.bottom)
                    height = Dimension.fillToConstraints
                })

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(leftIcon) {
                    top.linkTo(parent.top)
                    end.linkTo(parent.end, 12.dp)
                    start.linkTo(parent.start, 12.dp)
                    width = Dimension.fillToConstraints
                },
        ) {
            Image(
                painter = when (style) {
                    0 -> R.drawable.image_meeting_plan_txt_level_0.painterResource()
                    1 -> R.drawable.image_meeting_plan_txt_level_1.painterResource()
                    2 -> R.drawable.image_meeting_plan_txt_level_2.painterResource()
                    3 -> R.drawable.image_meeting_plan_txt_level_3.painterResource()
                    else -> R.drawable.image_meeting_plan_txt_level_4.painterResource()
                },
                contentDescription = "",
                modifier = Modifier.weight(676.0f)
            )

            Column(modifier = Modifier.weight(480.0f)) {
                Spacer(modifier = Modifier.height(5.dp))
                Image(
                    painter = when (style) {
                        0 -> R.drawable.image_meeting_plan_logo_level_0.painterResource()
                        1 -> R.drawable.image_meeting_plan_logo_level_1.painterResource()
                        2 -> R.drawable.image_meeting_plan_logo_level_2.painterResource()
                        3 -> R.drawable.image_meeting_plan_logo_level_4.painterResource()
                        else -> R.drawable.image_meeting_plan_logo_level_3.painterResource()
                    },
                    contentDescription = "",
                )

            }


        }



        Box(
            modifier = Modifier
                .constrainAs(resText) {
                    start.linkTo(parent.start, 24.dp)
                    top.linkTo(leftIcon.bottom, 12.dp)
                    end.linkTo(parent.end, 24.dp)
                    width = Dimension.fillToConstraints
                }, contentAlignment = Alignment.Center
        ) {
            Text(
                text = content,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF292929),
                )
            )
        }

        val btnColor = when (style) {
            0 -> Color(0xFFFCE7DE)
            1 -> Color(0xFFFDDAFF)
            2 -> Color(0xFFBEFAE7)
            3 -> Color(0xFFC5EEF9)
            4 -> Color(0xFFE1DBFB)
            else -> Color(0xFFE1DBFB)
        }

        Column(modifier = Modifier.constrainAs(resBtn) {
            start.linkTo(parent.start, 24.dp)
            end.linkTo(parent.end, 24.dp)
            top.linkTo(resText.bottom, 20.dp)
            width = Dimension.fillToConstraints
        }) {

            if (isLeftEnd || isRightEnd) {
                isEndView(btnColor, leftAvatarUrl, rightAvatarUrl, isLeftEnd, isRightEnd)
            } else {
                if (style == 3) {
                    // 按钮部分
                    Text(
                        text = "长按录制语音",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(700),
                            color = Color(0xFF0A4859),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = btnColor,
                                shape = RoundedCornerShape(25.dp)
                            )
                            .pointerInteropFilter { event ->
                                val fragment = fragmentRef.value
                                if (fragment == null) {
                                    return@pointerInteropFilter false
                                }

                                when (event.action) {
                                    MotionEvent.ACTION_DOWN -> {

                                        isRecording = true

                                        // 冻结RecyclerView的滑动
                                        disableScrolling(fragment)

                                        // 直接调用Fragment的处理方法
                                        handler.post {
                                            val activity = context as? FragmentActivity
                                            activity?.let {
                                                cY =
                                                    ScreenUtils.getScreenHeight(activity) - event.rawY
                                                val y = event.rawY + cY
                                                val eventN = MotionEvent.obtain(
                                                    event.downTime,
                                                    event.eventTime,
                                                    MotionEvent.ACTION_DOWN,
                                                    event.rawX,
                                                    y,
                                                    0
                                                )
                                                fragment.handleVoiceRecordTouchEvent(eventN, it)
                                            }
                                        }
                                        true
                                    }

                                    MotionEvent.ACTION_MOVE -> {
                                        if (isRecording) {

                                            // 确保滑动仍被禁用
                                            disableScrolling(fragment)

                                            val y = event.rawY + cY
                                            val eventN = MotionEvent.obtain(
                                                event.downTime,
                                                event.eventTime,
                                                MotionEvent.ACTION_MOVE,
                                                event.rawX,
                                                y,
                                                0
                                            )

                                            handler.post {
                                                val activity = context as? FragmentActivity
                                                activity?.let {
                                                    fragment.handleVoiceRecordTouchEvent(eventN, it)
                                                }
                                            }
                                        }
                                        true
                                    }

                                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {

                                        if (isRecording) {
                                            isRecording = false

                                            // 恢复滑动
                                            enableScrolling(fragment)
                                            val y = event.rawY + cY
                                            val eventN = MotionEvent.obtain(
                                                event.downTime,
                                                event.eventTime,
                                                MotionEvent.ACTION_UP,
                                                event.rawX,
                                                y,
                                                0
                                            )

                                            handler.post {
                                                val activity = context as? FragmentActivity
                                                activity?.let {
                                                    fragment.handleVoiceRecordTouchEvent(eventN, it)
                                                }
                                            }
                                        }
                                        true
                                    }

                                    else -> false
                                }
                            }
                            .padding(vertical = 12.dp)
                    )
                } else {
                    Text(
                        text = when (style) {
                            0 -> "去和Ta打个招呼"
                            1 -> "GO"
                            2 -> "去完成"
                            3 -> "长按录制语音"
                            else -> "点击解锁"
                        },
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(700),
                            color = when (style) {
                                0 -> Color(0xFF974335)
                                1 -> Color(0xFF913E91)
                                2 -> Color(0xFF191919)
                                3 -> Color(0xFF0A4859)
                                else -> Color(0xFF6045C8)
                            },
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = btnColor,
                                shape = RoundedCornerShape(25.dp)
                            )
                            .padding(vertical = 12.dp)
                            .noRippleClickable {
                                when (style) {
                                    0 -> {
                                        // 拉起键盘
                                        sendStringLiveEvent(
                                            LivedataKeyCommon.EVENT_HANDLER_CHAT,
                                            CHAT_EVENT_KEY_SHOW_KEYBOARD
                                        )
                                    }

                                    1 -> {
                                        //默契考验
                                        sendStringLiveEvent(
                                            LivedataKeyCommon.EVENT_HANDLER_CHAT,
                                            CHAT_EVENT_KEY_TACIT_TEST
                                        )
                                    }

                                    2 -> {
                                        //吊起相册
                                        sendStringLiveEvent(
                                            LivedataKeyCommon.EVENT_HANDLER_CHAT,
                                            CHAT_EVENT_KEY_SHOW_ALBUM
                                        )
                                    }

                                    3 -> {
                                    }

                                    else -> {
                                        //
                                        "orange/meetup/chat/unLockClick".simpleGet(mapOf("msgId" to msgId))
                                        ProtocolHelper.parseProtocol(jumpProto)
                                    }
                                }
                            }
                    )
                }


            }

            Spacer(modifier = Modifier.height(24.dp))
        }


    }
}

@Preview
@Composable
fun PreviewIsEndView() {
    isEndView(Color(0xFFE1DBFB), "", "", true, true)
}

@Composable
private fun isEndView(
    btnColor: Color,
    leftAvatarUrl: String,
    rightAvatarUrl: String,
    isLeftEnd: Boolean,
    isRightEnd: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .height(52.dp)
            .background(
                color = btnColor,
                shape = RoundedCornerShape(size = 16.dp)
            )
            .padding(horizontal = 8.dp),
        horizontalArrangement = Arrangement.SpaceAround
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            AsyncImage(
                model = leftAvatarUrl,
                contentDescription = null,
                modifier = Modifier
                    .size(36.dp)
                    .border(2.dp, Color(0xFFFFFFFF), shape = CircleShape)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop,
            )
            Spacer(modifier = Modifier.width(8.dp))
            Image(
                painter = if (isLeftEnd) R.drawable.chat_image_meeting_plan_card_end_txt.painterResource() else R.drawable.chat_image_meeting_plan_card_wait_txt.painterResource(),
                contentDescription = "",
                modifier = Modifier.size(52.dp, 22.dp)
            )
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            AsyncImage(
                model = rightAvatarUrl,
                contentDescription = null,
                modifier = Modifier
                    .size(36.dp)
                    .border(2.dp, Color(0xFFFFFFFF), shape = CircleShape)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop,
            )
            Spacer(modifier = Modifier.width(8.dp))
            Image(
                painter = if (isRightEnd) R.drawable.chat_image_meeting_plan_card_end_txt.painterResource() else R.drawable.chat_image_meeting_plan_card_wait_txt.painterResource(),
                contentDescription = "",
                modifier = Modifier.size(52.dp, 22.dp)
            )
        }
    }
}

// 添加新的辅助函数来禁用滑动
private fun disableScrolling(fragment: SingleChatFragment) {
    try {
        // 设置RecyclerView为不可用，阻止用户交互
        fragment.contentRecyclerView?.let { recyclerView ->
            recyclerView.suppressLayout(true) // 这个方法直接禁用了RecyclerView的所有滑动

            // 直接锁定所有父级视图
            var parent: View? = recyclerView
            while (parent != null) {
                if (parent is ViewGroup) {
                    parent.requestDisallowInterceptTouchEvent(true)
                }
                parent = parent.parent as? View
            }
        }

        //  禁用根视图的触摸事件
        fragment.getDataBinding().mRootView?.let { rootView ->
            rootView.isEnabled = false
        }

        //  禁用PanelSwitchLayout
        fragment.getDataBinding().flRootViewContainer?.let { flRootView ->
            flRootView.isEnabled = false
        }
    } catch (e: Exception) {
        TLog.print("testzhang", "Failed to disable scrolling: ${e.message}")
    }
}

// 添加新的辅助函数来恢复滑动
private fun enableScrolling(fragment: SingleChatFragment) {
    try {
        //  恢复RecyclerView的滑动
        fragment.contentRecyclerView?.let { recyclerView ->
            recyclerView.suppressLayout(false)

            // 恢复所有父级视图
            var parent: View? = recyclerView
            while (parent != null) {
                if (parent is ViewGroup) {
                    parent.requestDisallowInterceptTouchEvent(false)
                }
                parent = parent.parent as? View
            }
        }

        // 恢复根视图的触摸事件
        fragment.getDataBinding().mRootView?.let { rootView ->
            rootView.isEnabled = true
        }

        // 恢复PanelSwitchLayout
        fragment.getDataBinding().flRootViewContainer?.let { flRootView ->
            flRootView.isEnabled = true
        }
    } catch (e: Exception) {
        TLog.print("testzhang", "Failed to enable scrolling: ${e.message}")
    }
}

/**
 * 滚动到聊天页的任务卡片区域
 * @param fragment 聊天页Fragment
 * @param taskType 任务类型 1 初次打招呼 2 默契考验 3 交换照片 4 互发语音 5 解锁完成匹配报告 10 见面邀约
 * @param msgId 消息ID
 */
fun scrollToTaskCardMessage(fragment: SingleChatFragment, taskType: Int, msgId: Long) {
    try {
        // 获取聊天消息列表
        val adapter = fragment.chatAdapter
        val messageList = adapter.data

        // 查找对应的任务卡片消息
        var targetPosition = -1

        // 对于其他任务类型，查找对应的MessageForMeetingPlanTaskCard类型的消息
        for (i in messageList.indices) {
            val message = messageList[i]
            if (message is MessageForMeetingPlanTaskCard) {
                // 如果提供了msgId，则匹配msgId
                if (msgId > 0 && message.mid == msgId) {
                    targetPosition = i
                    break
                } else {
                    // 否则匹配任务类型
                    val meetupTask = message.meetupTask
                    if (meetupTask?.type == taskType + 1) {
                        targetPosition = i
                        break
                    }
                }
            }
        }

        // 如果找到了目标位置，滚动到该位置
        if (targetPosition >= 0) {
            val recyclerView = fragment.contentRecyclerView
            recyclerView?.post {
                val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
                layoutManager?.scrollToPositionWithOffset(targetPosition, 0)
            }
        }
    } catch (e: Exception) {
        TLog.print("MeetingPlanTip", "Failed to scroll to task card: ${e.message}")
    }
}

@Composable
fun MeetingPlanTiptop(position: Int = 0, allCount: Int = 0, day: Int = 1) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = RoundedCornerShape(12.dp))
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFFFFEAFF),
                        Color(0xFFFFDEF6),
                        Color(0xFFFFD2BD),
                        Color(0xFFFFC2BC),
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Text(
                text = buildAnnotatedString {
                    append("任务进度 ")

                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFAD42A7),
                        )
                    ) {
                        append("${position}/${allCount}")
                    }
                },
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                ),
                modifier = Modifier
            )

            Spacer(
                modifier = Modifier
                    .width(1.dp)
                    .height(8.dp)
                    .background(color = Color(0xFFFBB59B))
            )

            Text(
                text = buildAnnotatedString {
                    append("相处 ")

                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFAD42A7),
                        )
                    ) {
                        append("${day}")
                    }

                    append(" 天")
                },
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                ),
                modifier = Modifier
            )
        }
    }
}


