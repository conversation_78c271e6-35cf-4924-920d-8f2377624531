package com.kanzhun.marry.chat.emotion

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.emotion.data.EmotionItem
import com.kanzhun.utils.base.LList

class ExtendEmotionAdapter(private val context: Context?) :
    RecyclerView.Adapter<ExtendEmotionAdapter.EmotionContainerVH>() {
    private var iEmotionCallBack: IEmotionCallBack? = null
    private val result: MutableList<EmotionItem> = ArrayList()

    fun setiEmotionCallBack(iEmotionCallBack: IEmotionCallBack?) {
        this.iEmotionCallBack = iEmotionCallBack
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: List<EmotionItem>?) {
        result.clear()
        if (data != null) {
            result.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EmotionContainerVH {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.twl_ui_emotion_container_item, parent, false)
        return EmotionContainerVH(view)
    }

    override fun onBindViewHolder(holder: EmotionContainerVH, position: Int) {
        val item = LList.getElement(result, position)
        if (item != null) {
            EmotionLoadUtil.loadEmotionUrl(holder.mEmotionView, item.tinyUrl)
            holder.mTextView.text = item.name
            holder.itemView.setOnClickListener {
                iEmotionCallBack?.onItemEmotionClickListener(item)
            }
        }
    }

    override fun getItemCount(): Int {
        return LList.getCount(result)
    }

    class EmotionContainerVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        internal val mEmotionView: OImageView = itemView.findViewById(R.id.mEmotionView)
        val mTextView: TextView = itemView.findViewById(R.id.mTextView)
    }
}