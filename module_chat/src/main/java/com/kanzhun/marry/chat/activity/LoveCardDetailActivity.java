package com.kanzhun.marry.chat.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.kanzhun.marry.chat.fragment.LoveCardFinishedSenderFragment;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.statelayout.StateLayoutManager;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.callback.LoveCardDetailCallback;
import com.kanzhun.marry.chat.databinding.ChatActivityLoveCardDetailBinding;
import com.kanzhun.marry.chat.fragment.LoveCardFinishedFragment;
import com.kanzhun.marry.chat.fragment.LoveCardReceiveFragment;
import com.kanzhun.marry.chat.fragment.LoveCardSenderFragment;
import com.kanzhun.marry.chat.viewmodel.LoveCardDetailViewModel;

/**
 * 消息 - 表白信详情
 * <p>
 * Created by Qu Zhiyong on 2022/5/30
 */
@RouterUri(path = ChatPageRouter.LOVE_CARD_DETAIL_ACTIVITY)
public class LoveCardDetailActivity extends FoundationVMActivity<ChatActivityLoveCardDetailBinding, LoveCardDetailViewModel> implements LoveCardDetailCallback {

    private StateLayoutManager stateLayoutManager;

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_love_card_detail;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        QMUIStatusBarHelper.setStatusBarLightMode(this);
        stateLayoutManager = new StateLayoutManager(this, getDataBinding().fragmentContent);

        getViewModel().msgId = getIntent().getStringExtra(BundleConstants.BUNDLE_MSG_ID);
        getViewModel().msgSenderIdField.set(getIntent().getStringExtra(BundleConstants.BUNDLE_MSG_SENDER_ID));
        getViewModel().getLoveCard();

        getViewModel().getLoadingLiveData().observe(this, show -> {
            if (show) {
                stateLayoutManager.showLoadingView();
            } else {
                stateLayoutManager.dismiss();
            }
        });
        getViewModel().getLoveCardLiveData().observe(this, statusBean -> {
            if (!TextUtils.equals(AccountHelper.getInstance().getUserId(), getViewModel().msgSenderIdField.get())) {// 别人给我的
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_content, statusBean.reply > 0 ? new LoveCardFinishedFragment() : new LoveCardReceiveFragment())
                        .commit();
            } else {
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_content, statusBean.reply > 0 ? new LoveCardFinishedSenderFragment() : new LoveCardSenderFragment())
                        .commit();
            }
        });

        getViewModel().getFinishLiveData().observe(this, finish -> {
            if (finish) {
                AppUtil.finishActivity(LoveCardDetailActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void finish() {
        super.finish();
        AppUtil.finishActivityAnim(this, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }
}