package com.kanzhun.marry.chat.guard.ui

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.size.Size
import com.kanzhun.common.base.compose.ext.dpToPx
import com.kanzhun.common.photo.image.previewer.ImagePreviewer
import com.kanzhun.common.photo.image.previewer.defaultPreviewBackground
import com.kanzhun.common.photo.zoomable.pager.PagerGestureScope
import com.kanzhun.common.photo.zoomable.previewer.PreviewerState
import com.kanzhun.common.photo.zoomable.previewer.TransformLayerScope
import kotlinx.coroutines.delay

@Composable
fun PreviewImage(
    previewerState: PreviewerState,
    photos: List<String> = emptyList(),
    onImagePageChanged: (String, Int) -> Unit = { _, _ -> },
    onClosePreview: () -> Unit = {},
) {
    var sharePhotoUrl by remember { mutableStateOf("") }
    if (sharePhotoUrl.isNotEmpty()) BackHandler { sharePhotoUrl = "" }

    val onPageChanged by rememberUpdatedState(onImagePageChanged)
    LaunchedEffect(previewerState.pagerState) {
        delay(100)
        snapshotFlow { previewerState.pagerState.currentPage }
            .collect { page ->
                if (page >= 0 && page < photos.size) {
                    val photoItem = photos[page]

                    onPageChanged(photoItem, page)
                }
            }
    }

    ImagePreviewer(
        state = previewerState,
        detectGesture = PagerGestureScope(
            onTap = {
                onClosePreview()
            }
        ),
        imageLoader = { index ->
            val image = photos[index]

            val data = image

            val configuration = LocalConfiguration.current
            val screenWidth = configuration.screenWidthDp.dp.dpToPx().toInt()
            val screenHeight = configuration.screenHeightDp.dp.dpToPx().toInt()

            val imageRequest =
                ImageRequest.Builder(LocalContext.current)
                    .data(data)
                    .size(Size(screenWidth, screenHeight))
                    .build()

            val painter = rememberAsyncImagePainter(imageRequest)

            Pair(painter, painter.intrinsicSize)
        },
        previewerLayer = TransformLayerScope(
            background = defaultPreviewBackground,
            foreground = {
                val page = previewerState.pagerState.currentPage
                if (page >= 0 && page < photos.size) {
                    Box(
                        modifier = Modifier
                            .statusBarsPadding()
                            .fillMaxSize()
                    ) {
                        Indicator(
                            modifier = Modifier.align(alignment = Alignment.BottomCenter),
                            pageCount = photos.size,
                            currentPage = page
                        )
                    }
                }
            }
        ),
    )
}

@Composable
private fun Indicator(modifier: Modifier = Modifier, pageCount: Int = 0, currentPage: Int = 0) {
    Row(modifier = modifier.padding(12.dp), horizontalArrangement = Arrangement.spacedBy(12.dp)) {
        for (i in 0 until pageCount) {
            val selected = i == currentPage
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(
                        color = if (selected) Color(0xFFFFFFFF) else Color(0x33FFFFFF),
                        shape = CircleShape
                    )
            )
        }
    }
}

@Preview
@Composable
private fun IndicatorPreview() {
    Indicator(pageCount = 5, currentPage = 2)
}