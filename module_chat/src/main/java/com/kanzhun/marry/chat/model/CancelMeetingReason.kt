package com.kanzhun.marry.chat.model

import java.io.Serializable

/**
 * Model class for meeting cancellation reasons
 */
data class CancelMeetingReason(
    val code: Int,
    val desc: String,
    var isSelected: Boolean = false
) : Serializable

/**
 * Response model for meeting cancellation reasons API
 */
data class CancelMeetingReasonResponse(
    val rows: List<CancelMeetingReason>
) : Serializable
