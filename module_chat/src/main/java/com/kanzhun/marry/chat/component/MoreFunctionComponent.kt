package com.kanzhun.marry.chat.component

import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.chad.library.adapter.base.BaseBinderAdapter
import com.kanzhun.common.adpter.BaseDataBindingItemBinder
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.base.compose.ui.RoundedSquareProgress
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.ActivityAnimType
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.media.IMeetingService
import com.kanzhun.foundation.media.MediaConstants
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.page.VideoAudioFloatPageManager
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.TakePageRouter
import com.kanzhun.foundation.ui.Navigation
import com.kanzhun.http.callback.ICommonCallback
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.activity.TopicGameActivity
import com.kanzhun.marry.chat.databinding.ChatItemPanelFuncBinding
import com.kanzhun.marry.chat.model.MsgCountBean
import com.kanzhun.marry.chat.model.PanelFunc
import com.kanzhun.marry.chat.point.ChatPointReporter.Companion.reportChatToolsClick
import com.kanzhun.marry.chat.viewmodel.ChatViewModel
import com.kanzhun.utils.T
import com.sankuai.waimai.router.Router

class MoreFunctionComponent(
    val activity: FragmentActivity,
    val fragment: Fragment,
    val chatViewModel: ChatViewModel,
    private val moreFuncList: RecyclerView,
    val listener: OnMoreFuncListener
) {
    private var funcInputMoreAdapter: BaseBinderAdapter = BaseBinderAdapter()

    fun initMoreFuncList() {
        funcInputMoreAdapter.addItemBinder<PanelFunc>(
            PanelFunc::class.java,
            object : BaseDataBindingItemBinder<PanelFunc, ChatItemPanelFuncBinding>() {
                override fun getResLayoutId(): Int {
                    return R.layout.chat_item_panel_func
                }

                override fun bind(
                    holder: BinderDataBindingHolder<ChatItemPanelFuncBinding>,
                    binding: ChatItemPanelFuncBinding,
                    item: PanelFunc
                ) {
                    binding.setBean(item)

                    binding.progressBar.onSetWindowContent {
                        RoundedSquareProgress(
                            modifier = Modifier.size(64.dp),
                            progress = 10.coerceAtLeast(item.progress)
                        )
                    }

                    if (item.type == PanelFunc.TACIT_UNDERSTANDING_TEST) {
                        binding.composeView.visibility = View.VISIBLE
                        binding.composeView.onSetWindowContent {
                            val composition by rememberLottieComposition(
                                spec = LottieCompositionSpec.Asset(
                                    "tacit/tacit.json"
                                ),
                            )
                            LottieAnimation(
                                composition = composition,
                                iterations = 1,
                                modifier = Modifier.size(59.dp)
                            )
                        }
                    } else {
                        binding.composeView.visibility = View.GONE
                    }
                }

                override fun onClick(
                    holder: BinderDataBindingHolder<ChatItemPanelFuncBinding>,
                    view: View,
                    data: PanelFunc,
                    position: Int
                ) {
                    super.onClick(holder, view, data, position)
                    when (data.type) {
                        PanelFunc.TAKE_PHOTO -> {
                            listener.onHidePanelAndKeyboard()
                            listener.reversVoiceIcon()
                            clickTakePhoto()
                            reportToolsClick("拍照")
                        }

                        PanelFunc.CHOOSE_GALLERY -> {
                            clickPic()
                        }

                        PanelFunc.VOICE_CALL -> {
                            reportToolsClick("语音输入")
                            if (chatViewModel.contactLocked()) {
                                T.ss(R.string.common_contact_video_chat_notice)
                            }
                            if (VideoAudioFloatPageManager.getInstance().hasNoFloatPage()) {
                                listener.onHidePanelAndKeyboard() // 点击后收起更多相关view
                                val iMeetingService = Router.getService(
                                    IMeetingService::class.java,
                                    MediaConstants.ENGINE_LISTENER_KEY
                                )
                                iMeetingService.postLocalOverTimeMessage()
                                if (iMeetingService.alive()) {
                                    return
                                }
                                iMeetingService.setAlive(true)
                                val intent = Navigation.getVideoChatActivity(activity)
                                intent.putExtra(
                                    BundleConstants.BUNDLE_VIDEO_CHAT_TYPE,
                                    Constants.VIDEO_CHAT_AUDIO
                                )
                                intent.putExtra(
                                    BundleConstants.BUNDLE_VIDEO_CHAT_INVITATION,
                                    chatViewModel.chatId
                                )
                                AppUtil.startActivity(activity, intent)
                            }
                        }

                        PanelFunc.TOPIC_GAME -> {
                            reportToolsClick("话题游戏")
                            startAllTopicActivity()
                        }

                        PanelFunc.APPOINTMENT_CARD -> {
                            reportToolsClick("相识卡")
                            val dateCard: MsgCountBean = chatViewModel.cardProcessBean.dateCard
                            if (dateCard.status == 3) return  // 使用中不可点击
                            listener.onHidePanelAndKeyboard() // 点击后收起更多相关view
                            ChatPageRouter.jumpToDateCardActivity(activity, chatViewModel.chatId)
                        }

                        PanelFunc.CONFESSION_LETTER -> {
                            reportToolsClick("表白信")
                            val loveCard: MsgCountBean = chatViewModel.cardProcessBean.loveCard
                            if (loveCard.status == 3) return  // 使用中不可点击
                            val user =
                                ServiceManager.getInstance().profileService.userLiveData.value
                            if (user == null || TextUtils.isEmpty(user.nickName)) return
                            listener.onHidePanelAndKeyboard() // 点击后收起更多相关view
                            ChatPageRouter.jumpToLoveCardActivity(
                                activity, chatViewModel.chatId,
                                user.nickName,
                                chatViewModel.getNickName(chatViewModel.chatId)
                            )
                        }

                        PanelFunc.TACIT_UNDERSTANDING_TEST -> {
                            // 默契考验
                            reportToolsClick("默契考验")
                            // 跳转默契考验h5 页面
                            clickTacitTest()
                        }

                        PanelFunc.CHAT_ASSISTANT -> {
                            // 跳转AI聊天页面
                            reportToolsClick("聊天助手")
                            // 唤起聊天助手面板
                            listener.onShowAiAssistantPanel()
                        }
                        PanelFunc.CHANGE_WX -> {
                            reportToolsClick("交换微信")
                            ChatPageRouter.jumpToChangeWxActivity(activity, chatViewModel.chatId)
                        }

                        PanelFunc.PROTECT_MEET -> {
                            reportToolsClick("发起见面")
                            "orange/protect/meet/triggerInvite".simplePost(map = mapOf("chatId" to chatViewModel.chatId), autoErrorToast = true)
                        }

                        PanelFunc.LOCATION_SHARE -> {
                            reportToolsClick("位置分享")
                            "orange/location/share/send".simplePost(map = mapOf("chatId" to chatViewModel.chatId), autoErrorToast = true)
                        }
                    }
                }
            })
        moreFuncList.layoutManager = GridLayoutManager(activity, 4)
        moreFuncList.adapter = funcInputMoreAdapter
        funcInputMoreAdapter.setList(chatViewModel.panelFuncs)
    }

    fun clickPic() {
        listener.onHidePanelAndKeyboard()
        listener.reversVoiceIcon()
        clickSelectPhoto()
        reportToolsClick("图片")
    }

    fun clickTacitTest() {
        // 跳转默契考验h5 页面
        val jumpUrl = chatViewModel.cardProcessBean?.tacitTest?.jumpUrl
        ProtocolHelper.parseProtocol(jumpUrl)
    }

    fun updateList(conversation: Conversation?) {
        funcInputMoreAdapter.setList(chatViewModel.refreshPanelFunc(conversation != null && conversation.linkMsgFunc == Constants.LINK_MSG_FUNC_OPEN))

    }

    fun updateData() {
        chatViewModel.getCardProcess(object : ICommonCallback {
            override fun onSuccess() {
                funcInputMoreAdapter.setList(chatViewModel.panelFuncs)
            }
        })
        chatViewModel.dateLoveCardHasShown = true
    }

    /**
     * 埋点
     */
    fun reportToolsClick(name: String) {
        reportChatToolsClick(
            type = name,
            chatId = chatViewModel.chatId,
            conversation = chatViewModel.conversation
        )
    }

    /**
     * 拍照
     */
    private fun clickTakePhoto() {
        PermissionHelper.getCameraHelper(activity).setPermissionCallback { yes, permission ->
            if (yes) {
                TakePageRouter.jumpToCameraActivity(
                    fragment,
                    RequestCodeConstants.REQUEST_CODE_TAKE_CAMERA
                )
            } else {
//                T.ss(com.kanzhun.common.R.string.common_no_camera_audio_permission)
                T.ss(R.string.common_no_camera_permission)
            }
        }.requestPermission()
    }

    /**
     * 选择图片
     */
    private fun clickSelectPhoto() {

        PhotoSelectManager.jumpForGalleryFromChatResult(
            activity
        ) { fileList, _ ->
            chatViewModel.sendPictureMessage(
                fileList, chatViewModel.chatId,
                chatViewModel.type, chatViewModel.replyBean
            )
        }
    }

    /**
     * 打开话题游戏
     */
    private fun startAllTopicActivity() {
        val intent = Intent(activity, TopicGameActivity::class.java)
        intent.putExtra(BundleConstants.BUNDLE_DATA, chatViewModel.chatId)
        AppUtil.startActivity(activity, intent, ActivityAnimType.UP_GLIDE)
    }
}

interface OnMoreFuncListener {
    //隐藏功能面板和键盘
    fun onHidePanelAndKeyboard()

    fun reversVoiceIcon()

    fun onShowAiAssistantPanel()
}
