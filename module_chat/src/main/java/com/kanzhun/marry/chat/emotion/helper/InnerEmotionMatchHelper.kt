package com.kanzhun.marry.chat.emotion.helper

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import androidx.core.content.ContextCompat
import com.kanzhun.marry.chat.emotion.data.EmotionInnerManager
import com.kanzhun.marry.chat.emotion.data.EmotionItem
import com.kanzhun.marry.chat.emotion.data.Index
import com.qmuiteam.qmui.util.QMUIDisplayHelper

/**
 * create by guofeng
 * date on 2022/10/28
 */
object InnerEmotionMatchHelper {
    /**
     * 计算[]字符正在的位置坐标
     *
     * @param message
     * @return
     */
    @JvmStatic
    fun checkAllEmotion(message: String?): List<Index> {
        val indexList: MutableList<Index> = ArrayList()
        if (message != null) {
            val chars = message.toCharArray()
            val length = chars.size - 1
            var start = 0
            var end = 0
            while (start <= length && end <= length) {
                var canAdd = true
                if (chars[start] == '[') {
                    end = start + 1
                    while (end <= length) {
                        if (chars[end] == '[') {
                            start = end
                            canAdd = false
                            break
                        }
                        if (chars[end] == ']') {
                            val s = message.substring(start, end + 1)
                            val id = EmotionInnerManager.getResourceId(s)
                            if (id > 0) {
                                val index = Index()
                                index.start = start
                                index.end = end + 1
                                index.resourceId = id
                                indexList.add(index)
                            }
                            break
                        } else {
                            end++
                        }
                    }
                }
                if (canAdd) {
                    start++
                }
            }
        }
        return indexList
    }


    /**
     * 获取所有的emotion
     *
     * @param message
     * @return
     */
    @JvmStatic
    fun checkAllEmotionBeans(message: String?): List<EmotionItem> {
        val indexList: MutableList<EmotionItem> = ArrayList()
        if (message != null) {
            val chars = message.toCharArray()
            val length = chars.size - 1
            var start = 0
            var end = 0
            while (start <= length && end <= length) {
                var canAdd = true
                if (chars[start] == '[') {
                    end = start + 1
                    while (end <= length) {
                        if (chars[end] == '[') {
                            start = end
                            canAdd = false
                            break
                        }
                        if (chars[end] == ']') {
                            val s = message.substring(start, end + 1)
                            val bean = EmotionInnerManager.findGifByName(s) ?: break
                            if (bean.id > 0) {
                                indexList.add(bean)
                            }
                            break
                        } else {
                            end++
                        }
                    }
                }
                if (canAdd) {
                    start++
                }
            }
        }
        return indexList
    }


    /**
     * 文本内容 表情文本 -> emotion表情
     *
     * @param context
     * @param message
     * @return
     */
    @JvmStatic
    fun obtainEmotionSpannable(context: Context?, message: String?): SpannableString {
        val spannableString = SpannableString(message)
        val indexList = checkAllEmotion(message)
        val right = QMUIDisplayHelper.dp2px(context, 22)
        val bottom = QMUIDisplayHelper.dp2px(context, 22)
        for (index in indexList) {
            val drawable = ContextCompat.getDrawable(context!!, index.resourceId)
            if (drawable != null) {
                drawable.setBounds(0, 0, right, bottom)
                val imageSpan = ImageSpan(drawable)
                spannableString.setSpan(imageSpan, index.start, index.end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
        return spannableString
    }

    val regex = Regex("""^\[([\u4e00-\u9fa5a-zA-Z]+)\]$""")

    /**
     * 检查输入的内容是否是单个emoji，例如[微笑]
     */
    @JvmStatic
    fun checkAndGetEmotionIfSingle(text: String?): EmotionItem? {
        if (text.isNullOrBlank() || text.length < 3) {
            return null
        }
        if (regex.matches(text)) {
            val allEmotions = checkAllEmotionBeans(text)
            if (allEmotions.size == 1) {
                return allEmotions[0]
            }
        }
        return null
    }
}