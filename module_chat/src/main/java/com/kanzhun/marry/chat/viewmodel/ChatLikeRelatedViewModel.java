package com.kanzhun.marry.chat.viewmodel;

import android.app.Application;

import androidx.annotation.IntDef;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.api.callback.ChatCommonCallback;
import com.kanzhun.marry.chat.api.response.ChatCommonResponse;
import com.kanzhun.utils.L;
import com.kanzhun.utils.base.LList;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.disposables.Disposable;

public class ChatLikeRelatedViewModel extends FoundationViewModel {

    private static final int DEFAULT_PAGE_SIZE = 20;

    private int type;
    private String url;
    private String offsetId = "0";

    private MutableLiveData<ChatCommonResponse> likeRelatedLiveData = new MutableLiveData<>();
    private MutableLiveData<ChatCommonResponse> moreLikeRelatedLiveData = new MutableLiveData<>();

    private ObservableField<String> emptyDesc = new ObservableField<>();
    private ObservableField<Integer> likeMeCountField = new ObservableField<>();
    private int hasMore;
    private long nowTime;

    @IntDef({LikeRelatedType.TYPE_LIKE_RELATED_I_LIKE, LikeRelatedType.TYPE_LIKE_RELATED_LIKE_ME})
    @Retention(RetentionPolicy.SOURCE)
    public @interface LikeRelatedType {
        int TYPE_LIKE_RELATED_LIKE_ME = 1;
        int TYPE_LIKE_RELATED_I_LIKE = 2;
    }

    public ChatLikeRelatedViewModel(Application application) {
        super(application);
    }

    public void init(int likeMeCount, @LikeRelatedType int type) {
        this.type = type;
        initValue(likeMeCount);
    }

    public void loadList() {
        setShowProgressBar();
        HashMap<String, Object> messageKey = new HashMap<>();
        messageKey.put("offsetId", "0");
        messageKey.put("size", DEFAULT_PAGE_SIZE);
        HttpExecutor.executeJsonGet(url, messageKey, new ChatCommonCallback() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
            }

            @Override
            public void onSuccess(ChatCommonResponse data) {
                super.onSuccess(data);
                if (data != null) {
                    likeMeCountField.set(data.totalCount);
                    hasMore = data.hasMore;
                }
            }

            @Override
            public void handleInChildThread(ChatCommonResponse data) {
                offsetId = data != null ? data.offsetId : "0";
                if (data != null) {
                    nowTime = data.nowTime;
                    L.e("TAG", "nowTime = " + nowTime);
                }
                likeRelatedLiveData.postValue(data);
                if (data != null && !LList.isEmpty(data.chatList)) {
                    Iterator<ChatCommonResponse.ChatCommonBean> iterator = data.chatList.iterator();
                    dealDataAndContact(iterator);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                likeRelatedLiveData.postValue(null);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void loadMoreList() {
        HashMap<String, Object> messageKey = new HashMap<>();
        messageKey.put("offsetId", offsetId);
        messageKey.put("size", DEFAULT_PAGE_SIZE);
        HttpExecutor.executeJsonGet(url, messageKey, new ChatCommonCallback() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
            }

            @Override
            public void handleInChildThread(ChatCommonResponse data) {
                if (data != null) {
                    nowTime = data.nowTime;
                    offsetId = data.offsetId;
                    hasMore = data.hasMore;
                    if (!LList.isEmpty(data.chatList)) {
                        Iterator<ChatCommonResponse.ChatCommonBean> iterator = data.chatList.iterator();
                        dealDataAndContact(iterator);
                    }
                }
                moreLikeRelatedLiveData.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                moreLikeRelatedLiveData.postValue(null);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    private void initValue(int likeMeCount) {
//        switch (type) {
//            case LikeRelatedType.TYPE_LIKE_RELATED_LIKE_ME:
        url = URLConfig.URL_CHAT_LIKE_ME_LIST;
        emptyDesc.set(getApplication().getString(R.string.chat_all_like_reply));
        this.likeMeCountField.set(likeMeCount);
//                break;
//            default:
//                url = URLConfig.URL_CHAT_LIKE_LIST;
//                emptyDesc.set(getApplication().getString(R.string.chat_nothing_left));
//                break;
//        }
    }

    private void dealDataAndContact(Iterator<ChatCommonResponse.ChatCommonBean> iterator) {
        List<Contact> contacts = new ArrayList<>();
        while (iterator.hasNext()) {
            ChatCommonResponse.ChatCommonBean chatCommonBean = iterator.next();
            if (chatCommonBean == null || chatCommonBean.lastVisibleMessage.getMediaType() != MessageConstants.MSG_LIKE_CARD) {
                iterator.remove();
            } else {
                Contact contact = new Contact();
                contact.setNickName(chatCommonBean.nickName);
                contact.setAvatar(chatCommonBean.avatar);
                contact.setTinyAvatar(chatCommonBean.tinyAvatar);
                contact.setGender(chatCommonBean.gender);
                contact.setLiveVideo(chatCommonBean.liveVideo);
                contact.setUserId(chatCommonBean.chatId);
                contact.setStatus(chatCommonBean.status);
                contact.setProfileLocked(chatCommonBean.profileLocked);
                contacts.add(contact);
            }
        }

        if (!LList.isEmpty(contacts)) {
            ServiceManager.getInstance().getContactService().insertTemporaryContacts(contacts);
        }
    }

    public MutableLiveData<ChatCommonResponse> getLikeRelatedLiveData() {
        return likeRelatedLiveData;
    }

    public MutableLiveData<ChatCommonResponse> getMoreLikeRelatedLiveData() {
        return moreLikeRelatedLiveData;
    }

    public String getEmptyDesc() {
        return emptyDesc.get();
    }

    public ObservableField<Integer> getLikeMeCount() {
        return likeMeCountField;
    }

    public int getType() {
        return type;
    }

    public int getHasMore() {
        return hasMore;
    }

    public void viewLikeMe(String chatId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", chatId);
        HttpExecutor.requestSimplePost(URLConfig.URL_VIEW_LIKE_ME, params, null);
    }

    public long getNowTime() {
        if (nowTime <= 0) {
            return System.currentTimeMillis();
        }
        return nowTime;
    }
}