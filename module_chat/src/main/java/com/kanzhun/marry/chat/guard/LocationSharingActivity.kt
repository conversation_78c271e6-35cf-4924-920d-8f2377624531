package com.kanzhun.marry.chat.guard

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.MapsInitializer
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.maps.model.MyLocationStyle
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.ChatPageRouterKT.KEY_PEER_ID
import com.kanzhun.foundation.router.ChatPageRouterKT.KEY_RECORD_ID
import com.kanzhun.marry.chat.guard.viewmodel.LocationSharingViewModel
import com.kanzhun.marry.location.GPSTracker
import com.kanzhun.marry.location.GPSTracker.Companion.setLastLocation
import com.sankuai.waimai.router.annotation.RouterUri
import org.json.JSONObject

/**
 * 位置共享页面 - 实时显示双方位置和见面地点
 */
@RouterUri(path = [ChatPageRouterKT.LOCATION_SHARING_ACTIVITY])
class LocationSharingActivity : BaseComposeActivity() {

    companion object {
        private const val DEFAULT_LATITUDE = 39.906901
        private const val DEFAULT_LONGITUDE = 116.397972

        /**
         * 启动位置共享页面
         */
        fun start(
            context: Context,
            recordId: String = "",
            peerId: String = ""
        ) {
            val intent = Intent(context, LocationSharingActivity::class.java).apply {
                putExtra(KEY_RECORD_ID, recordId)
                putExtra(KEY_PEER_ID, peerId)
            }
            context.startActivity(intent)
        }
    }

    private var recordId: String? = null
    private var peerId: String? = null

    // Store the MapView reference
    private var mapView: MapView? = null
    private lateinit var gpsTracker: GPSTracker

    // Map markers
    private var myLocationMarker: Marker? = null
    private var peerLocationMarker: Marker? = null
    private var meetingLocationMarker: Marker? = null

    override fun enableSafeDrawingPadding() = false

    override fun onCreate(savedInstanceState: Bundle?) {
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        // 初始化高德地图隐私政策
        MapsInitializer.updatePrivacyAgree(this, true)
        MapsInitializer.updatePrivacyShow(this, true, true)

        // 从 Intent 中获取参数
        recordId = intent.getStringExtra(KEY_RECORD_ID)
        peerId = intent.getStringExtra(KEY_PEER_ID)

        gpsTracker = GPSTracker(this)

        val hasLocationPermission = gpsTracker.hasLocationPermission(this)
        if (!hasLocationPermission) {
            requestLocationPermission()
        }

        liveEventBusObserve(LivedataKeyCommon.EVENT_KEY_FRIEND_LOCATION) { extend: String? ->
            if (!extend.isNullOrBlank()) {
                try {
                    // DataSync新增类型：100620-好友位置，sync内容格式{"userId":"xxxx", "gps": "119.97372,31.76943","rotateAngle":30}，gps为经纬度英文逗号分割
                    val extendData = JSONObject(extend)
                    val userId = extendData.optString("userId")
                    val gps = extendData.optString("gps")
                    // 旋转角度（可选）
                    val rotateAngle = extendData.optInt("rotateAngle")

                    if (peerId == userId && !gps.isNullOrBlank()) {
                        val latLng = gps.split(",").mapNotNull { it.toDoubleOrNull() }.let {
                            if (it.size == 2) {
                                LatLng(it[1], it[0])
                            } else {
                                null
                            }
                        }

                       val viewModel: LocationSharingViewModel by viewModels<LocationSharingViewModel>()
                        latLng?.let {
                            viewModel.updatePeerLocation(it, rotateAngle)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        super.onCreate(savedInstanceState)
    }

    @Composable
    override fun OnSetContent() {
        LocationSharingScreen(
            recordId = recordId,
            peerId = peerId,
            onBackClick = { AppUtil.finishActivity(this) }
        )
    }

    @Composable
    private fun LocationSharingScreen(
        recordId: String?,
        peerId: String?,
        onBackClick: () -> Unit
    ) {
        val viewModel: LocationSharingViewModel = viewModel()

        // 收集ViewModel状态
        val meetingLocation by viewModel.meetingLocation.collectAsState()
        val myUserInfo by viewModel.myUserInfo.collectAsState()
        val peerUserInfo by viewModel.peerUserInfo.collectAsState()

        // 初始化数据
        LaunchedEffect(Unit) {
            recordId?.let { id ->
                if (id.isNotEmpty()) {
                    viewModel.initLocationSharing(recordId = id, peerId = peerId ?: "")
                }
            }
        }

        // 监听位置变化，更新地图标记
        LaunchedEffect(myUserInfo?.location) {
            myUserInfo?.location?.let { updateMyLocationMarker(it) }
        }

        LaunchedEffect(peerUserInfo?.location) {
            peerUserInfo?.location?.let { updatePeerLocationMarker(it) }
        }

        LaunchedEffect(meetingLocation) {
            meetingLocation?.let { updateMeetingLocationMarker(it) }
        }

        Box(modifier = Modifier.fillMaxSize()) {
            // 高德地图
            AndroidView(
                factory = { context ->
                    MapView(context).apply {
                        <EMAIL> = this
                        onCreate(Bundle())

                        // 设置地图属性
                        map?.uiSettings?.apply {
                            isZoomControlsEnabled = false
                            isCompassEnabled = false
                            isMyLocationButtonEnabled = false
                            isScaleControlsEnabled = false
                            isRotateGesturesEnabled = true
                            isTiltGesturesEnabled = false
                        }

                        // 启用定位
                        map?.isMyLocationEnabled = true
                        map?.myLocationStyle = MyLocationStyle().apply {
                            myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER)
                            interval(2000) // 2秒更新一次
                        }

                        // 设置地图类型
                        map?.mapType = AMap.MAP_TYPE_NORMAL

                        // 监听定位变化
                        map?.setOnMyLocationChangeListener { location ->
                            location?.let {
                                val latitude = it.latitude
                                val longitude = it.longitude
                                val latLng = LatLng(latitude, longitude)

                                if (isValidLocation(latitude, longitude)) {
                                    // 更新当前用户位置
                                    viewModel.updateMyLocation(latLng)
                                    
                                    // 保存位置到GPSTracker
                                    setLastLocation(Location(null).apply {
                                        this.latitude = latitude
                                        this.longitude = longitude
                                    })

                                    // 更新地图上的标记
                                    updateMyLocationMarker(latLng)
                                }
                            }
                        }

                        // 地图加载完成后的初始化
                        map?.setOnMapLoadedListener {
                            val hasLocationPermission = gpsTracker.hasLocationPermission(this@LocationSharingActivity)
                            if (!hasLocationPermission) {
                                // 没有定位权限时，显示默认位置（北京）
                                map.moveCamera(
                                    CameraUpdateFactory.newLatLngZoom(
                                        LatLng(DEFAULT_LATITUDE, DEFAULT_LONGITUDE),
                                        12f
                                    )
                                )
                            }
                        }
                    }
                },
                modifier = Modifier.fillMaxSize()
            )

            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .statusBarsPadding()
                    .padding(12.dp)
            ) {
                Icon(
                    painter = painterResource(id = com.kanzhun.common.R.drawable.common_ic_black_back),
                    contentDescription = "返回",
                    tint = Color.Black
                )
            }
        }

        // 处理地图生命周期
        DisposableEffect(Unit) {
            onDispose {
                mapView?.onDestroy()
            }
        }
    }

    /**
     * 更新当前用户位置标记
     */
    private fun updateMyLocationMarker(latLng: LatLng) {
        mapView?.map?.let { map ->
            // 移除旧标记
            myLocationMarker?.remove()

            // 添加新标记 - 蓝色表示当前用户
            myLocationMarker = map.addMarker(
                MarkerOptions()
                    .position(latLng)
                    .title("我的位置")
                    .snippet("当前位置")
                    .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE))
            )
        }
    }

    /**
     * 更新对方用户位置标记
     */
    private fun updatePeerLocationMarker(latLng: LatLng) {
        mapView?.map?.let { map ->
            // 移除旧标记
            peerLocationMarker?.remove()

            // 添加新标记 - 红色表示对方用户
            peerLocationMarker = map.addMarker(
                MarkerOptions()
                    .position(latLng)
                    .title("对方位置")
                    .snippet("实时位置")
                    .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED))
            )
        }
    }

    /**
     * 更新见面地点标记
     */
    private fun updateMeetingLocationMarker(meetingInfo: com.kanzhun.marry.chat.guard.viewmodel.MeetingLocationInfo) {
        mapView?.map?.let { map ->
            // 移除旧标记
            meetingLocationMarker?.remove()

            val latLng = LatLng(meetingInfo.latitude, meetingInfo.longitude)

            // 添加新标记 - 绿色表示见面地点
            meetingLocationMarker = map.addMarker(
                MarkerOptions()
                    .position(latLng)
                    .title(meetingInfo.name)
                    .snippet(meetingInfo.address)
                    .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN))
            )

            // 将地图中心移动到见面地点
            map.moveCamera(
                CameraUpdateFactory.newLatLngZoom(latLng, 15f)
            )
        }
    }

    /**
     * 验证位置坐标是否有效
     */
    private fun isValidLocation(latitude: Double, longitude: Double): Boolean {
        return latitude >= -90f && latitude <= 90f && longitude >= -180f && longitude <= 180f
    }

    /**
     * 请求位置权限
     */
    @SuppressLint("MissingPermission")
    private fun requestLocationPermission() {
        PermissionHelper
            .getSafetyGuardLocationHelper(this)
            .setPermissionCallback { granted, _ ->
                if (granted) {
                    // 权限获取成功后重新加载地图
                    reloadMapAfterPermissionGranted()
                }
            }
            .requestPermission()
    }

    /**
     * 权限获取后重新加载地图
     */
    private fun reloadMapAfterPermissionGranted() {
        mapView?.map?.let { map ->
            // 重新注册定位监听器
            map.setOnMyLocationChangeListener { location ->
                location?.let {
                    val latitude = it.latitude
                    val longitude = it.longitude
                    val latLng = LatLng(latitude, longitude)

                    if (isValidLocation(latitude, longitude)) {
                        setLastLocation(Location(null).apply {
                            this.latitude = latitude
                            this.longitude = longitude
                        })

                        updateMyLocationMarker(latLng)
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mapView?.onResume()
    }

    override fun onPause() {
        super.onPause()
        mapView?.onPause()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView?.onSaveInstanceState(outState)
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView?.onDestroy()
    }

    @Preview(showBackground = true)
    @Composable
    private fun LocationSharingScreenPreview() {
        LocationSharingScreen(
            recordId = "12345",
            peerId = "67890",
            onBackClick = {}
        )
    }
}
