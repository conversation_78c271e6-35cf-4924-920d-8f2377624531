package com.kanzhun.marry.chat.fragment.singlechat

import android.app.Dialog
import android.view.LayoutInflater
import android.view.Window
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.marry.chat.bindadapter.WelcomeMeetingPlanMode
import com.kanzhun.marry.chat.databinding.ChatMeetingPlanSuccessDialogBinding
import com.kanzhun.utils.ui.ActivityUtils


class MeetingPlanSuccessDialog(val activity: FragmentActivity) {
    fun show() {
        if (ActivityUtils.isValid(activity)) {
            val dialog = Dialog(activity, android.R.style.Theme_Light)

            val binding = ChatMeetingPlanSuccessDialogBinding.inflate(LayoutInflater.from(activity))
            binding.apply {
                root.setViewTreeLifecycleOwner(activity)
                root.setViewTreeSavedStateRegistryOwner(activity)
                root.postDelayed({
                    if (ActivityUtils.isValid(activity)) {
                        dialog.dismiss()
                    }
                }, 3000)

                composeView.onSetWindowContent {
                    WelcomeMeetingPlanMode()
                }
            }

            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setContentView(binding.root)
            dialog.show()
        }
    }
}