@file:Suppress("DEPRECATION")

package com.kanzhun.marry.chat.bindadapter

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import androidx.compose.ui.platform.ComposeView
import androidx.databinding.DataBindingUtil
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.decoration.CommonHorizontalDecoration
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toChatTime
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.click
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.common.views.span.ExpandColorTextView
import com.kanzhun.common.views.span.internal.FormatRegulation
import com.kanzhun.common.views.span.internal.LinkType
import com.kanzhun.common.views.span.internal.Regulation
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.coevent.CoEventTip
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.ChatUserInfo
import com.kanzhun.foundation.model.message.MatchInfo
import com.kanzhun.foundation.model.message.MessageForActivityMedia
import com.kanzhun.foundation.model.message.MessageForUserCard
import com.kanzhun.foundation.router.MePageRouter.jumpToInfoPreviewActivity
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.adapter.ChatUserCardImageAdapter
import com.kanzhun.marry.chat.adapter.ChatUserCardTagAdapter
import com.kanzhun.marry.chat.databinding.ChatItemMessageOfficialActivityBinding
import com.kanzhun.marry.chat.databinding.ChatItemMessageUserInfoCardBinding
import com.kanzhun.marry.chat.databinding.ChatItemNoticeAvatarsBinding
import com.kanzhun.marry.chat.point.ChatPointReporter.Companion.reportChatUserDetailClick
import com.kanzhun.marry.chat.viewmodel.ChatViewModel
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LList

@SuppressLint("SetTextI18n")
class ChatBindingAdapterHelper {
    companion object {
        fun setChatUserInfoCard(view: View, message: MessageForUserCard?) {
            if (message != null) {
                val chatUserCard = message.chatUserCard
                if (chatUserCard != null) {
                    view.visibility = View.VISIBLE
                    val binding =
                        view.getTag(R.id.chat_binding) as? ChatItemMessageUserInfoCardBinding
                            ?: return

                    binding.run {
                        chatUserCard.setBaeInfo(tvBasicInfo)
                        // 标签
                        val allTags = chatUserCard.getAllTags()
                        if (LList.isEmpty(allTags)) {
                            rvTags.gone()
                        } else {
                            rvTags.visible()
                            var adapter = rvTags.adapter
                            if (adapter == null) {
                                rvTags.addItemDecoration(CommonHorizontalDecoration(10f, 8f))
                                adapter = ChatUserCardTagAdapter {
                                    jumpToInfoPreviewActivity(
                                        rvTags.context,
                                        message.userId,
                                        "",
                                        PageSource.CHAT,
                                        securityId = chatUserCard.securityId
                                    )
                                }
                                rvTags.adapter = adapter
                            }
                            (adapter as ChatUserCardTagAdapter).setNewInstance(allTags.toMutableList())
                        }

                        // 图片
                        val allPics = chatUserCard.getAllPics()
                        if (allPics.isEmpty()) {
                            rvPic.gone()
                        } else {
                            rvPic.visible()
                            var picAdapter = rvPic.adapter
                            if (picAdapter == null) {
                                rvPic.addItemDecoration(CommonHorizontalDecoration(10f, 8f))
                                picAdapter = ChatUserCardImageAdapter {
                                    jumpToInfoPreviewActivity(
                                        rvPic.context,
                                        message.userId,
                                        "",
                                        PageSource.CHAT,
                                        securityId = chatUserCard.securityId
                                    )
                                }
                                rvPic.adapter = picAdapter
                            }
                            (picAdapter as ChatUserCardImageAdapter).setNewInstance(allPics.toMutableList())
                        }

                        view.click { view1: View ->
                            reportChatUserDetailClick("对方资料卡片", message.userId)
                            jumpToInfoPreviewActivity(
                                view1.context,
                                message.userId,
                                "",
                                PageSource.CHAT,
                                securityId = chatUserCard.securityId
                            )
                        }
                    }
                } else {
                    view.visibility = View.GONE
                }
            } else {
                view.visibility = View.GONE
            }
        }

        @JvmStatic
        fun bindSameActivityView(
            composeViewCoeventTip: ComposeView,
            chatUserInfo: ChatUserInfo? = null,
            viewModel: ChatViewModel
        ) {
            val showText = chatUserInfo?.sameActivity?.showText ?: ""
            if (showText.isNotEmpty() && !viewModel.isMeetingPlan) {
                composeViewCoeventTip.apply {
                    visible()

                    onSetWindowContent {
                        CoEventTip(showText = showText)
                    }
                }
            } else {
                composeViewCoeventTip.gone()
            }
        }

        @JvmStatic
        fun setChatStickyInfo(view: View, message: MessageForUserCard?) {
            view.visibility = View.GONE
            if (message != null) {
                val chatUserCard = message.chatUserCard
                if (chatUserCard != null) {
                    val matchInfo = chatUserCard.matchInfo
                    if (matchInfo != null) {
                        val content = matchInfo.content
                        if (!TextUtils.isEmpty(content)) {
                            view.visibility = View.VISIBLE
                            val tv = view.findViewById<ExpandColorTextView>(R.id.tvSameTag)
                            tv.bind(matchInfo)
                            tv.setContent(content, matchInfo.toFormatRegulation())
                        }
                    }
                }
                view.click { }
            }
        }

        private fun MatchInfo.toFormatRegulation(): FormatRegulation {
            val formatRegulation = FormatRegulation()
            formatRegulation.formatContent = this.content
            val list = mutableListOf<Regulation>()
            this.highlight?.forEach {
                list.add(Regulation(it.start, it.end, "", LinkType.CUSTOM))
            }
            formatRegulation.regulations = list
            return formatRegulation
        }

        fun setChatActivityInfo(view: View, message: MessageForActivityMedia) {
            val binding =
                view.getTag(R.id.chat_binding) as? ChatItemMessageOfficialActivityBinding ?: return

            binding.run {
                idUser.textOrGone(message.ogActivityMedia?.name + " " + message.time.toChatTime())
                idTitle.textOrGone(message.ogActivityMedia?.title)
                if (message.ogActivityMedia?.img.isNullOrEmpty()) {
                    idImageView.gone()
                } else {
                    idImageView.visible()
                    idImageView.load(message.ogActivityMedia?.img)
                }
                idContent.textOrGone(message.ogActivityMedia?.content)
                if (TextUtils.isEmpty(message.ogActivityMedia?.linkTitle)) {
                    idBtn.gone()
                    idLine.gone()
                    idRightArrow.gone()
                } else {
                    idBtn.visible()
                    idLine.visible()
                    idRightArrow.visible()
                    idBtn.text = message.ogActivityMedia?.linkTitle
                }
                if (message.ogActivityMedia?.status == 1) {
                    idIcon.gone()
                } else {
                    idIcon.visible()
                }
            }

            view.onClick {
                reportPoint("official-activities-card-click") {
                    actionp2 = message.ogActivityMedia?.adminPushId.toString()
                    actionp3 = message.ogActivityMedia?.pushType.toString()
                }
                if (message.ogActivityMedia?.status == 0) {
                    message.ogActivityMedia?.status = 1
                    binding.idIcon.gone()
                    ServiceManager.getInstance().getMessageService()
                        .insertMessages(listOf<ChatMessage>(message))
                    val params = mutableMapOf<String, Any?>()
                    params["msgId"] = message.mid
                    HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_READ_MESSAGE, params, null)
                }
                val endTime = message.ogActivityMedia?.endTime ?: 0
                if (System.currentTimeMillis() > endTime && endTime != 0L) {
                    var expireTip = message.ogActivityMedia?.expireTip ?: ""
                    if (TextUtils.isEmpty(expireTip)) {
                        expireTip = "本活动已结束，请关注平台内其它活动"
                    }
                    T.ss(expireTip)
                    return@onClick
                }
                ProtocolHelper.parseProtocol(message.ogActivityMedia?.linkUrl)
            }
        }

        fun setNoticeAvatars(view: View, conversation: Conversation) {
            val draftPhotoList = conversation.draftPhotoList
            val bind = DataBindingUtil.bind<ChatItemNoticeAvatarsBinding>(view) ?: return
            bind.run {
                when {
                    draftPhotoList.isEmpty() -> {
                        clNoticeAvatars.gone()
                    }

                    draftPhotoList.size == 1 -> {
                        clNoticeAvatars.visible()
                        ivAvatar1.load(draftPhotoList[0])
                        ivAvatar2.gone()
                        ivAvatar3.gone()
                        rbMore.gone()
                    }

                    draftPhotoList.size == 2 -> {
                        clNoticeAvatars.visible()
                        ivAvatar1.load(draftPhotoList[0])
                        ivAvatar2.load(draftPhotoList[1])
                        ivAvatar3.gone()
                        rbMore.gone()
                    }

                    draftPhotoList.size == 3 -> {
                        clNoticeAvatars.visible()
                        ivAvatar1.load(draftPhotoList[0])
                        ivAvatar2.load(draftPhotoList[1])
                        ivAvatar3.load(draftPhotoList[2])
                        rbMore.gone()

                    }

                    else -> {
                        clNoticeAvatars.visible()
                        ivAvatar1.load(draftPhotoList[0])
                        ivAvatar2.load(draftPhotoList[1])
                        ivAvatar3.load(draftPhotoList[2])
                        rbMore.gone()
                        rbMore.text = "+" + (draftPhotoList.size - 3)
                    }
                }
            }
        }
    }
}