package com.kanzhun.marry.chat.type

enum class LikeListItemType(val value: Int, val resourceName: String) {
    EMPTY(-1, ""),
//    USER(0),//用户
//    AVATAR(10),//形象照
//    INTRO(30),//关于我
//    FAMILY_DESC(31),//家庭介绍
//    IDEAL_PARTNER_DESC(32),//我的理想型模块
//    STORY_PHOTO(50),//我的生活图片
//    AUDIO_ANSWER(61),//我的声音模块
//    TEXT_ANSWER(62),//文字问答
//    AB_FACE(70),//AB面
//    INTEREST(200),//我的兴趣爱好
//    SINGLE_REASON(210),//单身原因
//    INTRO_PHOTO(321),//自我介绍图片
//    INTEREST_PHOTO(322),//兴趣爱好图片
//    FAMILY_DESC_PHOTO(323),//家庭情况图片

    USER(0, "用户"),  //用户
    AVATAR(10, "形象照"),  //形象照
    INTRO(30, "关于我"),  //关于我
    FAMILY_DESC(31, "家庭介绍"),  //家庭介绍
    IDEAL_PARTNER_DESC(32, "我的理想型模块"),  //我的理想型模块
    STORY_PHOTO(50, "我的生活照片"),  //我的生活照片
    AUDIO_ANSWER(61, "我的声音模块"),  //我的声音模块
    TEXT_ANSWER(62, "文字问答"),  //文字问答
    AB_FACE(70, "AB面"),  //AB面
    INTEREST(200, "我的兴趣爱好"),  //我的兴趣爱好
    SINGLE_REASON(210, "单身原因"),  //单身原因
    INTRO_PHOTO(321, "自我介绍照片"),  //自我介绍照片
    INTEREST_PHOTO(322, "兴趣爱好照片"),  //兴趣爱好照片
    FAMILY_DESC_PHOTO(323, "家庭情况照片"), //家庭情况照片
    ACTIVITY_PHOTO(505,"活动个人照片"), //活动个人照片
    ACTIVITY_PHOTO_2(506,"活动个人照片"), //活动个人照片


}