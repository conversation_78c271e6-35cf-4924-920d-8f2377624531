package com.kanzhun.marry.chat.emotion

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

class EmotionViewPager : ViewPager {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    //是否消费了事件
    private var canConsumeEvent = false

    interface OnEmotionTouchCallBack {
        fun showPreviewGif(touchX: Float, touchY: Float)

        fun removePreviewGif()
    }

    private var touchCallBack: OnEmotionTouchCallBack? = null

    fun setTouchCallBack(touchCallBack: OnEmotionTouchCallBack?) {
        this.touchCallBack = touchCallBack
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        detector.onTouchEvent(ev)
        if (canConsumeEvent) {
            onTouchEmotion(ev)
            return true
        } else {
            return super.dispatchTouchEvent(ev)
        }
    }

    private fun onTouchEmotion(ev: MotionEvent) {
        val action = ev.action
        if (action == MotionEvent.ACTION_MOVE) { //如果是有效的滑动&&绘制预览gif
            if (canConsumeEvent) {
                val touchX = ev.x
                val touchY = ev.y
                touchCallBack?.showPreviewGif(touchX, touchY)
            }
        } else {
            canConsumeEvent = false
            //移除老的预览表情
            touchCallBack?.removePreviewGif()
        }
    }

    private val detector =
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onLongPress(e: MotionEvent) {
                super.onLongPress(e)
                canConsumeEvent = true
                touchCallBack?.showPreviewGif(e.x, e.y)
            }
        })
}