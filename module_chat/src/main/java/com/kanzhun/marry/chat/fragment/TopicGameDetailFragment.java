package com.kanzhun.marry.chat.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;

import com.chad.library.BR;
import com.chad.library.adapter.base.BaseBinderAdapter;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.bean.ChatTopicGameQuestion;
import com.kanzhun.foundation.api.bean.TopicGameOptionBean;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.api.bean.TopicGameQuestionBean;
import com.kanzhun.marry.chat.callback.TopicGameDetailCallback;
import com.kanzhun.marry.chat.databinding.ChatFragmentTopicGameDetailBinding;
import com.kanzhun.marry.chat.databinding.ChatItemTopicGameOption2Binding;
import com.kanzhun.marry.chat.viewmodel.TopicGameActivityViewModel;
import com.kanzhun.marry.chat.viewmodel.TopicGameDetailViewModel;
import com.kanzhun.marry.chat.views.TopicGameVoiceView;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/12
 */
public class TopicGameDetailFragment extends FoundationVMFragment<ChatFragmentTopicGameDetailBinding, TopicGameDetailViewModel> implements TopicGameDetailCallback {

    private BaseBinderAdapter adapter;
    private long lastVoiceDown = 0;

    public static TopicGameDetailFragment newInstance(String toId, TopicGameQuestionBean questionBean, String startBgColor, String checkColor) {
        TopicGameDetailFragment fragment = new TopicGameDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA, questionBean);
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, toId);
        bundle.putString(BundleConstants.BUNDLE_DATA_2, startBgColor);
        bundle.putString(BundleConstants.BUNDLE_DATA_3, checkColor);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static TopicGameDetailFragment newInstanceForDirect(String toId, String questionId, String startBgColor, String checkColor) {
        TopicGameDetailFragment fragment = new TopicGameDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, true);
        bundle.putSerializable(BundleConstants.BUNDLE_DATA, questionId);
        bundle.putString(BundleConstants.BUNDLE_DATA_STRING, toId);
        bundle.putString(BundleConstants.BUNDLE_DATA_2, startBgColor);
        bundle.putString(BundleConstants.BUNDLE_DATA_3, checkColor);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static TopicGameDetailFragment newInstanceForReply(ChatTopicGameQuestion gameQuestion, String msgId) {
        TopicGameDetailFragment fragment = new TopicGameDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_MSG_ID, msgId);
        bundle.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE, gameQuestion);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_fragment_topic_game_detail;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        initView();
        initData();
    }

    private void initData() {
        getViewModel().getOptionLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                adapter.setList(getViewModel().getOptionList());
                getDataBinding().tvTitle.setText(getViewModel().getQuestion());
            }
        });

        getViewModel().getSuccessLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                activity.onBackPressed();
            }
        });

        getViewModel().getVoicePlayingLiveData().observe(this, new Observer<Float>() {
            @Override
            public void onChanged(Float aFloat) {
                getDataBinding().recordPlayView.setReadPercent(aFloat);
            }
        });
    }

    private void initView() {
        Bundle arguments = getArguments();
        if (arguments == null) {
            return;
        }

        String msgId = arguments.getString(BundleConstants.BUNDLE_MSG_ID);
        boolean isDirect = arguments.getBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN);
        getViewModel().setDirectSend(isDirect);
        if (isDirect) {
            String toId = arguments.getString(BundleConstants.BUNDLE_DATA_STRING);
            getViewModel().setToId(toId);
            String questionId = arguments.getString(BundleConstants.BUNDLE_DATA);
            getViewModel().setQuestionId(questionId);
            String startBgColor = arguments.getString(BundleConstants.BUNDLE_DATA_2);
            String checkColor = arguments.getString(BundleConstants.BUNDLE_DATA_3);
            getViewModel().setStartBgColor(startBgColor);
            getViewModel().setCheckColor(checkColor);
            getDataBinding().ivClose.setImageResource(R.drawable.common_black_close);
        } else {
            if (TextUtils.isEmpty(msgId)) {
                TopicGameQuestionBean questionBean = (TopicGameQuestionBean) arguments.getSerializable(BundleConstants.BUNDLE_DATA);
                String toId = arguments.getString(BundleConstants.BUNDLE_DATA_STRING);
                getViewModel().setToId(toId);
                getViewModel().setQuestionBean(questionBean);
                String startBgColor = arguments.getString(BundleConstants.BUNDLE_DATA_2);
                String checkColor = arguments.getString(BundleConstants.BUNDLE_DATA_3);
                getViewModel().setStartBgColor(startBgColor);
                getViewModel().setCheckColor(checkColor);
            } else {
                getViewModel().setReply(true);
                getViewModel().setMsgId(msgId);
                ChatTopicGameQuestion gameQuestion = (ChatTopicGameQuestion) arguments.getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
                getViewModel().setGameQuestion(gameQuestion);

                if (gameQuestion != null) {
                    getViewModel().setStartBgColor(gameQuestion.bgColor);
                    getViewModel().setCheckColor(gameQuestion.checkColor);
                    getDataBinding().tvTitle.setText(gameQuestion.title);
                    getViewModel().setOptionList(gameQuestion.optionList);
                }

            }
        }


        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(TopicGameOptionBean.class, new BaseDataBindingItemBinder<TopicGameOptionBean, ChatItemTopicGameOption2Binding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.chat_item_topic_game_option2;
            }

            @Override
            protected void bind(BinderDataBindingHolder<ChatItemTopicGameOption2Binding> holder, ChatItemTopicGameOption2Binding binding, TopicGameOptionBean item) {
                binding.setItem(item);
                boolean selected = TextUtils.equals(item.id, getViewModel().getSelOptionId());
                if (!selected) {
                    binding.tvContent.setTextColor(ResourcesCompat.getColor(activity.getResources(), R.color.common_color_191919, null));

                    binding.flContent.setBackgroundResource(R.drawable.chat_bg_gray_btn);
                } else {
                    binding.tvContent.setTextColor(ResourcesCompat.getColor(activity.getResources(), R.color.common_color_FFFFFF, null));

                    binding.flContent.setBackgroundResource(R.drawable.chat_bg_black_btn);
                }
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<ChatItemTopicGameOption2Binding> holder, @NonNull View view, TopicGameOptionBean data, int position) {
                super.onClick(holder, view, data, position);
                getViewModel().setSelOptionId(data.id);
                getViewModel().setSelOptionContent(data.content);
                getViewModel().enableSendObservable.set(true);
                adapter.notifyDataSetChanged();
            }
        });
        getDataBinding().recyclerView.setLayoutManager(new GridLayoutManager(activity, 2));
        getDataBinding().recyclerView.setAdapter(adapter);
        if (!getViewModel().isReply() || getViewModel().isDirectSend()) {
            getViewModel().requestContent();
        } else {
            adapter.setList(getViewModel().getOptionList());
        }
        getDataBinding().etInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getViewModel().setErrorObservable("");
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        getDataBinding().etInput.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getDataBinding().btnNext.getVisibility() == View.VISIBLE) {
                    getDataBinding().btnNext.setVisibility(View.GONE);
                }
            }
        });
        QMUIKeyboardHelper.setVisibilityEventListener(activity, new QMUIKeyboardHelper.KeyboardVisibilityEventListener() {
            @Override
            public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
                if (TopicGameDetailFragment.this.isResumed()) {
                    if (isOpen) {
                        if (getDataBinding().btnNext.getVisibility() == View.VISIBLE) {
                            getDataBinding().btnNext.setVisibility(View.GONE);
                        }
                        smoothToBottom();
                    } else {
                        getDataBinding().btnNext.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                getDataBinding().btnNext.setVisibility(View.VISIBLE);
                            }
                        }, 50);
                    }
                }

                return false;
            }
        });
        initChatInputVoiceView();

        getDataBinding().flContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new ViewModelProvider(requireActivity()).get(TopicGameActivityViewModel.class).getDismissLiveData().setValue(true);
            }
        });
    }

    protected void smoothToBottom() {
        getDataBinding().scrollView.postDelayed(new Runnable() {
            @Override
            public void run() {
                getDataBinding().scrollView.fullScroll(View.FOCUS_DOWN);
                getDataBinding().etInput.setFocusable(true);
                getDataBinding().etInput.setFocusableInTouchMode(true);
                getDataBinding().etInput.requestFocus();
            }
        }, 100);
    }

    @Override
    public void clickSend() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (getViewModel().userProfileLocked()) {
            return;
        }
        getViewModel().questionSend();
    }

    @Override
    public void clickClose() {
        if (QMUIKeyboardHelper.isKeyboardVisible(activity)) {
            QMUIKeyboardHelper.hideKeyboard(getDataBinding().etInput);
        } else {
            if (getViewModel().isDirectSend()) {
                activity.onBackPressed();
            } else if (!getViewModel().isReply()) {
                getParentFragmentManager().popBackStack();
            } else {
                activity.onBackPressed();
            }
        }
    }

    @Override
    public void clickClearInput() {
        getViewModel().inputObservable.set("");
    }

    @Override
    public void clickSwitchVoiceInput() {
        if (getViewModel().getType() == 1) {
            //切换到语音输入
            QMUIKeyboardHelper.hideKeyboard(getDataBinding().etInput);
            getViewModel().setType(2);
            getDataBinding().ivVoice.setImageResource(R.drawable.chat_selector_topic_game_input2);
            getDataBinding().flVoiceInput.setVisibility(View.VISIBLE);
            getDataBinding().flInput.setVisibility(View.GONE);
        } else {
            //切换到文本输入
            getViewModel().setType(1);
            getDataBinding().ivVoice.setImageResource(R.drawable.chat_selector_topic_game_voice_new);
            getDataBinding().flInput.setVisibility(View.VISIBLE);
            getDataBinding().flVoiceInput.setVisibility(View.GONE);
        }
    }

    @Override
    public void clickClearVoice() {
        getViewModel().onPause();
        getViewModel().release();
        getDataBinding().tvVoiceRecord.setVisibility(View.VISIBLE);
        getDataBinding().flVoicePlay.setVisibility(View.GONE);
        getViewModel().setVoicePath(null);
        getViewModel().setReadPercent(0.0f);
        getViewModel().setVoiceStatus(MessageForAudio.STATUS_VOICE_START);
    }

    @Override
    public void startVoice() {
        getViewModel().voicePlay();
    }

    /**
     * 初始化输入语音相关的view
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initChatInputVoiceView() {
        getDataBinding().voiceView.setActivity((FragmentActivity) activity);
        getDataBinding().ivVoice.post(new Runnable() {
            @Override
            public void run() {
                int[] pos = new int[2];
                getDataBinding().ivVoice.getLocationOnScreen(pos);
                int top = pos[1];
                getDataBinding().voiceView.setEnableHeight(QMUIDisplayHelper.getScreenHeight(activity) - top);
            }
        });
        getDataBinding().voiceView.setOnRecordCallBack(new TopicGameVoiceView.OnRecordCallBack() {
            @Override
            public void onRecordSuccessListener(String path, long totalTimeMillSecond, int[] waveArray) {
                //发送语音消息
                getViewModel().setVoicePath(path);
                getViewModel().setVoiceTime(totalTimeMillSecond);
                getViewModel().setWaveArray(waveArray);

                setVoiceWidth();
                getDataBinding().tvTime.setText(String.valueOf(totalTimeMillSecond / 1000) + "s");
                getDataBinding().tvVoiceRecord.setVisibility(View.GONE);
                getDataBinding().flVoicePlay.setVisibility(View.VISIBLE);


            }

            @Override
            public void onRecordStart() {
                getDataBinding().flVoice.setVisibility(View.VISIBLE);
            }

            @Override
            public void onRecordEnd() {
                getDataBinding().flVoice.setVisibility(View.GONE);
            }

            @Override
            public void onStateChanged(int status) {
                if (status == 0) {
                    getDataBinding().btnNext.setAlpha(0.1f);
                    getDataBinding().tvVoiceRecord.setBackgroundResource(R.drawable.chat_bg_topic_game_send_voice_sel);
                    getDataBinding().tvVoiceRecord.setText(R.string.chat_topic_game_record_release_to_send);
                } else if (status == 1) {
                    getDataBinding().tvVoiceRecord.setText(R.string.chat_record_release_to_cancel);
                } else if (status == 2) {
                    getDataBinding().btnNext.setAlpha(1f);
                    getDataBinding().tvVoiceRecord.setBackgroundResource(R.drawable.chat_bg_topic_game_send_voice);
                    getDataBinding().tvVoiceRecord.setText(R.string.chat_record_voice);
                }
            }
        });
        getDataBinding().tvVoiceRecord.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        long current = System.currentTimeMillis();
                        if (current - lastVoiceDown < 1000) {
                            return false;
                        }
                        lastVoiceDown = current;
                        getDataBinding().voiceView.responseTouchEvent(event);
                        break;
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        getDataBinding().voiceView.responseTouchEvent(event);
                        break;
                    default:
                        break;
                }
                return true;
            }
        });
    }

    public void setVoiceWidth() {
        ViewGroup.LayoutParams layoutParams = getDataBinding().llVoice.getLayoutParams();
        int minWidth = QMUIDisplayHelper.dp2px(activity, 100);
        int maxWidth = (int) (QMUIDisplayHelper.getScreenWidth(activity) - QMUIDisplayHelper.dp2px(activity, 112));
        int length = getViewModel().getWaveArray().length;
        int width = (int) (((float) (length - 6) / 29) * (maxWidth - minWidth) + minWidth);
        if (width > maxWidth) {
            width = maxWidth;
        }
        layoutParams.width = width;
        getDataBinding().llVoice.setLayoutParams(layoutParams);

        getDataBinding().recordPlayView.setLumpCount(getViewModel().getWaveArray().length);
        getDataBinding().recordPlayView.setWaveData(getViewModel().getWaveArray());
        getDataBinding().recordPlayView.setReadPercent(getViewModel().getReadPercent());
    }

    @Override
    public void onPause() {
        super.onPause();
        getViewModel().onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getViewModel().release();
    }
}
