package com.kanzhun.marry.chat.guard

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amap.api.maps.MapsInitializer
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.base.compose.ui.O2Toolbar
import com.kanzhun.common.photo.zoomable.previewer.PreviewerState
import com.kanzhun.common.photo.zoomable.previewer.rememberPreviewerState
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.LDate
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.ChatPageRouterKT.KEY_RECORD_ID
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.ChatApi
import com.kanzhun.marry.chat.api.model.AddressModel
import com.kanzhun.marry.chat.bindadapter.showMeetingConflictDialog
import com.kanzhun.marry.chat.guard.SelectLocationActivity.Companion.EXTRA_PEER_ID
import com.kanzhun.marry.chat.guard.SelectLocationActivity.Companion.EXTRA_SELECTED_ADDRESS
import com.kanzhun.marry.chat.guard.SelectLocationActivity.Companion.EXTRA_SELECTED_ID
import com.kanzhun.marry.chat.guard.SelectLocationActivity.Companion.EXTRA_SELECTED_LATITUDE
import com.kanzhun.marry.chat.guard.SelectLocationActivity.Companion.EXTRA_SELECTED_LONGITUDE
import com.kanzhun.marry.chat.guard.ui.Address
import com.kanzhun.marry.chat.guard.ui.AddressImage
import com.kanzhun.marry.chat.guard.ui.PreviewImage
import com.kanzhun.marry.chat.guard.ui.buildAddressImageKey
import com.kanzhun.marry.chat.guard.viewmodel.MakeMeetingPlanViewModel
import com.kanzhun.marry.chat.model.MeetingConflictResponse
import com.ozcanalasalvar.datepicker.compose.datepicker.RestrictedWheelDateTimePicker
import com.sankuai.waimai.router.annotation.RouterUri
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.launch

@RouterUri(path = [ChatPageRouterKT.MAKE_MEETING_PLAN_ACTIVITY])
class MakeMeetingPlanActivity : BaseComposeActivity() {
    override fun enableSafeDrawingPadding() = false

    // Record ID from intent
    private var recordId: String? = null

    // ViewModel
    private val viewModel: MakeMeetingPlanViewModel by viewModels()

    // Activity result launcher for location selection
    private val startActivityForResult = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val locationResult = SelectLocationActivity.getLocationResult(data)
            locationResult?.let { location ->
                // Update the location in the ViewModel
                viewModel.updateSelectedLocation(
                    latitude = location.latitude,
                    longitude = location.longitude,
                    addressName = location.addressName,
                    address = location.address,
                    province = location.province,
                    city = location.city,
                    district = location.district,
                    isRecommendedAddress = location.isRecommendedAddress,
                    id = location.id
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        MapsInitializer.updatePrivacyAgree(this, true)
        MapsInitializer.updatePrivacyShow(this, true, true)

        super.onCreate(savedInstanceState)

        // Get record ID from intent
        recordId = intent.getStringExtra(KEY_RECORD_ID)
    }

    @Composable
    override fun OnSetContent() {
        Content(
            recordId = recordId,
            viewModel = viewModel,
            onBackClick = { AppUtil.finishActivity(this) }
        )
    }

    @Composable
    private fun Content(
        modifier: Modifier = Modifier,
        recordId: String? = null,
        viewModel: MakeMeetingPlanViewModel,
        onBackClick: () -> Unit = {}
    ) {
        // Fetch meeting plan setting detail when the composable is first created
        androidx.compose.runtime.LaunchedEffect(Unit) {
            recordId?.let { id ->
                if (id.isNotEmpty()) {
                    viewModel.getMeetingPlanSettingDetail(id)
                }
            }
        }

        // Observe the setting detail state
        val settingDetail by viewModel.settingDetail.collectAsState(null)
        val isLoading by viewModel.isLoading.collectAsState(null)

        Box(
            modifier = modifier
                .fillMaxSize()
                .background(color = Color(0xFFFFFFFF))
        ) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(375f / 350)
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(
                                Color(0xFFB4F0FF),
                                Color(0xFFFFFFFF)
                            )
                        )
                    )
            )

            val recommendPics = settingDetail?.recommendPics ?: emptyList()
            val scope = rememberCoroutineScope()
            val addressKey = MakeMeetingPlanActivity::class.java
            val previewerState: PreviewerState = rememberPreviewerState(
                key1 = settingDetail,
                pageCount = { recommendPics.size },
                getKey = {
                    buildAddressImageKey(
                        index = it,
                        url = recommendPics.getOrNull(it).toString(),
                        key1 = addressKey
                    )
                }
            )
            if (previewerState.canClose || previewerState.animating) {
                BackHandler {
                    if (previewerState.canClose) {
                        scope.launch {
                            previewerState.exitTransform()
                        }
                    }
                }
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .statusBarsPadding()
                    .padding(bottom = 86.dp)
            ) {
                O2Toolbar(onBackClick = onBackClick)

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .padding(top = 20.dp)
                            .padding(horizontal = 20.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "制定见面计划",
                            style = TextStyle(
                                fontSize = 28.sp,
                                fontFamily = boldFontFamily(),
                                fontWeight = FontWeight(700),
                                color = Color(0xFF191919),
                            )
                        )

                        Spacer(
                            modifier = Modifier.weight(1f)
                        )

                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_make_meeting_plan_label),
                            contentDescription = "image description",
                            modifier = Modifier.size(width = 134.dp, height = 70.dp)
                        )
                    }

                    Spacer(
                        modifier = Modifier
                            .height(16.dp)
                    )

                    Text(
                        text = "见面时间",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF5E5E5E),
                        ),
                        modifier = Modifier
                            .padding(horizontal = 20.dp)
                    )

                    Spacer(
                        modifier = Modifier.height(8.dp)
                    )

                    Row(
                        modifier = Modifier,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(modifier = Modifier.weight(1f)) {
                            /*
                                时间选择器，支持选择年月日时分，分钟位置只展示0分及30分
                                首次进入时默认定位在当月当日
                                不可选择过去时间，时间选择器从现在时间开始截断
                             */
                            RestrictedWheelDateTimePicker(
                                offset = 2,
                                initialTimeMillis = settingDetail?.meetTime,
                                onDateChanged = { day, month, year, _ ->
                                    TLog.debug(TAG, "$day / $month / $year")
                                    viewModel.updateSelectedDate(day, month, year)
                                },
                                onTimeChanged = { hour, minute, _ ->
                                    TLog.debug(TAG, "$hour : $minute")
                                    viewModel.updateSelectedTime(hour, minute)
                                }
                            )
                        }
                    }

                    Spacer(
                        modifier = Modifier
                            .height(16.dp)
                    )
                }

                Spacer(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(10.dp)
                        .background(color = Color(0xFFF5F5F6))
                )

                Spacer(modifier = Modifier.height(16.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp)
                    ) {
                        Text(
                            text = "见面地址",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF5E5E5E),
                            )
                        )

                        Spacer(modifier = Modifier.weight(1f))

                        Row(
                            modifier = Modifier.clickable {
                                // 获取当前选中的地址信息
                                val selectedLatitude = viewModel.selectedLatitude.value
                                val selectedLongitude = viewModel.selectedLongitude.value
                                val selectedAddress = viewModel.selectedAddress.value
                                val selectedAddressId = viewModel.selectedAddressId.value

                                // 创建 Intent 并添加已选地址的信息
                                val intent = Intent(
                                    this@MakeMeetingPlanActivity,
                                    SelectLocationActivity::class.java
                                )

                                // 如果有已选地址，则添加到 Intent 中
                                if (selectedLatitude != null && selectedLongitude != null && selectedAddress != null) {
                                    intent.putExtra(EXTRA_SELECTED_LATITUDE, selectedLatitude)
                                    intent.putExtra(EXTRA_SELECTED_LONGITUDE, selectedLongitude)
                                    intent.putExtra(EXTRA_SELECTED_ADDRESS, selectedAddress)
                                    intent.putExtra(EXTRA_SELECTED_ID, selectedAddressId)
                                    intent.putExtra(EXTRA_PEER_ID, settingDetail?.peerId ?: "")
                                }

                                // 使用 startActivityForResult 来启动活动并接收返回的结果
                                startActivityForResult.launch(intent)
                            },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "更换",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF005EFF),
                                )
                            )

                            Image(
                                painter = painterResource(id = R.mipmap.chat_ic_make_meeting_plan_picker),
                                contentDescription = "image description",
                                contentScale = ContentScale.None,
                                modifier = Modifier.size(14.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    // Use the extracted Address component
                    Address(
                        addressModel = AddressModel(
                            selectedAddressName = viewModel.selectedAddressName.collectAsState().value,
                            selectedAddress = viewModel.selectedAddress.collectAsState().value,

                            selectedProvince = viewModel.selectedProvince.collectAsState().value,
                            selectedDistrict = viewModel.selectedDistrict.collectAsState().value,
                            selectedCity = viewModel.selectedCity.collectAsState().value,

                            recommendAddress = settingDetail?.recommendAddress,

                            gps = settingDetail?.gps,
                            addressName = settingDetail?.addressName,
                            address = settingDetail?.address,
                            addressPicUrl = settingDetail?.addressPicUrl,
                            showCoupon = settingDetail?.showCoupon,
                            recommendPics = settingDetail?.recommendPics
                        ),
                        // 区分 Address 来源
                        addressKey = addressKey,
                        previewerState = previewerState,
                        onPreview = { index ->
                            scope.launch {
                                previewerState.enterTransform(index = index)
                            }
                        }
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    val selectedAddressUrl by viewModel.selectedAddressUrl.collectAsState()
                    AddressImage(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp),
                        addressPicUrl = (selectedAddressUrl
                            ?: settingDetail?.addressPicUrl).toString(),
                        onClick = {
                            // 获取当前选中的地址信息
                            val selectedLatitude = viewModel.selectedLatitude.value
                            val selectedLongitude = viewModel.selectedLongitude.value
                            val selectedAddress = viewModel.selectedAddress.value
                            val selectedAddressId = viewModel.selectedAddressId.value

                            // 创建 Intent 并添加已选地址的信息
                            val intent = Intent(
                                this@MakeMeetingPlanActivity,
                                SelectLocationActivity::class.java
                            )

                            // 如果有已选地址，则添加到 Intent 中
                            if (selectedLatitude != null && selectedLongitude != null && selectedAddress != null) {
                                intent.putExtra(EXTRA_SELECTED_LATITUDE, selectedLatitude)
                                intent.putExtra(EXTRA_SELECTED_LONGITUDE, selectedLongitude)
                                intent.putExtra(EXTRA_SELECTED_ADDRESS, selectedAddress)
                                intent.putExtra(EXTRA_SELECTED_ID, selectedAddressId)
                                intent.putExtra(EXTRA_PEER_ID, settingDetail?.peerId ?: "")
                            }

                            // 使用 startActivityForResult 来启动活动并接收返回的结果
                            startActivityForResult.launch(intent)
                        }
                    )
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(color = Color.White)
                    .padding(horizontal = 20.dp, vertical = 16.dp)
                    .align(alignment = Alignment.BottomCenter)
            ) {
                val isSubmitting by viewModel.isSubmitting.collectAsState()
                val submissionSuccess by viewModel.submissionSuccess.collectAsState()

                // Handle submission success
                LaunchedEffect(submissionSuccess) {
                    if (submissionSuccess) {
                        // Close the activity after successful submission
                        onBackClick()
                    }
                }

                // Get selected location data if available
                val selectedLatitude by viewModel.selectedLatitude.collectAsState()
                val selectedLongitude by viewModel.selectedLongitude.collectAsState()
                val selectedAddressName by viewModel.selectedAddressName.collectAsState()
                val selectedAddress by viewModel.selectedAddress.collectAsState()
                val selectedProvince by viewModel.selectedProvince.collectAsState()
                val selectedCity by viewModel.selectedCity.collectAsState()
                val selectedDistrict by viewModel.selectedDistrict.collectAsState()
                val selectedAddressUrl by viewModel.selectedAddressUrl.collectAsState()

                O2Button(
                    text = "确定",
                    enabled = !isSubmitting && settingDetail != null,
                    onClick = {
                        fun checkMeeting(recordId: String, meetTime: String) {
                            val observable =
                                RetrofitManager.getInstance().createApi(ChatApi::class.java)
                                    .getProtectMeetupChatReviewDetail(recordId, meetTime)
                            HttpExecutor.execute(
                                observable,
                                object : BaseRequestCallback<MeetingConflictResponse?>(true) {
                                    override fun onSuccess(data: MeetingConflictResponse?) {
                                        if (data != null && data.userId != null) {
                                            // 弹窗
                                            showMeetingConflictDialog(data,settingDetail?.peerId?:"")
                                        } else {
                                            // Get the necessary data from settingDetail or selected location
                                            settingDetail?.let { detail ->
                                                val recordId = recordId
                                                // Use selected location if available, otherwise use detail
                                                val gps =
                                                    if (selectedLatitude != null && selectedLongitude != null) {
                                                        "${selectedLongitude},${selectedLatitude}"
                                                    } else {
                                                        detail.gps ?: ""
                                                    }

                                                viewModel.submitMeetingPlanSetting(
                                                    recordId = recordId,
                                                    addressName = selectedAddressName
                                                        ?: detail.addressName ?: "",
                                                    address = selectedAddress ?: detail.address
                                                    ?: "",
                                                    province = selectedProvince ?: detail.province
                                                    ?: "",
                                                    city = selectedCity ?: detail.city ?: "",
                                                    district = selectedDistrict ?: detail.district
                                                    ?: "",
                                                    gps = gps,
                                                    selectedAddressUrl =
                                                        selectedAddressUrl ?: detail.addressPicUrl
                                                        ?: "",
                                                )
                                            }
                                        }
                                    }

                                    override fun dealFail(reason: ErrorReason?) {

                                    }
                                })
                        }

                        // Format the date and time as required by the API (yyyy-MM-dd HH:mm)
                        val calendar = viewModel.getSelectedDateTime()
                        checkMeeting(
                            recordId = settingDetail?.recordId.toString(),
                            meetTime =
                                LDate.getDate(calendar.time, "yyyy-MM-dd HH:mm")
                        )

                        reportPoint("O2-seeyou-seeinfoset-click") {
                            actionp2 = LDate.getDate(calendar.time, "yyyy-MM-dd HH:mm") // 见面时间
                            actionp3 =
                                selectedAddressName ?: settingDetail?.addressName ?: "" // 见面地点
                            actionp4 = if (selectedLatitude != null && selectedLongitude != null) {
                                "${selectedLongitude},${selectedLatitude}"
                            } else {
                                settingDetail?.gps ?: ""
                            } // 见面地点gps
                            actionp5 =
                                com.kanzhun.marry.location.GPSTracker.getLngLatStr() // 用户当前位置gps
                            peer_id = settingDetail?.peerId // 对方的ID
                        }
                    }
                )
            }

            // Loading indicator overlay
            if (isLoading == true) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color(0x80FFFFFF)) // Semi-transparent white background
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(48.dp)
                            .align(Alignment.Center),
                        color = Color(0xFF005EFF) // 使用应用的主色调
                    )
                }
            }

            PreviewImage(
                previewerState = previewerState,
                photos = recommendPics,
                onImagePageChanged = { item: String, idx: Int ->

                },
                onClosePreview = {
                    scope.launch {
                        previewerState.exitTransform()
                    }
                },
            )
        }
    }

    @Preview
    @Composable
    private fun ContentPreview() {
        val previewViewModel =
            androidx.lifecycle.viewmodel.compose.viewModel<MakeMeetingPlanViewModel>()
        previewViewModel.mock()
        Content(viewModel = previewViewModel)
    }
}