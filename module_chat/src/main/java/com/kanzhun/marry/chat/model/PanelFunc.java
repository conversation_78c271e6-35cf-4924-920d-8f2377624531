package com.kanzhun.marry.chat.model;

/**
 * <AUTHOR>
 * @date 2020/12/2.
 */
public class PanelFunc {
    public static final int TAKE_PHOTO = 0;
    public static final int CHOOSE_GALLERY = 1;
    public static final int APPOINTMENT_CARD = 2;
    public static final int CONFESSION_LETTER = 3;
    public static final int VOICE_CALL = 4;
    public static final int TOPIC_GAME = 5;
    // 默契考验
    public static final int TACIT_UNDERSTANDING_TEST = 6;
    // 聊天助手
    public static final int CHAT_ASSISTANT = 7;
    public static final int CHANGE_WX = 8;
    public static final int PROTECT_MEET = 9;
    public static final int LOCATION_SHARE = 10;

    private int drawableId;
    private String name;
    private int type;
    private boolean showProgress = false;
    private int progress;
    private int labelStatus;// 0 不显示，1-未解锁，2-已解锁，3-使用中

    public PanelFunc(int drawableId, String name, int type) {
        this.drawableId = drawableId;
        this.name = name;
        this.type = type;
    }

    public PanelFunc(int drawableId, String name, int type, int progress, boolean showProgress, int labelStatus) {
        this.drawableId = drawableId;
        this.name = name;
        this.type = type;
        this.progress = progress;
        this.showProgress = showProgress;
        this.labelStatus = labelStatus;
    }

    public int getDrawableId() {
        return drawableId;
    }

    public String getName() {
        return name;
    }

    public int getType() {
        return type;
    }

    public boolean isShowProgress() {
        return showProgress;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public int getLabelStatus() {
        return labelStatus;
    }

    public void setLabelStatus(int labelStatus) {
        this.labelStatus = labelStatus;
    }
}
