package com.kanzhun.marry.chat.emotion.preview;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.foundation.kotlin.ktx.ImageViewExtKt;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.emotion.data.EmotionItem;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;


/**
 * create by guofeng
 * date on 2022/11/3
 */

public class InnerEmotionPreviewLayoutImp extends IBasePreviewLayout<EmotionItem> {

    public InnerEmotionPreviewLayoutImp(Context context) {
        super(context);
        initView();
    }

    public InnerEmotionPreviewLayoutImp(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }


    public InnerEmotionPreviewLayoutImp(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private View mRootView;

    private TextView previewName;

    private ImageView simpleDraweeView;

    private void initView() {
        setBackgroundColor(Color.TRANSPARENT);
        LayoutInflater.from(getContext()).inflate(R.layout.twl_ui_inner_emotion_long_press_preview, this, true);
        mRootView = findViewById(R.id.mRootView);
        mRootView.setBackgroundResource( R.mipmap.bg_emotion_preview);
        simpleDraweeView = findViewById(R.id.iv_preview);
        previewName = findViewById(R.id.tv_preview);
    }


    @Override
    public void refresh(@NonNull EmotionItem innerEmotion, Rect rectArea) {

        String gifName = innerEmotion.getName();

        if (gifName != null) {
            String text = gifName
                    .replace("[", "")
                    .replace("]", "");
            previewName.setText(text);
        }

        String webpUrl = innerEmotion.getOrigUrl();
        ImageViewExtKt.loadUrl(simpleDraweeView, webpUrl);


        MarginLayoutParams rootViewLayoutParams = (MarginLayoutParams) mRootView.getLayoutParams();
        if (rootViewLayoutParams == null) {
            rootViewLayoutParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        }
        int width = rootViewLayoutParams.width;
        int height = rootViewLayoutParams.height;

        int rectWidth = rectArea.right - rectArea.left;

        if (width > rectWidth) {
            rootViewLayoutParams.leftMargin = rectArea.left - (width / 2 - rectWidth / 2);
        } else {
            rootViewLayoutParams.leftMargin = rectArea.left + (rectWidth / 2 - width / 2);
        }

        rootViewLayoutParams.topMargin = rectArea.bottom - height + QMUIDisplayHelper.dp2px(getContext(), 12);

        mRootView.setLayoutParams(rootViewLayoutParams);
    }
}