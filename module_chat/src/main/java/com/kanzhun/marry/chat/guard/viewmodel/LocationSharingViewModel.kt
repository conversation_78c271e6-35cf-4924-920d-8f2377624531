package com.kanzhun.marry.chat.guard.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amap.api.maps.model.LatLng
import com.kanzhun.foundation.kotlin.ktx.simplePost
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 位置共享页面的ViewModel
 */
class LocationSharingViewModel : ViewModel() {
    // recordId: String, peerId: String
    private var recordId: String? = null
    private var peerId: String? = null

    // 当前用户位置
    private val _myLocation = MutableStateFlow<LatLng?>(null)
    val myLocation: StateFlow<LatLng?> = _myLocation.asStateFlow()

    // 对方用户位置
    private val _peerLocation = MutableStateFlow<LatLng?>(null)
    val peerLocation: StateFlow<LatLng?> = _peerLocation.asStateFlow()

    // 见面地点位置
    private val _meetingLocation = MutableStateFlow<MeetingLocationInfo?>(null)
    val meetingLocation: StateFlow<MeetingLocationInfo?> = _meetingLocation.asStateFlow()

    // 当前用户信息
    private val _myUserInfo = MutableStateFlow<UserLocationInfo?>(null)
    val myUserInfo: StateFlow<UserLocationInfo?> = _myUserInfo.asStateFlow()

    // 对方用户信息
    private val _peerUserInfo = MutableStateFlow<UserLocationInfo?>(null)
    val peerUserInfo: StateFlow<UserLocationInfo?> = _peerUserInfo.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    /**
     * 初始化位置共享
     */
    fun initLocationSharing(recordId: String, peerId: String) {
        this.recordId = recordId
        this.peerId = peerId
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // TODO: 调用API获取见面计划详情和对方用户信息
                // 这里应该调用实际的API来获取数据
                loadMeetingPlanDetail(recordId)
                loadPeerUserInfo(peerId)
                startLocationUpdates(recordId, peerId)
            } catch (e: Exception) {
                _errorMessage.value = "初始化失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 更新当前用户位置
     */
    fun updateMyLocation(location: LatLng) {
        _myLocation.value = location

        // 将位置信息发送到服务器，实现实时位置共享
        uploadMyLocationToServer(chatId = recordId ?: "", location = location, rotateAngle = 0)
    }

    /**
     * 更新对方用户位置（从服务器接收）
     */
    fun updatePeerLocation(location: LatLng, rotateAngle: Int) {
        _peerLocation.value = location
    }

    /**
     * 设置见面地点信息
     */
    fun setMeetingLocation(location: MeetingLocationInfo) {
        _meetingLocation.value = location
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 加载见面计划详情
     */
    private suspend fun loadMeetingPlanDetail(recordId: String) {
        // TODO: 实现API调用
        // 示例数据
        _meetingLocation.value = MeetingLocationInfo(
            name = "星巴克咖啡厅",
            address = "北京市朝阳区建国门外大街1号",
            latitude = 39.906901,
            longitude = 116.397972
        )
    }

    /**
     * 加载对方用户信息
     */
    private suspend fun loadPeerUserInfo(peerId: String) {
        // TODO: 实现API调用
        // 示例数据
        _peerUserInfo.value = UserLocationInfo(
            userId = peerId,
            nickname = "对方用户",
            avatar = "",
            location = null,
            bearing = 0f // 手机方向角度
        )
    }

    /**
     * 开始位置更新
     */
    private fun startLocationUpdates(recordId: String, peerId: String) {
        // no-op
        // DataSync 会同步对方位置过来
    }

    /**
     * 上传当前用户位置到服务器
     */
    private fun uploadMyLocationToServer(chatId: String, location: LatLng, rotateAngle: Int = 0) {
        viewModelScope.launch {
            try {
                // 将位置信息发送到服务器
                // 这样对方就能实时看到当前用户的位置

                "orange/location/share/report".simplePost(
                    mapOf(
                        "chatId" to chatId, // 替换为实际的recordId
                        // gps信息，经纬度英文逗号分割
                        "gpsInfo" to "${location.latitude},${location.longitude}",
                        "rotateAngle" to rotateAngle.toString()
                    )
                )
            } catch (_: Exception) {

            }
        }
    }

    /**
     * 停止位置共享
     */
    fun stopLocationSharing() {

    }

    override fun onCleared() {
        super.onCleared()
        stopLocationSharing()
    }
}

/**
 * 见面地点信息
 */
data class MeetingLocationInfo(
    val name: String,
    val address: String,
    val latitude: Double,
    val longitude: Double
)

/**
 * 用户位置信息
 */
data class UserLocationInfo(
    val userId: String,
    val nickname: String,
    val avatar: String,
    val location: LatLng?,
    val bearing: Float // 手机方向角度，用于显示用户朝向
)
