package com.kanzhun.marry.chat.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.chat.api.ChatApi;
import com.kanzhun.marry.chat.model.CheckCardStatusBean;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class LoveCardViewModel extends FoundationViewModel {

    private MutableLiveData<Boolean> loadingLiveData = new MutableLiveData<>();
    private MutableLiveData<CheckCardStatusBean> cardStatusLiveData = new MutableLiveData<>();
    private ObservableField<CheckCardStatusBean> cardStatusField = new ObservableField<>();

    public String chatId;
    public String fromName;
    public String toName;

    /**
     * 获取相识卡/表白信发送状态
     *
     * @param type 1:相识卡 2:表白信
     */
    public void checkCardStatus(int type) {
        Observable<BaseResponse<CheckCardStatusBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(ChatApi.class).checkCardStatus(type, chatId);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<CheckCardStatusBean>(true) {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                loadingLiveData.postValue(true);
            }

            @Override
            public void onSuccess(CheckCardStatusBean data) {
                cardStatusField.set(data);
                cardStatusLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                super.onComplete();
                loadingLiveData.postValue(false);
            }
        });
    }

    public MutableLiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }

    public MutableLiveData<CheckCardStatusBean> getCardStatusLiveData() {
        return cardStatusLiveData;
    }

    public ObservableField<CheckCardStatusBean> getCardStatusField() {
        return cardStatusField;
    }

    public void setCardStatusField(ObservableField<CheckCardStatusBean> cardStatusField) {
        this.cardStatusField = cardStatusField;
    }

    public LoveCardViewModel(Application application) {
        super(application);
    }

}