package com.kanzhun.webview;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.kanzhun.foundation.Constants;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.rxbus.RxBus;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

public class WebViewForAgreementActivity extends AppCompatActivity {

    private final static String WEB_VIEW_AGREEMENT = "web_view_agreement";
    private WebView webView;

    public static void jumpToWebViewForAgreementActivity(Context context, String url) {
        RxBus.getInstance().post("open", Constants.POST_WEB_AGREEMENT);
        Intent intent = new Intent(context, WebViewForAgreementActivity.class);
        intent.putExtra(WEB_VIEW_AGREEMENT, url);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        QMUIStatusBarHelper.translucent(this);
        getWindow().setStatusBarColor(getResources().getColor(com.kanzhun.common.R.color.white));
        setContentView(R.layout.activity_webview_for_agreement);
        webView = findViewById(R.id.webview);
        initWebView();
        webView.loadUrl(getIntent().getStringExtra(WEB_VIEW_AGREEMENT));
    }

    @SuppressLint("JavascriptInterface")
    public void initWebView() {
        WebSettings settings = webView.getSettings();
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setJavaScriptEnabled(true);
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setBuiltInZoomControls(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);
//        settings.setAppCacheEnabled(true);

        settings.setMediaPlaybackRequiresUserGesture(false);
        settings.setAllowFileAccess(true);
        settings.setTextZoom(100);
        settings.setDisplayZoomControls(false);

        settings.setAllowFileAccessFromFileURLs(false);
        settings.setAllowUniversalAccessFromFileURLs(false);
        WebView.setWebContentsDebuggingEnabled(true);
        settings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        String userAgent = settings.getUserAgentString();
        String bossUserAgent = "orange2/" + SettingBuilder.getInstance().getVersionName();
        if (userAgent != null) {
            userAgent = userAgent + " " + bossUserAgent;
        } else {
            userAgent = bossUserAgent;
        }
        settings.setUserAgentString(userAgent);
        webView.setWebViewClient(new WebViewClientListener(this));
        webView.addJavascriptInterface(new WebViewForAgreementJs(this), "BZLBridge");
        webView.setWebChromeClient(new WebChromeClient());
    }

    public WebView getWebView() {
        return webView;
    }

    public static class WebViewClientListener extends WebViewClient {

        private final Activity activity;

        public WebViewClientListener(Activity activity) {
            this.activity = activity;
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            Uri uri = request.getUrl();
            if (uri != null) {
                return load(uri.toString());
            }
            return true;
        }

        private boolean load(String url) {
            //修复用户协议上的webview无法跳转新Url问题
//            if (!TextUtils.isEmpty(url)) {
//                return true;
//            }
            return false;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().post("close", Constants.POST_WEB_AGREEMENT);
    }
}
