package com.kanzhun.webview;

import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.utils.SettingBuilder;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.StatusBarUtil;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/7/1.
 */
public class WebViewForAgreementJs {
    FragmentActivity activity;

    public WebViewForAgreementJs(FragmentActivity activity) {
        this.activity = activity;
    }

    /**
     * 关闭h5界面
     */
    @JavascriptInterface
    public void closeWindow() {
        if (activity != null) {
            AppUtil.finishActivity(activity);
        }
    }

    @JavascriptInterface
    public int getVersion() {
        return 1020000;
    }

    @JavascriptInterface
    public int getStatusBarHeight() {
        if (activity != null) {
            int barHeightPx = StatusBarUtil.getStatusBarHeight(activity);
            float density = activity.getResources().getDisplayMetrics().density;
            return (int) (barHeightPx / density);
        } else {
            return 0;
        }
    }

    @JavascriptInterface
    public void setStatusBarModel(String status) {
        if (activity == null) {
            return;
        }
        if (TextUtils.isEmpty(status)) {
            return;
        }
        try {
            JSONObject object = new JSONObject(status);
            boolean isLight = TextUtils.equals("light", object.getString("model")); //ios系统风格，light是透明，dark是白色
            ExecutorFactory.execMainTask(new Runnable() {
                @Override
                public void run() {
                    if (isLight) {
                        QMUIStatusBarHelper.setStatusBarLightMode(activity);
                    } else {
                        QMUIStatusBarHelper.setStatusBarDarkMode(activity);
                    }
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }


}
