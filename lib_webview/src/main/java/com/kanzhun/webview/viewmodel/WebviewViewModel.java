package com.kanzhun.webview.viewmodel;


import android.app.Application;
import android.net.Uri;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.utils.T;
import com.kanzhun.utils.file.FileUtils;
import com.techwolf.lib.tlog.TLog;

import java.io.File;
import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * create by sunyangyang
 * on 2020-01-10
 */
public class WebviewViewModel extends FoundationViewModel {

    public MutableLiveData<Boolean> uploadSuccessLiveData = new MutableLiveData<>();
    public WebviewViewModel(Application application) {
        super(application);
    }

    public void uploadFile(String path) {
        setShowProgressBar();
        UploadFileUtil.uploadImage(UploadFileUtil.AUTH_SOURCE,  Uri.fromFile(new File(path)), new UploadRequestCallback<ImageUploadModel>() {

            @Override
            public void handleInChildThread(ImageUploadModel data) {
                super.handleInChildThread(data);
                if (data != null && data.originImage != null) {
                    HashMap<String,Object> hashMap = new HashMap<>();
                    hashMap.put("chsiUrl",data.originImage.url);
                    TLog.print("chsiUrl",data.originImage.url);
                    HttpExecutor.requestSimplePost(URLConfig.URL_ORANGE_CERT_CHSI_ONLINE, hashMap, new SimpleRequestCallback() {
                        @Override
                        public void onSuccess() {
                            uploadSuccessLiveData.postValue(true);
                            PointHelperKt.reportPoint("xuexinwang-page-click", new Function1<PointBean, Unit>() {
                                @Override
                                public Unit invoke(PointBean pointBean) {
                                    pointBean.setType("发起验证");
                                    pointBean.setResult("提交成功");
                                    return null;
                                }
                            });
                        }

                        @Override
                        public void dealFail(ErrorReason reason) {
                            T.ss(reason.getErrReason());
                            PointHelperKt.reportPoint("xuexinwang-page-click", new Function1<PointBean, Unit>() {
                                @Override
                                public Unit invoke(PointBean pointBean) {
                                    pointBean.setType("发起验证");
                                    pointBean.setActionp2(reason.getErrReason());
                                    pointBean.setResult(reason.getErrReason());
                                    return null;
                                }
                            });
                        }

                        @Override
                        public void onComplete() {
                            super.onComplete();
                            FileUtils.delete(new File(path));
                            hideShowProgressBar();
                        }
                    });

                }

            }

            @Override
            public void onSuccess(ImageUploadModel data) {

            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
                FileUtils.delete(new File(path));
                hideShowProgressBar();
            }

            @Override
            public void onComplete() {
                super.onComplete();

            }
        });
    }
}
