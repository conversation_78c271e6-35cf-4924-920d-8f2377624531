package com.kanzhun.marry.take.widget;

import android.app.Activity;
import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.Point;
import android.graphics.Rect;
import android.hardware.Camera;
import android.media.CamcorderProfile;
import android.media.MediaRecorder;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.marry.take.CameraDirection;
import com.kanzhun.marry.take.CameraManager;
import com.kanzhun.marry.take.FlashLightStatus;
import com.kanzhun.marry.take.inter.IActivityLifeCycle;
import com.kanzhun.marry.take.inter.ICameraOperation;
import com.kanzhun.marry.take.inter.SwitchCameraCallback;
import com.kanzhun.marry.take.util.OrientationUtil;
import com.kanzhun.marry.take.util.TakeFileUtil;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/6.
 */
public class CameraPreview extends SurfaceView implements SurfaceHolder.Callback, ICameraOperation, IActivityLifeCycle {
    private static final String TAG = "CameraPreview";
    private CameraManager mCameraManager;
    private CameraDirection mCameraId;
    private Camera mCamera;
    private SwitchCameraCallback mSwitchCameraCallBack;
    private Camera.PictureCallback callback;
    private Activity mActivity;
    private int mZoom; //当前缩放
    /**
     * 录制控制类
     */
    private MediaRecorder mediaRecorder;
    /**
     * 录制标记
     */
    private volatile boolean isRecording;
    /**
     * 录制准备完成
     */
    private volatile boolean isPrepareRecord;
    /**
     * 录像监听
     */
    private List<OnRecordListener> recordListeners = new ArrayList<>();
    /**
     * 录像保存文件
     */
    private File outputMediaFile;

    public CameraPreview(Context context) {
        this(context, null);
    }

    public CameraPreview(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CameraPreview(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mCameraManager = new CameraManager(context);
        mCameraId = mCameraManager.getCameraDirection();
        setFocusable(true);
        getHolder().addCallback(this); // 为SurfaceView的句柄添加一个回调函数
    }

    @Override
    public void onCreate(Activity activity) {
        this.mActivity = activity;
    }

    @Override
    public void onResume() {
        if (mCamera != null) {
            mCamera.startPreview();
        }
    }

    @Override
    public void onPause() {
        mCameraManager.releaseCamera(mCamera);
        mCamera = null;
    }

    @Override
    public void onDestroy() {
        mCameraManager.releaseCamera(mCamera);
        mCamera = null;
        Log.i(TAG, "onDestroy: ");
    }


    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        try {
            //android 6.0以下部分手机如果拒绝权限时，无法获取拒绝权限的监听，此时mCamera可能为null
            mCamera = mCameraManager.openCamera(mCameraId.ordinal());
            mCamera.setPreviewDisplay(holder);
            mCamera.startPreview();
        } catch (IOException e) {
            Log.d(TAG, "Error setting camera preview: " + e.getMessage());
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        if (holder.getSurface() == null) {
            return;
        }
        mCameraManager.releaseCamera(mCamera);
        mCamera = null;
        //打开默认的摄像头
        setUpCamera(mCameraId, false);

        Log.i(TAG, "surfaceChanged:");
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        onDestroy();
        Log.i(TAG, "surfaceDestroyed:");
    }

    /**
     * 设置当前的Camera 并进行参数设置
     */
    private void setUpCamera(CameraDirection mCameraId, boolean isSwitchFromFront) {
        try {
            mCamera = mCameraManager.openCamera(mCameraId.ordinal());
            mCamera.setPreviewDisplay(getHolder());
            initCamera();
            mCameraManager.setCameraDirection(mCameraId);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (mSwitchCameraCallBack != null) {
            mSwitchCameraCallBack.switchCamera(isSwitchFromFront);
        }
    }

    /**
     * 开始照片预览
     */
    public void preview() {
        if (mCamera != null) {
            mCamera.startPreview();
        }
    }

    public void initCamera() {
        Camera.Parameters parameters = mCamera.getParameters();
        parameters.setPictureFormat(ImageFormat.JPEG);
        parameters.setRecordingHint(true);
        List<String> focusModes = parameters.getSupportedFocusModes();
        //设置对焦模式
        if (focusModes.contains(Camera.Parameters.FOCUS_MODE_AUTO)) {
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
        }
        // 修正surfaceView的宽高
        mCameraManager.setFitSize(mCamera);
        mCamera.setDisplayOrientation(OrientationUtil.determineDisplayOrientationDegree(mActivity, mCameraId.ordinal()));
        mCamera.startPreview();
        turnLight(mCameraManager.getFlashLightStatus());  //设置闪光灯
    }

    /**
     * 闪光灯开关   开->关->自动
     */
    private void turnLight(FlashLightStatus lightStatus) {
        if (CameraManager.mFlashLightNotSupport.contains(lightStatus)) {
            turnLight(lightStatus.next());
            return;
        }
        if (mCamera == null || mCamera.getParameters() == null
                || mCamera.getParameters().getSupportedFlashModes() == null) {
            return;
        }
        Camera.Parameters parameters = mCamera.getParameters();
        List<String> supportedModes = mCamera.getParameters().getSupportedFlashModes();

        switch (lightStatus) {
            case LIGHT_OFF:
                parameters.setFlashMode(Camera.Parameters.FLASH_MODE_OFF);
                break;
            case LIGHT_ON:
                if (supportedModes.contains(Camera.Parameters.FLASH_MODE_TORCH)) {
                    parameters.setFlashMode(Camera.Parameters.FLASH_MODE_TORCH);
                } else if (supportedModes.contains(Camera.Parameters.FLASH_MODE_OFF)) {
                    parameters.setFlashMode(Camera.Parameters.FLASH_MODE_OFF);
                }
                break;
            default:
                break;
        }
        mCamera.setParameters(parameters);
        mCameraManager.setFlashLightStatus(lightStatus);
    }

    /**
     * 切换前后摄像头
     */
    @Override
    public void switchCamera() {
        mCameraId = mCameraId.next();
        onDestroy();
        setUpCamera(mCameraId, mCameraId == CameraDirection.CAMERA_BACK);
    }

    /**
     * 拍照
     */
    @Override
    public boolean takePicture() {
        try {
            mCamera.takePicture(null, null, callback);
            Log.i(TAG, "拍照");
        } catch (Throwable t) {
            onDestroy();
            //打开默认的摄像头
            setUpCamera(mCameraId, false);
            Log.e(TAG, "拍照失败 ：" + t.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public void recordVideo() {
        if (!isRecording) {
            // initialize video camera
            if (isPrepareRecord) {
                // Camera is available and unlocked, MediaRecorder is prepared,
                // now you can start recording
                mediaRecorder.start();
                // inform the user that recording has started
                isRecording = true;
                Log.i(TAG, "开始录制 ");
            } else {
                // prepare didn't work, release the camera
                releaseMediaRecorder();
                // inform user
            }
        }
    }

    @Override
    public void stopRecord() {
        Log.i(TAG, "stopRecord: " + isRecording);
        if (isRecording && mediaRecorder != null) {
            // stop recording and release camera
            try {
                mediaRecorder.stop();
                for (OnRecordListener listener : recordListeners) {
                    listener.onEndOfRecord(outputMediaFile);
                }
                Log.i(TAG, "结束录制");
            } catch (RuntimeException e) { //关闭的太快会报错
                //录制出来无效的视频文件需要删除,马上删除会有0k文件留存
                //给一秒钟时间来保证删除
                postDelayed(() -> {
                    if (outputMediaFile != null) {
                        outputMediaFile.delete();
                    }
                }, 2000);
                for (OnRecordListener listener : recordListeners) {
                    listener.onEndOfRecord(null);
                }
            }
            releaseMediaRecorder(); // release the MediaRecorder object
            mCamera.lock(); // take camera access back from MediaRecorder
            // inform the user that recording has stopped
            isRecording = false;
            isPrepareRecord = false;
        }
    }

    @Override
    public void shortRecordRelease() {
        Log.i(TAG, "shortRecordRelease: " + isRecording);
        if (isRecording && mediaRecorder != null) {
            for (OnRecordListener listener : recordListeners) {
                listener.onShortRecord();
            }
            Log.i(TAG, "录制时间过短");
            //录制出来无效的视频文件需要删除,马上删除会有0k文件留存
            //给一秒钟时间来保证删除
            postDelayed(() -> {
                if (outputMediaFile != null) {
                    outputMediaFile.delete();
                }
            }, 2000);
        }
        releaseMediaRecorder(); // release the MediaRecorder object
        mCamera.lock(); // take camera access back from MediaRecorder
        // inform the user that recording has stopped
        isRecording = false;
        isPrepareRecord = false;
    }

    /**
     * 闪光灯开启，关闭
     */
    @Override
    public void switchFlashMode() {
        turnLight(mCameraManager.getFlashLightStatus().next());
    }

    /**
     * 相机最大缩放级别
     */
    @Override
    public int getMaxZoom() {
        if (mCamera == null) {
            return -1;
        }
        Camera.Parameters parameters = mCamera.getParameters();
        if (!parameters.isZoomSupported()) {
            return -1;
        }
        return parameters.getMaxZoom() > 40 ? 40 : parameters.getMaxZoom();
    }

    /**
     * 设置当前缩放级别
     *
     * @param zoom
     */
    @Override
    public void setZoom(int zoom) {
        if (mCamera == null) {
            return;
        }
        Camera.Parameters parameters;
        //注意此处为录像模式下的setZoom方式。在Camera.unlock之后，调用getParameters方法会引起android框架底层的异常
        //stackoverflow上看到的解释是由于多线程同时访问Camera导致的冲突，所以在此使用录像前保存的mParameters。
        parameters = mCamera.getParameters();
        if (!parameters.isZoomSupported()) {
            return;
        }
        parameters.setZoom(zoom);
        mCamera.setParameters(parameters);
        mZoom = zoom;
    }

    /**
     * 获取当前缩放级别
     */
    @Override
    public int getZoom() {
        return mZoom;
    }


    public CameraDirection getCameraId() {
        return mCameraId;
    }


    public void setPictureCallback(Camera.PictureCallback callback) {
        this.callback = callback;
    }

    public void setSwitchCameraCallBack(SwitchCameraCallback mSwitchCameraCallBack) {
        this.mSwitchCameraCallBack = mSwitchCameraCallBack;
    }

    /**
     * 手动聚焦
     *
     * @param point 触屏坐标
     */
    protected boolean onFocus(Point point, Camera.AutoFocusCallback callback) {
        if (mCamera == null) {
            return false;
        }
        Camera.Parameters parameters = null;
        try {
            parameters = mCamera.getParameters();
        } catch (RuntimeException e) {
            //开始录像后锁定了照相机，获取parameter会报错
            return false;
        }
        //不支持设置自定义聚焦，则使用自动聚焦，返回
        if (Build.VERSION.SDK_INT >= 14) {
            if (parameters.getMaxNumFocusAreas() <= 0) {
                return focus(callback);
            }
            int screenWidth = QMUIDisplayHelper.getScreenWidth(getContext());
            int screenHeight = QMUIDisplayHelper.getScreenHeight(getContext());
            final List<Camera.Area> areas = new ArrayList<Camera.Area>();
            Rect rect = new Rect(point.x - 100, point.y - 100, point.x + 100, point.y + 100);
            int left = rect.left * 2000 / screenWidth - 1000;
            int top = rect.top * 2000 / screenHeight - 1000;
            int right = rect.right * 2000 / screenWidth - 1000;
            int bottom = rect.bottom * 2000 / screenHeight - 1000;
            // 如果超出了(-1000,1000)到(1000, 1000)的范围，则会导致相机崩溃
            left = left < -1000 ? -1000 : left;
            top = top < -1000 ? -1000 : top;
            right = right > 1000 ? 1000 : right;
            bottom = bottom > 1000 ? 1000 : bottom;
            areas.add(new Camera.Area(new Rect(left, top, right, bottom), 100));
            parameters.setFocusAreas(areas);
            parameters.setMeteringAreas(areas);
            List<String> focusModes = parameters.getSupportedFocusModes();
            //设置对焦模式
            if (focusModes.contains(Camera.Parameters.FOCUS_MODE_AUTO)) {
                parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            }
            try {
                // 使用的小米手机在设置聚焦区域的时候经常会出异常，看日志发现是框架层的字符串转int的时候出错了，
                // 目测是小米修改了框架层代码导致，在此try掉，对实际聚焦效果没影响
                mCamera.setParameters(parameters);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return focus(callback);
    }

    private boolean focus(Camera.AutoFocusCallback callback) {
        try {
            if (mCamera != null) {
                mCamera.cancelAutoFocus();
                mCamera.autoFocus(callback);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 配置 MediaRecorder
     * 官方文档示例
     */
    public boolean prepareVideoRecorder() {

        mediaRecorder = new MediaRecorder();

        // Step 1: Unlock and set camera to MediaRecorder
        mCamera.unlock();
        mediaRecorder.setCamera(mCamera);

        // Step 2: Set sources
        mediaRecorder.setAudioSource(MediaRecorder.AudioSource.CAMCORDER);
        mediaRecorder.setVideoSource(MediaRecorder.VideoSource.CAMERA);

        // Step 3: Set a CamcorderProfile (requires API Level 8 or higher)
        mediaRecorder.setProfile(CamcorderProfile.get(Camera.CameraInfo.CAMERA_FACING_FRONT, CamcorderProfile.QUALITY_HIGH));

        // Step 4: Set output file
        outputMediaFile = TakeFileUtil.getOutputMediaFile(getContext(), TakeFileUtil.MEDIA_TYPE_VIDEO);
        mediaRecorder.setOutputFile(outputMediaFile.getPath());
        //设置录制的视频的方向
        if (mCameraId == CameraDirection.CAMERA_BACK) {
            mediaRecorder.setOrientationHint(OrientationUtil.determineDisplayOrientationDegree(mActivity, mCameraId.ordinal()));
        } else {
            mediaRecorder.setOrientationHint(OrientationUtil.determineDisplayOrientationDegree(mActivity, mCameraId.ordinal()) + 180);
        }

        // Step 5: Set the preview output
        mediaRecorder.setPreviewDisplay(getHolder().getSurface());

        // Step 6: Prepare configured MediaRecorder
        try {
            mediaRecorder.prepare();
            Log.i(TAG, "prepareVideoRecorder");
        } catch (IllegalStateException e) {
            Log.d(TAG, "IllegalStateException preparing MediaRecorder: " + e.getMessage());
            releaseMediaRecorder();
            return false;
        } catch (IOException e) {
            Log.d(TAG, "IOException preparing MediaRecorder: " + e.getMessage());
            releaseMediaRecorder();
            return false;
        }
        isPrepareRecord = true;
        return true;
    }

    /**
     * 释放录制配置的资源
     */
    public void releaseMediaRecorder() {
        if (mediaRecorder != null) {
            mediaRecorder.reset();   // clear recorder configuration
            mediaRecorder.release(); // release the recorder object
            mediaRecorder = null;
            mCamera.lock();           // lock camera for later use
            Log.i(TAG, "releaseMediaRecorder ");
        }
    }

    /**
     * 添加录屏结果监听
     */
    public void addOnRecordListener(OnRecordListener listener) {
        recordListeners.add(listener);
    }

    public interface OnRecordListener {
        /**
         * 录屏成功返回文件，失败返回null
         */
        void onEndOfRecord(File resultFile);

        /**
         * 录制时间过短
         */
        void onShortRecord();
    }


}

