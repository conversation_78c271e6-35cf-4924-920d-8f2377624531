package com.kanzhun.http.monitor

import com.hpbr.apm.Apm
import com.hpbr.apm.event.ApmAnalyzer
import com.kanzhun.http.monitor.service.ACCOUNT_HELPER_SERVICE
import com.kanzhun.http.monitor.service.IAccountHelperService
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.util.concurrent.TimeUnit

private const val TAG = "HttpTimeConsumingInterc"

private const val ACTION_HTTP_TIME_CONSUMING = "action_http_time_consuming"

class HttpTimeConsumingInterceptor : Interceptor {
    private var defaultTookMs: Long = -1
    private var retrieveTime: Long = -1

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val startNs = System.nanoTime()

        val response: Response
        try {
            response = chain.proceed(chain.request())
        } catch (e: Exception) {
            throw e
        }

        val tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs)

        try {
            if (tookMs > getTookMs()) { // 只上报大于 tookMs 时间的请求

                //region 截取业务请求接口中的 /api/orange 片段
                val requestUrl = response.request.url.toString()
                val fullApiUrl =
                    if (requestUrl.contains("/api/orange")) {
                        val apiUrl = requestUrl
                            .substringAfter("/api")
                            .substringBefore("?")
                        "/api$apiUrl"
                    } else {
                        ""
                    }
                //endregion

                ApmAnalyzer.create().action(ACTION_HTTP_TIME_CONSUMING)
                    .p2(response.code.toString()) // code
                    .p3(response.message) // msg
                    .p4(requestUrl) // url
                    .p5(tookMs.toString()) // tookMs
                    .p6(fullApiUrl) // 示例：/api/orange/recommend/users/v1
                    .report()

                TLog.info(
                    TAG,
                    "code: ${response.code}, msg: ${response.message}, url: ${response.request.url}, tookMs: ${tookMs}ms"
                )
            }
        } catch (e: Throwable) {
            TLog.error(TAG, "getTookMs, $e")
        }

        return response
    }

    private fun getTookMs(): Long {
        // 超过一天，重新获取tookMs
        val nowTime = System.currentTimeMillis()
        if (nowTime - retrieveTime > 24 * 60 * 60 * 1000L) {
            TLog.info(TAG, "超过一天，重新获取tookMs")

            reloadTookMs()
            retrieveTime = nowTime
        } else {
            TLog.info(TAG, "未超过一天，使用默认tookMs: $defaultTookMs")

            // 首次获取tookMs
            if (defaultTookMs < 0) {
                TLog.info(TAG, "首次获取tookMs")

                reloadTookMs()
            }
        }

        return defaultTookMs
    }

    /**
     * https://apm.weizhipin.com/admin/application/android/custom-config?appKey=Y5LTHZ5237qbk08U
     *
     * 配置：tookMs，默认300ms
     */
    private fun reloadTookMs() {
        val jsonObject = Apm.getPublicConfigJsonObject()
        TLog.info(TAG, "getPublicConfigJsonObject: $jsonObject")

        if (jsonObject != null) {
            val monitorJo = jsonObject.optJSONObject("monitor")
            monitorJo?.let {
                defaultTookMs = it.optLong("tookMs", 400)
                TLog.info(TAG, "reloadTookMs, defaultTookMs: $defaultTookMs")
            }
        }
    }
}

fun addHttpTimeConsumingInterceptor(builder: okhttp3.OkHttpClient.Builder) {
    // apm后台支持灰度开关控制，如，根据用户id尾号
    try {
        if (isGrayNum()) {
            TLog.info(TAG, "addHttpTimeConsumingInterceptor, isGrayNum: true")
            builder.addInterceptor(HttpTimeConsumingInterceptor())
        }
    } catch (e: Throwable) {
        TLog.error(TAG, "addHttpTimeConsumingInterceptor, isGrayNum: false, $e")
    }
}

private fun isGrayNum(): Boolean {
    val jsonObject = Apm.getPublicConfigJsonObject()
    TLog.info(TAG, "getPublicConfigJsonObject: $jsonObject")

    if (jsonObject != null) {
        val monitorJo = jsonObject.optJSONObject("monitor")
        monitorJo?.let {
            // 尾号：0/1/2/3/4/5/6/7/8/9
            // 0: <= 0 0 10%
            // 1: <= 1 0/1 20%
            // 2: <= 2 0/1/2 30%
            // 3: <= 3 0/1/2/3 40%
            // 4: <= 4 0/1/2/3/4 50%
            // 5: <= 5 0/1/2/3/4/5 60%
            // 6: <= 6 0/1/2/3/4/5/6 70%
            // 7: <= 7 0/1/2/3/4/5/6/7 80%
            // 8: <= 8 0/1/2/3/4/5/6/7/8 90%
            // 9: <= 9 0/1/2/3/4/5/6/7/8/9 100%
            // <0: 不命中
            // >9: 全量
            val inGrayNum = it.optLong("inGrayNum", -1)
            TLog.info(TAG, "isGrayNum, inGrayNum: $inGrayNum")

            Router.getService(IAccountHelperService::class.java, ACCOUNT_HELPER_SERVICE)
                ?.getUserId()?.let { userId ->
                    val userIdTail = userId % 10 // 获取用户id尾号
                    TLog.info(TAG, "isGrayNum, userId: $userId, userIdTail: $userIdTail")
                    return userIdTail <= inGrayNum
                }
        }
    }

    return false
}