package com.kanzhun.http.kotlin.flow

import com.kanzhun.http.kotlin.error.ApiException
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.utils.L
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart

/**
 * 通过flow执行请求，需要在协程作用域中执行
 * @param errorBlock 错误回调
 * @param requestCall 执行的请求
 * @param showLoading 开启和关闭加载框
 * @return 请求结果
 */
suspend fun <T> requestFlow(
    errorBlock: ((Int?, String?) -> Unit)? = null,
    requestCall: suspend () -> BaseResponse<T>?,
    showLoading: ((Boolean) -> Unit)? = null
): T? {
    var data: T? = null
    val flow = requestFlowResponse(errorBlock, requestCall, showLoading)
    flow.collect {
        data = it?.data
    }
    return data
}

/**
 * 通过flow执行请求，需要在协程作用域中执行
 * @param errorBlock 错误回调
 * @param requestCall 执行的请求
 * @param showLoading 开启和关闭加载框
 * @return Flow<BaseResponse<T>>
 */
suspend fun <T> requestFlowResponse(
    errorBlock: ((Int?, String?) -> Unit)? = null,
    requestCall: suspend () -> BaseResponse<T>?,
    showLoading: ((Boolean) -> Unit)? = null
): Flow<BaseResponse<T>?> {
    //1.执行请求
    val flow = flow {
        //设置超时时间
        val response = kotlinx.coroutines.withTimeout(10 * 1000) {
            requestCall()
        }

        if (response?.isFailed == true) {
            throw ApiException(response.code, response.msg)
        }
        //2.发送网络请求结果回调
        emit(response)
        //3.指定运行的线程
    }.flowOn(Dispatchers.IO)
        .onStart {
            showLoading?.invoke(true)
        }.catch { e ->
            if(e is ApiException){
                errorBlock?.let { it(e.errCode,e.errMsg) }
            }
            e.printStackTrace()
            L.e("flow", e)
        }
        .onCompletion {
            showLoading?.invoke(false)
        }
    return flow
}