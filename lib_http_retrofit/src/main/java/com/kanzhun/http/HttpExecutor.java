package com.kanzhun.http;

import android.os.Environment;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.JsonObject;
import com.kanzhun.http.error.ErrorReason;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.kanzhun.http.api.BaseApiService;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.config.HttpConfig;
import com.kanzhun.http.download.DownLoadService;
import com.kanzhun.http.download.LoadOnSubscribe;
import com.kanzhun.http.download.callback.DownLoadListener;
import com.kanzhun.http.download.callback.loadCallback;
import com.kanzhun.http.download.converter.FileResponseBodyConverter;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.transformer.BaseObserver;
import com.kanzhun.http.transformer.JsonResponseTransformer;
import com.kanzhun.http.transformer.ResponseTransformer;
import com.kanzhun.http.transformer.SimpleBaseObserver;
import com.kanzhun.http.transformer.SimpleResponseTransformer;
import com.kanzhun.http.upload.UploadObserver;
import com.kanzhun.http.upload.UploadOnSubscribe;
import com.kanzhun.http.upload.UploadParam;
import com.kanzhun.http.upload.UploadParamForUri;
import com.kanzhun.http.upload.UploadRequestBody;
import com.kanzhun.http.upload.UploadRequestBodyForUri;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.http.upload.UploadService;
import com.kanzhun.utils.file.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.schedulers.Schedulers;
import okhttp3.Headers;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/11
 */
public class HttpExecutor {

    public static String defaultDownLoadPath;

    public static void initialize(HttpConfig config) {
        if (config == null) {
            throw new NullPointerException("You must initialize HttpConfig first.");
        }
        HttpConfig.getInstance().initCustom(config);
    }

    public static void requestSimplePost(String url, Map<String, Object> params, SimpleRequestCallback callback) {
        if(params == null){
            params = new HashMap<>();
        }
        if (callback == null){
            callback = new SimpleRequestCallback() {
                @Override
                public void onSuccess() {

                }

                @Override
                public void dealFail(ErrorReason reason) {

                }
            };
        }
        Observable<BaseResponse> simpleResponseObservable = RetrofitManager.getInstance().createApi(BaseApiService.class).executePost(url, params);
        requestSimple(simpleResponseObservable, callback);
    }

    public static void requestSimplePost(String url, Map<String, Object> params, SimpleRequestCallback callback,boolean autoErrorToast) {
        if(params == null){
            params = new HashMap<>();
        }
        if (callback == null){
            callback = new SimpleRequestCallback(autoErrorToast) {
                @Override
                public void onSuccess() {

                }

                @Override
                public void dealFail(ErrorReason reason) {

                }
            };
        }
        Observable<BaseResponse> simpleResponseObservable = RetrofitManager.getInstance().createApi(BaseApiService.class).executePost(url, params);
        requestSimple(simpleResponseObservable, callback);
    }

    public static void requestSimpleGet(String url, Map<String, Object> params, SimpleRequestCallback callback) {
        if(params == null){
            params = new HashMap<>();
        }
        Observable<BaseResponse> simpleResponseObservable = RetrofitManager.getInstance().createApi(BaseApiService.class).executeGet(url, params);
        requestSimple(simpleResponseObservable, callback);
    }

    public static void requestSimpleGet(String url, Map<String, Object> params, SimpleRequestCallback callback,Boolean autoErrorToast) {
        if(params == null){
            params = new HashMap<>();
        }
        if (callback == null){
            callback = new SimpleRequestCallback(autoErrorToast) {
                @Override
                public void onSuccess() {

                }

                @Override
                public void dealFail(ErrorReason reason) {

                }
            };
        }
        Observable<BaseResponse> simpleResponseObservable = RetrofitManager.getInstance().createApi(BaseApiService.class).executeGet(url, params);
        requestSimple(simpleResponseObservable, callback);
    }

    public static void requestSimple(Observable<BaseResponse> observable, SimpleRequestCallback callback) {
        observable.compose(new SimpleResponseTransformer())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SimpleBaseObserver(callback));

    }


    public static <T> void execute(Observable<BaseResponse<T>> observable, BaseRequestCallback<T> callback) {
        observable.compose(new ResponseTransformer<>())
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .doOnNext(new BaseObserver.ChildThirdConsumer<>(callback))
                .doOnError(new BaseObserver.ChildThirdErrorConsumer(callback))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new BaseObserver<>(callback));
    }

    public static <T> void executeJsonPost(String url, Map<String, Object> params, BaseRequestCallback<T> callback) {
        Observable<BaseResponse<JsonObject>> observable = RetrofitManager.getInstance().createApi(BaseApiService.class).executeJsonPost(url, params);
        executeJson(observable, callback);
    }

    public static <T> void executeJsonGet(String url, Map<String, Object> params, BaseRequestCallback<T> callback) {
        Observable<BaseResponse<JsonObject>> observable = RetrofitManager.getInstance().createApi(BaseApiService.class).executeJsonGet(url, params);
        executeJson(observable, callback);
    }

    public static <T> void executeJson(Observable<BaseResponse<JsonObject>> observable, BaseRequestCallback<T> callback) {
        observable
                .compose(new JsonResponseTransformer())
                .map(new Function<JsonObject, T>() {
                    @Override
                    public T apply(JsonObject result) throws Throwable {
                        if (callback != null) {
                            return callback.parseResponse(result);
                        } else {
                            CrashReport.postCatchedException(new Throwable("executeJson方法 callback 不允许为空！"));
                        }
                        return null;
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .doOnNext(new BaseObserver.ChildThirdConsumer<>(callback))
                .doOnError(new BaseObserver.ChildThirdErrorConsumer(callback))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new BaseObserver<>(callback));
    }

    public static void downLoadFile(String url, DownLoadListener<File> loadListener) {
        downLoadFile(url,getDefaultDownLoadPath(),loadListener);
    }

    public static void downLoadFile(String url,String path, DownLoadListener<File> loadListener) {
        if (TextUtils.isEmpty(url)) {
            TLog.info("downLoadFile", "%s:下载失败:url为空", url);
            if (loadListener != null) {
                loadListener.onFail(new Throwable("url为空"));
            }
            return;
        }
        final String filePath = path;
        File file = FileUtils.getFile(url, filePath);
        if (file.exists()) {
            if (loadListener != null) {
                loadListener.onProgress(100);
                loadListener.onSuccess(file);
            }
        } else {
            LoadOnSubscribe loadOnSubscribe = new LoadOnSubscribe();
            Observable<File> map = RetrofitManager.getInstance().createUploadOrDownloadApi(DownLoadService.class).downLoadFile(url)
                    .map(new Function<ResponseBody, File>() {

                        @Override
                        public File apply(@NonNull ResponseBody responseBody) throws Throwable {
                            File saveFile = FileResponseBodyConverter.saveFile(loadOnSubscribe, responseBody, url, filePath);
                            if (saveFile == null) {
                                throw new Throwable("下载失败,文件为空");
                            }
                            return saveFile;
                        }
                    });

            Observable.merge(Observable.create(loadOnSubscribe), map)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new loadCallback<Object>() {

                        @Override
                        public void onSubscribe(@NonNull Disposable d) {
                            if (loadListener != null) {
                                loadListener.onStart(d);
                            }
                        }

                        @Override
                        protected void onProgress(int percent) {
                            if (loadListener != null) {
                                loadListener.onProgress(percent);
                            }
                        }

                        @Override
                        protected void onSuccess(Object o) {
                            TLog.info("downLoadFile", "%s:下载成功=%s", url, ((File) o).getAbsolutePath());
                            if (loadListener != null) {
                                loadListener.onSuccess((File) o);
                            }
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            super.onError(e);
                            TLog.info("downLoadFile", "%s:下载失败=%s", url, e.getMessage());
                            if (loadListener != null) {
                                loadListener.onFail(e);
                            }
                        }
                    });
        }
    }

    /**
     * 获得下载保存默认地址
     */
    public static String getDefaultDownLoadPath() {
        if (defaultDownLoadPath != null) {
            return defaultDownLoadPath;
        }
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath() + File.separator;
    }


    /**
     * 上传
     */
    public static void uploadFileForUri(String url, Map<String, String> params, UploadParamForUri uploadParam, UploadRequestCallback<JsonObject> callback) {
        //进度Observable
        UploadOnSubscribe uploadOnSubscribe = new UploadOnSubscribe();
        Observable<Object> progressObservable = Observable.create(uploadOnSubscribe);
        // 组装请求
        UploadRequestBodyForUri uploadRequestBody = new UploadRequestBodyForUri(uploadParam.getFileUri(), uploadParam.getFileSize());
        uploadOnSubscribe.addSumLength(uploadParam.getFileSize());
        uploadRequestBody.setUploadOnSubscribe(uploadOnSubscribe);
        // 发起请求
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);//表单类型
        builder.addFormDataPart(uploadParam.getName(), uploadParam.getFileName(), uploadRequestBody);
        if (params != null && params.size() > 0) {
            for (String key : params.keySet()) {
                String value = params.get(key);
                if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {
                    builder.addPart(Headers.of("Content-Disposition", "form-data; name=\"" + key + "\""),
                            RequestBody.create(null, value));
                }
            }
        }
        List<MultipartBody.Part> parts = builder.build().parts();
        Observable<BaseResponse<JsonObject>> uploadObservable = RetrofitManager.getInstance()
                .createUploadOrDownloadApi(UploadService.class)
                .uploadFile(url, parts);
        Observable<JsonObject> observable = uploadObservable.compose(new ResponseTransformer<>())
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .doOnNext(new BaseObserver.ChildThirdConsumer<JsonObject>(callback))
                .doOnError(new BaseObserver.ChildThirdErrorConsumer(callback));

        Observable.merge(progressObservable, observable)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new UploadObserver(callback));

    }


    /**
     * 上传
     */
    public static void uploadFile(String url, Map<String, String> params, UploadParam uploadParam, UploadRequestCallback<JsonObject> callback) {
        //进度Observable
        UploadOnSubscribe uploadOnSubscribe = new UploadOnSubscribe();
        Observable<Object> progressObservable = Observable.create(uploadOnSubscribe);
        // 组装请求
        UploadRequestBody uploadRequestBody = new UploadRequestBody(uploadParam.getFile());
        uploadOnSubscribe.addSumLength(uploadParam.getFile().length());
        uploadRequestBody.setUploadOnSubscribe(uploadOnSubscribe);
        MultipartBody.Part part = MultipartBody.Part.createFormData(uploadParam.getName(), uploadParam.getFileName(), uploadRequestBody);
        // 发起请求
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);//表单类型
        builder.addFormDataPart(uploadParam.getName(), uploadParam.getFileName(), uploadRequestBody);
        if (params != null && params.size() > 0) {
            for (String key : params.keySet()) {
                String value = params.get(key);
                if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {
                    builder.addPart(Headers.of("Content-Disposition", "form-data; name=\"" + key + "\""),
                            RequestBody.create(null, value));
                }
            }
        }
        List<MultipartBody.Part> parts = builder.build().parts();
        Observable<BaseResponse<JsonObject>> uploadObservable = RetrofitManager.getInstance()
                .createUploadOrDownloadApi(UploadService.class)
                .uploadFile(url, parts);
        Observable<JsonObject> observable = uploadObservable.compose(new ResponseTransformer<>())
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .doOnNext(new BaseObserver.ChildThirdConsumer<JsonObject>(callback))
                .doOnError(new BaseObserver.ChildThirdErrorConsumer(callback));

        Observable.merge(progressObservable, observable)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new UploadObserver(callback));

    }

    /**
     * 上传
     */
    public static void uploadFiles(String url, List<UploadParam> params, UploadRequestCallback<JsonObject> callback) {
        //进度Observable
        UploadOnSubscribe uploadOnSubscribe = new UploadOnSubscribe();
        Observable<Object> progressObservable = Observable.create(uploadOnSubscribe);
        // 组装请求
        List<MultipartBody.Part> parts = new ArrayList<>(params.size());
        for (UploadParam param : params) {
            switch (param.getType()) {
                case UploadParam.TYPE_STRING:
                    parts.add(MultipartBody.Part.createFormData(param.getName(), param.getValue()));
                    break;
                case UploadParam.TYPE_FILE:
                    if (param.getFile() == null || !param.getFile().exists()) {
                        break;
                    }
                    UploadRequestBody uploadRequestBody = new UploadRequestBody(param.getFile());
                    // 设置总长度
                    uploadOnSubscribe.addSumLength(param.getFile().length());
                    uploadRequestBody.setUploadOnSubscribe(uploadOnSubscribe);
                    parts.add(MultipartBody.Part.createFormData(param.getName(), param.getFileName(), uploadRequestBody));
                    break;
                default:
                    break;
            }
        }

        // 发起请求
        Observable<BaseResponse<JsonObject>> uploadObservable = RetrofitManager.getInstance()
                .createUploadOrDownloadApi(UploadService.class)
                .uploadFiles(url, parts);


        Observable<JsonObject> observable = uploadObservable.compose(new ResponseTransformer<>())
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .doOnNext(new BaseObserver.ChildThirdConsumer<JsonObject>(callback))
                .doOnError(new BaseObserver.ChildThirdErrorConsumer(callback));

        Observable.merge(progressObservable, observable)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new UploadObserver(callback));

    }

    public static void cancelAll() {
        OkHttpClientFactory.getInstance().getGeneralClient().dispatcher().cancelAll();
        OkHttpClientFactory.getInstance().getUploadOrDownloadClient().dispatcher().cancelAll();
    }
}
