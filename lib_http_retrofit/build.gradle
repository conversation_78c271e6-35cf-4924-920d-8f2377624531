plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: "$rootDir/gradle/common_library.gradle"

android {
    defaultConfig {
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.kanzhun.http'
}

dependencies {
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
//    api project(':lib_http')
    implementation deps.androidx.app_compat
    implementation deps.androidx.recyclerview
    implementation deps.support.design
    implementation project(':lib_utils')
    deps.bugly.each { k, v -> api v }
    deps.retrofit.each { k, v -> api v }
    api deps.qbusict
    api deps.okhttp3_log

    api deps.apm.core
    api deps.wmrouter

    debugImplementation "com.github.chuckerteam.chucker:library:4.1.0"
    releaseImplementation "com.github.chuckerteam.chucker:library-no-op:4.1.0"
}