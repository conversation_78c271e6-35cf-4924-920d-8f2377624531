package com.kanzhun.mms.service.process;

import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;

import com.kanzhun.mms.service.AppStatus;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/14.
 */

public class ProcessGuard implements IBinder.DeathRecipient {

    IBinder.DeathRecipient mDeathRecipient;
    private volatile IBinder mBinder;

    public ProcessGuard(IBinder.DeathRecipient deathRecipient){
        this.mDeathRecipient = deathRecipient;
    }

    @Override
    public void binderDied() {
        IBinder lastBinder = mBinder;
        if (lastBinder != null) {
            lastBinder.unlinkToDeath(this, 0);
            mBinder = null;
        }
        if (mDeathRecipient != null) {
            mDeathRecipient.binderDied();
        }
        AppStatus.setForeground(false);
    }

    public void setBinder(IInterface iInterface) {
        IBinder binder = iInterface == null ? null : iInterface.asBinder();
        IBinder lastBinder = mBinder;
        if (lastBinder != binder) {
            if (lastBinder != null) {
                lastBinder.unlinkToDeath(this, 0);
                mBinder = null;
            }
            if (binder != null && binder.isBinderAlive()) {
                try {
                    binder.linkToDeath(this, 0);
                    mBinder = binder;
                } catch (RemoteException e) {
                }
            }
        }
    }

    public boolean isAlive() {
        IBinder binder = mBinder;
        return binder != null && binder.isBinderAlive();
    }
}
