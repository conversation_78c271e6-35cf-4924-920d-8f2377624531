package com.kanzhun.mms.utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/8/29.
 */
public final class BLog {
    private static final String TAG = "MMSService";

    public static final int LEVEL_VERBOSE = 0;
    public static final int LEVEL_DEBUG = 1;
    public static final int LEVEL_INFO = 2;
    public static final int LEVEL_WARNING = 3;
    public static final int LEVEL_ERROR = 4;
    public static final int LEVEL_FATAL = 5;
    public static final int LEVEL_NONE = 6;

    // defaults to LEVEL_NONE
    private static int level = LEVEL_VERBOSE;

    public interface LogImp {

        void logV(String tag, String log);

        void logI(String tag, String log);

        void logD(String tag, String log);

        void logW(String tag, String log);

        void logE(String tag, String log);

        void logF(String tag, String log);

        int getLogLevel();

        void flush();

    }

    private static LogImp debugLog = new LogImp() {

        @Override
        public void logV(String tag, String log) {
            if (level <= LEVEL_VERBOSE) {
                android.util.Log.v(tag, log);
            }
        }

        @Override
        public void logI(String tag, String log) {
            if (level <= LEVEL_INFO) {
                android.util.Log.i(tag, log);
            }
        }

        @Override
        public void logD(String tag, String log) {
            if (level <= LEVEL_DEBUG) {
                android.util.Log.d(tag, log);
            }

        }

        @Override
        public void logW(String tag, String log) {
            if (level <= LEVEL_WARNING) {
                android.util.Log.w(tag, log);
            }

        }

        @Override
        public void logE(String tag, String log) {
            if (level <= LEVEL_ERROR) {
                android.util.Log.e(tag, log);
            }
        }

        @Override
        public void logF(String tag, final String log) {
            if (level > LEVEL_FATAL) {
                return;
            }
            android.util.Log.e(tag, log);
        }

        @Override
        public int getLogLevel() {
            return level;
        }

        @Override
        public void flush() {

        }

    };

    private static LogImp logImp = debugLog;

    public static void setLogImp(LogImp imp) {
        logImp = imp;
    }

    public static LogImp getImpl() {
        return logImp;
    }

    public static int getLogLevel() {
        if (logImp != null) {
            return logImp.getLogLevel();
        }
        return LEVEL_NONE;
    }

    /**
     * use f(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void f(final String tag, final String msg) {
        f(tag, msg, (Object[]) null);
    }

    /**
     * use e(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void e(final String tag, final String msg) {
        e(tag, msg, (Object[]) null);
    }

    /**
     * use w(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void w(final String tag, final String msg) {
        w(tag, msg, (Object[]) null);
    }

    /**
     * use i(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void i(final String tag, final String msg) {
        i(tag, msg, (Object[]) null);
    }

    /**
     * use d(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void d(final String tag, final String msg) {
        d(tag, msg, (Object[]) null);
    }

    /**
     * use v(tag, format, obj) instead
     *
     * @param tag
     * @param msg
     */
    public static void v(final String tag, final String msg) {
        v(tag, msg, (Object[]) null);
    }

    public static void f(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_FATAL) {
            String log = obj == null ? format : String.format(format, obj);
            log = formatTag(tag, log);
            logImp.logF(TAG, log);
        }
    }

    public static void e(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_ERROR) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            logImp.logE(TAG, log);
        }
    }

    public static void w(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_WARNING) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            logImp.logW(TAG, log);
        }
    }

    public static void i(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_INFO) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            logImp.logI(TAG, log);
        }
    }

    public static void d(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_DEBUG) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            logImp.logD(TAG, log);
        }
    }

    public static void v(String tag, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_VERBOSE) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            logImp.logV(TAG, log);
        }
    }

    public static void printErrStackTrace(String tag, Throwable tr, final String format, final Object... obj) {
        if (logImp != null && getLogLevel() <= LEVEL_FATAL) {
            String log = obj == null ? format : String.format(format, obj);
            if (log == null) {
                log = "";
            }
            log = formatTag(tag, log);
            log += "  " + android.util.Log.getStackTraceString(tr);
            logImp.logE(TAG, log);
        }
    }

    private static String formatTag(String tag, String log) {
        StringBuilder stringBuilder = StringUtils.obtainStringBuilder();
        stringBuilder.append("[").append(tag).append("]").append(" ").append(log);
        return stringBuilder.toString();
    }

    public static void flush() {
        if (logImp != null) {
            logImp.flush();
        }
    }
}
