package com.kanzhun.foundation.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.R
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.api.model.CerBean

class ReCertActivity : BaseComposeActivity() {
    companion object {
        fun jump(context: Context) {
            AppUtil.startActivity(context, Intent(context, ReCertActivity::class.java))
        }
    }

    override fun enableSafeDrawingPadding() = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }
    }

    @Composable
    override fun OnSetContent() {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color(0xFFFFFFFF))
        ) {
            ReCertContent(onBack = { AppUtil.finishActivity(this@ReCertActivity) })
        }
    }

    override fun onResume() {
        super.onResume()

        reportPoint("upgradecertification-method-page-expo")

        val viewModel by viewModels<ReCertViewModel>()
        viewModel.getUpgradeCert()
    }
}

@Composable
private fun ReCertContent(viewModel: ReCertViewModel = viewModel(), onBack: () -> Unit = {}) {
    Column(modifier = Modifier.fillMaxSize()) {
        Box(
            modifier = Modifier
                .verticalScroll(state = rememberScrollState())
                .fillMaxWidth()
                .weight(1f)
        ) {
            Image(
                painterResource(R.mipmap.found_ic_header_bg),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1500 / 724f)
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .statusBarsPadding()
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.common_ic_black_back),
                    contentDescription = "Back",
                    modifier = Modifier
                        .padding(start = 16.dp, top = 16.dp)
                        .noRippleClickable { onBack() }
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 20.dp, vertical = 16.dp),
                    horizontalAlignment = Alignment.Start
                ) {
                    Text(
                        text = "账号申诉",
                        style = TextStyle(
                            fontSize = 28.sp,
                            fontFamily = boldFontFamily(),
                            fontWeight = FontWeight(700),
                            color = Color(0xFF191919),
                        ),
                        modifier = Modifier.padding(top = 60.dp)
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "为了保证您的账号安全，请您补充完成以下信息认证，审核通过后将进行账号自动解封。",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF797979),
                        )
                    )

                    Spacer(modifier = Modifier.height(32.dp))

                    val response by viewModel.reCertResponseLiveData.observeAsState()

                    val context = LocalContext.current

                    response?.marriageCert?.let {
                        VerificationItem(
                            title = "婚姻状态认证",
                            cert = it,
                            onClick = {
                                viewModel.clickMarry(context)
                                reportClick("婚姻认证")
                            }
                        )
                    }

                    response?.eduCert?.let {
                        VerificationItem(
                            title = "学历认证",
                            cert = it,
                            onClick = {
                                viewModel.clSchoolApproveClick(context = context)
                                reportClick("学历认证")
                            }
                        )
                    }

                    response?.companyCert?.let {
                        VerificationItem(
                            title = "工作认证", cert = it,
                            onClick = {
                                viewModel.clCompanyApproveClick(
                                    context = context
                                )
                                reportClick("工作认证")
                            }
                        )
                    }

                    response?.houseCert?.let {
                        VerificationItem(
                            title = "房产认证", cert = it,
                            onClick = {
                                viewModel.clHouseApproveClick(
                                    context = context
                                )
                                reportClick("房产认证")
                            }
                        )
                    }

                    response?.carCert?.let {
                        VerificationItem(
                            title = "车产认证", cert = it,
                            onClick = {
                                viewModel.clCarApproveClick(
                                    context = context
                                )
                                reportClick("车产认证")
                            }
                        )
                    }

                    response?.incomeCert?.let {
                        VerificationItem(
                            title = "收入认证",
                            cert = it,
                            onClick = {
                                viewModel.clRevenueClick(context = context)
                                reportClick("收入认证")
                            }
                        )
                    }
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 16.dp)
        ) {
            Text(
                text = "如有任何问题，可发送邮件至官方反馈邮箱：",
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF7F7F7F),
                )
            )

            Text(
                text = "<EMAIL>",
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF191919),
                )
            )
        }
    }
}

private fun reportClick(type: String) {
    reportPoint("upgradecertification-method-page-click") {
        this.type = type
    }
}

@Composable
private fun VerificationItem(title: String, cert: CerBean, onClick: () -> Unit = {}) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(color = Color(0xFFF5F5F5))
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.align(Alignment.CenterStart),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val status = cert.status

            Column(modifier = Modifier.weight(1f)) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = title,
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF292929),
                        ),
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    /*
                        若未提交认证，显示【去认证】按钮
                        若审核中，显示标签「审核中」，不可再进入
                        若审核通过，显示标签「通过」，不可再进入
                        若被审核驳回，显示驳回文案+【去认证】按钮
                     */
                    val certStatus = when (status) {
                        1 -> "审核中"
                        3 -> "通过"
                        else -> ""
                    }

                    if (certStatus.isNotEmpty()) {
                        Text(
                            text = certStatus,
                            style = TextStyle(
                                fontSize = 11.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF005EFF),
                            ),
                            modifier = Modifier
                                .padding(0.5.dp)
                                .background(
                                    color = Color(0x334F90FF),
                                    shape = RoundedCornerShape(size = 4.dp)
                                )
                                .padding(horizontal = 4.dp, vertical = 2.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                val rejectReason = cert.rejectReason
                if (rejectReason?.isNotEmpty() == true) {
                    Row {
                        Image(
                            painter = painterResource(id = R.mipmap.found_ic_warning_reason),
                            contentDescription = "image description",
                            modifier = Modifier
                                .padding(top = 3.dp)
                                .size(12.dp)
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = rejectReason,
                            style = TextStyle(
                                fontSize = 13.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFFF06A3A),
                            )
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.width(8.dp))

            /*
               若未提交认证，显示【去认证】按钮
               若被审核驳回，显示驳回文案+【去认证】按钮
            */
            if (status != 1 && status != 3) {
                Button(
                    onClick = onClick,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF191919)),
                    shape = RoundedCornerShape(32.dp),
                ) {
                    Text(
                        text = "去认证",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFFFFFFF),
                        )
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun VerificationItemPreview() {
    val viewModel: ReCertViewModel = viewModel()
    viewModel.setReCertResponse(reCertResponse = MOCK.apply {
        incomeCert?.status = 1
        carCert?.status = 3
    })
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFFFFFFFF))
    ) {
        ReCertContent()
    }
}