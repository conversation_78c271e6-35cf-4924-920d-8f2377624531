package com.kanzhun.marry.me.info.activity.preview.performance

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.model.PersonalAlbumResponse
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemBean
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemType
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel

/**
 * 活动相册
 */
class UserInfoActivityPicPerformance(val activity: FragmentActivity, val viewModel: MeUserInfoViewModel,val adapter: CommonListAdapter<UserPreviewItemBean>) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        viewModel.previewPersonalAlbumBeanLiveData.observe(activity,object : androidx.lifecycle.Observer<PersonalAlbumResponse> {
            override fun onChanged(value: PersonalAlbumResponse) {
                if(value.photoList?.isNotEmpty() == true){
                    val data:MutableList<UserPreviewItemBean> = adapter.data
                    data.forEachIndexed { index, userPreviewItemBean ->
                        if(userPreviewItemBean.type == UserPreviewItemType.ABOUT_US){
                            if((index + 1) < data.size  && data[index + 1].type != UserPreviewItemType.ACTIVITY_PIC){
                                data.add(index+1,  UserPreviewItemBean(userPreviewItemBean.data, UserPreviewItemType.ACTIVITY_PIC, "看准活动照片"))
                                adapter.notifyItemChanged(index+1)
                            }
                            return
                        }
                    }
                }
            }

        })
    }

}