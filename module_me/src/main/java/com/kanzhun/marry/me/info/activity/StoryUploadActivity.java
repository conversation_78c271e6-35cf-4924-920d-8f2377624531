package com.kanzhun.marry.me.info.activity;

import android.app.Dialog;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.views.ItemTouchChangeListener;
import com.kanzhun.common.views.ItemTouchHelperCallback;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.BackHandlerHelper;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.bean.BaseStoryShowItem;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityStoryUploadBinding;
import com.kanzhun.marry.me.databinding.MeItemPicStoryCertBinding;
import com.kanzhun.marry.me.databinding.MeItemStoryAddBinding;
import com.kanzhun.marry.me.databinding.MeItemVideoStoryCertBinding;
import com.kanzhun.foundation.dialog.StoryGuideDialog;
import com.kanzhun.foundation.bean.ImageReloadBean;
import com.kanzhun.foundation.bean.PicStoryItem;
import com.kanzhun.marry.me.info.bean.VideoStoryItem;
import com.kanzhun.marry.me.info.callback.StoryUploadCallback;
import com.kanzhun.marry.me.info.fragment.StoryListEditFragment;
import com.kanzhun.marry.me.info.viewmodel.StoryUploadViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;

import java.util.ArrayList;
import java.util.List;

@RouterUri(path = MePageRouter.ME_STORY_UPLOAD_ACTIVITY)
public class StoryUploadActivity extends FoundationVMActivity<MeActivityStoryUploadBinding, StoryUploadViewModel> implements StoryUploadCallback {
    public static String TAG = "StoryUploadActivity";
    BaseBinderAdapter adapter;
    ItemTouchHelperCallback itemTouchHelperCallback;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_story_upload;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);

        adapter = new BaseBinderAdapter();
        getDataBinding().rvStory.setLayoutManager(new GridLayoutManager(this, 3));
        getDataBinding().rvStory.setAdapter(adapter);
        adapter.addItemBinder(PicStoryItem.class, new BaseDataBindingItemBinder<PicStoryItem, MeItemPicStoryCertBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_pic_story_cert;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemPicStoryCertBinding> holder, MeItemPicStoryCertBinding binding, PicStoryItem item) {
                binding.setBean(item);
                binding.setCanShowStoryDelete(getViewModel().getCanShowStoryDelete());
                if (item.getFile() != null) {
                    binding.ovStory.loadRoundUri(item.getFile());
                } else {
                    if (!TextUtils.isEmpty(item.getUrl())) {
                        getViewModel().downLoadItemFile(item, binding.ovStory);
                    }
                }
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_delete, R.id.tv_retry, R.id.tv_retry_down);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemPicStoryCertBinding> holder, @NonNull View view, PicStoryItem data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                if (TextUtils.isEmpty(data.getId())) {
                    return;
                }
                if (data.getFile() == null) {
                    return;
                }
                if (!data.getAddFailed().get() && data.getUpLoadPercent().get() < 100) {
                    return;
                }
                if (data.getCertStatus().get() == ProfileMetaModel.STATUS_REJECTED) {
                    reEditRejectedStoryFiles(data);
                    return;
                }
                reEdit(data);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemPicStoryCertBinding> holder, @NonNull View view, PicStoryItem data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                int id = view.getId();
                if (id == R.id.iv_delete) {
                    if (TextUtils.isEmpty(data.getId())) {//未生成故事
                        getViewModel().deleteStoryLocalData(data);
                    } else {
                        getViewModel().deleteStory(data);
                    }
                } else if (id == R.id.tv_retry) {
                    data.setAddFailed(false);
                    if (data.getUpLoadPercent().get() >= 100) {//文件上传成功
                        getViewModel().addPicStory(data);
                    } else {
                        getViewModel().upLoadImage(data);
                    }
                } else if (id == R.id.tv_retry_down) {
                    data.setDownLoadFailed(false);
                    data.setDownLoadPercent(0);
                    getViewModel().downLoadItemFile(data, holder.getDataBinding().ovStory);
                }
            }
        });
        adapter.addItemBinder(VideoStoryItem.class, new BaseDataBindingItemBinder<VideoStoryItem, MeItemVideoStoryCertBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_video_story_cert;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemVideoStoryCertBinding> holder, MeItemVideoStoryCertBinding binding, VideoStoryItem item) {
                binding.setBean(item);
                binding.setCanShowStoryDelete(getViewModel().getCanShowStoryDelete());
                if (item.getFile() != null) {
                    binding.ovStory.loadRoundUri(item.getFile());
                } else {
                    if (!TextUtils.isEmpty(item.getUrl())) {
                        getViewModel().downLoadItemFile(item, binding.ovStory);
                    }
                }
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_delete, R.id.tv_retry, R.id.tv_retry_down);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemVideoStoryCertBinding> holder, @NonNull View view, VideoStoryItem data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                if (TextUtils.isEmpty(data.getId())) {
                    return;
                }
                if (data.getFile() == null) {
                    return;
                }
                if (!data.getAddFailed().get() && data.getUpLoadPercent().get() < 100) {
                    return;
                }
                if (data.getCertStatus().get() == ProfileMetaModel.STATUS_REJECTED) {
                    reEditRejectedStoryFiles(data);
                    return;
                }
                reEdit(data);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemVideoStoryCertBinding> holder, @NonNull View view, VideoStoryItem data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                int id = view.getId();
                if (id == R.id.iv_delete) {
                    if (TextUtils.isEmpty(data.getId())) {//未生成故事
                        getViewModel().deleteStoryLocalData(data);
                    } else {
                        getViewModel().deleteStory(data);
                    }

                } else if (id == R.id.tv_retry) {
                    data.setAddFailed(false);
                    if (data.getUpLoadPercent().get() >= 100) {//文件上传成功
                        getViewModel().addVideoStory(data);
                    } else {
                        getViewModel().upLoadIVideo(data);
                    }
                } else if (id == R.id.tv_retry_down) {
                    data.setDownLoadFailed(false);
                    data.setDownLoadPercent(0);
                    getViewModel().downLoadItemFile(data, holder.getDataBinding().ovStory);
                }
            }
        });
        adapter.addItemBinder(String.class, new BaseDataBindingItemBinder<String, MeItemStoryAddBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_story_add;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemStoryAddBinding> holder, MeItemStoryAddBinding binding, String item) {
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemStoryAddBinding> holder, @NonNull View view, String data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
//                boolean hasShowGuide = SpManager.get().user().getBoolean(Constants.HAS_SHOW_STORY_GUIDE, false);
//                if (hasShowGuide) {
//                    addStory();
//                } else {
//                    SpManager.putUserBoolean(Constants.HAS_SHOW_STORY_GUIDE, true);
                    showStoryGuideDialog();
//                }
            }
        });
        itemTouchHelperCallback = new ItemTouchHelperCallback(adapter, false);
        itemTouchHelperCallback.setLongPressDragEnabled(true);
        itemTouchHelperCallback.setListener(new ItemTouchChangeListener() {
            @Override
            public void touchChanged() {

            }

            @Override
            public void onDragFinish() {
                getViewModel().sortStory(adapter.getData());
            }
        });
        ItemTouchHelper helper = new ItemTouchHelper(itemTouchHelperCallback);
        helper.attachToRecyclerView(getDataBinding().rvStory);
        getViewModel().loadRejectOrCacheStory();
        adapter.addData("");
        getViewModel().getStorySelectLiveData().observe(this, new Observer<List>() {
            @Override
            public void onChanged(List list) {
                List result = new ArrayList();
                boolean hasData = !LList.isEmpty(list);
                getViewModel().getHasStoryData().set(hasData);
                if (hasData) {
                    result.addAll(list);
                    if (result.size() < 6) {
                        result.add("");
                        itemTouchHelperCallback.setEnableLastDrag(false);
                    } else {
                        itemTouchHelperCallback.setEnableLastDrag(true);
                    }
                } else {
                    result.add("");
                    itemTouchHelperCallback.setEnableLastDrag(false);
                }
                adapter.setList(result);
            }
        });
        getViewModel().getGuideClickedLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    addStory();
                }
            }
        });
        getViewModel().getImageReloadLiveData().observe(this, new Observer<ImageReloadBean>() {
            @Override
            public void onChanged(ImageReloadBean imageReloadBean) {
                if (imageReloadBean != null && imageReloadBean.getImageView() != null && imageReloadBean.getFile() != null) {
                    imageReloadBean.getImageView().loadRoundUri(imageReloadBean.getFile());
                }
            }
        });
    }

    private void addStory() {
        List list = adapter.getData();
        int count = 6;
        if (!LList.isEmpty(list)) {
            count = count + 1 - list.size();
        }
        selectStoryFile(count);
    }

    private void reEdit(BaseStoryShowItem baseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.getId()) && !baseStoryShowItem.getAddFailed().get()) {
            getViewModel().setReEditSelectId(baseStoryShowItem.getId());
            ((FragmentActivity) StoryUploadActivity.this).getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                            0,
                            0,
                            R.anim.common_activity_slide_from_left_to_right_exit)
                    .add(R.id.fragment_container, StoryListEditFragment.newInstance(StoryListEditFragment.TYPE_EDIT))
                    .addToBackStack(null)
                    .commitAllowingStateLoss();
        }
    }

    @Override
    public void clickLeft(View view) {
        onBackPressed();
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(StoryUploadActivity.this, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (hasUploading(true)) {
            return;
        }
        if (getViewModel().indexObservable.get() > 0) {
            ProfileHelper.getInstance().checkJump(StoryUploadActivity.this, getViewModel().indexObservable.get() + 1);
        }
    }

    private boolean hasUploading(boolean goNext) {
        List<BaseStoryShowItem> list = getViewModel().getStorySelectLiveData().getValue();
        if (!LList.isEmpty(list)) {
            for (BaseStoryShowItem b : list) {
                if (!b.getAddFailed().get() && b.getUpLoadPercent().get() < 100) {//为上传成功
                    showAlertDialog(goNext);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void onBackPressed() {
        if (BackHandlerHelper.handleBackPress(this)) {
            return;
        }
        if (hasUploading(false)) {
            return;
        }
        ProfileHelper.getInstance().setUploadCache(getViewModel().getStorySelectLiveData().getValue(), getViewModel().getRejected());
        super.onBackPressed();
    }

    private void selectStoryFile(int count) {
        PhotoSelectManager.jumpForGalleryFromResult(this, count, PhotoSelectManager.SELECT_MIME_TYPE_IMAGE, true, new PhotoSelectManager.OnGalleryCountCallBack() {
            @Override
            public void onGalleryListener(List<Uri> files, boolean originalEnable) {
                if (LList.isEmpty(files)) {
                    return;
                }
                getViewModel().setSelectFiles(files);
                ((FragmentActivity) StoryUploadActivity.this).getSupportFragmentManager().beginTransaction()
                        .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                                0,
                                0,
                                R.anim.common_activity_slide_from_left_to_right_exit)
                        .add(R.id.fragment_container, StoryListEditFragment.newInstance(StoryListEditFragment.TYPE_ADD))
                        .addToBackStack(null)
                        .commitAllowingStateLoss();
            }
        });
    }

    private void showAlertDialog(boolean goNext) {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                .setTitle(getString(R.string.me_un_finish_story_upload))
                .setContent(getString(R.string.me_un_save_story_exist))
                .setPositiveText(getResources().getString(R.string.me_waite))
                .setNegativeText(getResources().getString(R.string.me_give_up))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {
                        getViewModel().cancelUploadAddUpdateRequest();
                        if (goNext) {
                            getViewModel().deleteUnAddStoryPost();
                            ProfileHelper.getInstance().checkJump(StoryUploadActivity.this, getViewModel().indexObservable.get() + 1);
                        } else {
                            List<BaseStoryShowItem> result = getViewModel().deleteUnAddStory();
                            ProfileHelper.getInstance().setUploadCache(result, getViewModel().getRejected());
                            AppUtil.finishActivity(StoryUploadActivity.this);
                        }
                    }
                });
        builder.create().show();
    }

    private void reEditRejectedStoryFiles(BaseStoryShowItem baseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.getId()) && !baseStoryShowItem.getAddFailed().get()) {
            int textReject = baseStoryShowItem.getTextReject();
            int photoReject = baseStoryShowItem.getPhotoReject();
            if (baseStoryShowItem.getCertStatus().get() == ProfileMetaModel.STATUS_REJECTED && textReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && photoReject != ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                textReject = !TextUtils.isEmpty(baseStoryShowItem.getStoryText()) ? ProfileInfoModel.CONTENT_TYPE_REJECTED : ProfileInfoModel.CONTENT_TYPE_UN_REJECTED;
                photoReject = ProfileInfoModel.CONTENT_TYPE_REJECTED;
            }
            getViewModel().setReEditSelectId(baseStoryShowItem.getId());
            getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                            0,
                            0,
                            R.anim.common_activity_slide_from_left_to_right_exit)
                    .add(R.id.fragment_container, StoryListEditFragment.newInstance(StoryListEditFragment.TYPE__REJECTED_EDIT, textReject, photoReject,baseStoryShowItem.getCertInfo()))
                    .addToBackStack(null)
                    .commitAllowingStateLoss();
        }
    }

    private void showStoryGuideDialog() {
        StoryGuideDialog.Builder builder = new StoryGuideDialog.Builder(this)
                .setPadding(0, 0)
                .add(R.id.btn_finish)
                .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
                    @Override
                    public void onItemClick(Dialog dialog, View view) {
                        getViewModel().postGuideClick(true);
                        dialog.dismiss();
                    }
                });
        builder.create().show();
    }
}