package com.kanzhun.marry.me.info.fragment;

import static android.app.Activity.RESULT_OK;
import static android.content.Context.VIBRATOR_SERVICE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.common.AvoidOnResult;
import com.kanzhun.foundation.utils.PathUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.animator.Techniques;
import com.kanzhun.common.animator.YoYo;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;
import com.kanzhun.common.dialog.BottomListDialog;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentAbImpressionUploadBinding;
import com.kanzhun.marry.me.info.callback.ABImpressionUploadCallback;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionUploadViewModel;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionViewModel;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.yalantis.ucrop.UCrop;
import com.zhihu.matisse.Matisse;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class ABImpressionUploadFragment extends FoundationVMShareFragment<MeFragmentAbImpressionUploadBinding, ABImpressionUploadViewModel, ABImpressionViewModel> implements ABImpressionUploadCallback, BottomListDialog.OnBottomItemClickListener {
    public static final String TAG = "ABImpressionUploadFragment";

    private BottomListDialog mDialog;
    private View.OnTouchListener mOnTouchListener;
    private View.OnLongClickListener mOnLongClickListener;
    boolean drag = false;
    int lastX;
    int lastY;
    int left;
    int top;
    int right;
    int bottom;

    //初始坐标
    int initALeft;
    int initATop;
    int initARight;
    int initABottom;
    int initBLeft;
    int initBTop;
    int initBRight;
    int initBBottom;
    private AtomicBoolean exChanging = new AtomicBoolean(false);
    int translationZ;
    private Vibrator vibrator;

    public static Fragment getInstance() {
        ABImpressionUploadFragment fragment = new ABImpressionUploadFragment();
        return fragment;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_ab_impression_upload;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(activity);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().abUpload.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        translationZ = QMUIDisplayHelper.dp2px(activity, 7);
        getViewModel().setContentRejected(getActivityViewModel().getFaceAPictureReject().get(),
                getActivityViewModel().getFaceATitleReject().get(),
                getActivityViewModel().getFaceBPictureReject().get(),
                getActivityViewModel().getFaceBTitleReject().get(),getActivityViewModel().getCertInfo());
        getActivityViewModel().getUriData().observe(this, new Observer<Pair<String, Uri>>() {
            @Override
            public void onChanged(Pair<String, Uri> pair) {
                startEditFragment(pair.first, pair.second);
            }
        });

        getDataBinding().llImpressionA.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (getDataBinding().llImpressionA.getMeasuredHeight() > 0) {
                    getDataBinding().llImpressionA.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    initALeft = getDataBinding().llImpressionA.getLeft();
                    initATop = getDataBinding().llImpressionA.getTop();
                    initARight = getDataBinding().llImpressionA.getRight();
                    initABottom = getDataBinding().llImpressionA.getBottom();
                    initBLeft = getDataBinding().llImpressionB.getLeft();
                    initBTop = getDataBinding().llImpressionB.getTop();
                    initBRight = getDataBinding().llImpressionB.getRight();
                    initBBottom = getDataBinding().llImpressionB.getBottom();
                    getDataBinding().llContainer.setInitLayout(initALeft, initATop, initARight, initABottom, initBLeft, initBTop, initBRight, initBBottom);
                }
            }
        });

        vibrator = (Vibrator) activity.getSystemService(VIBRATOR_SERVICE);

        initLongClickListener();
        getDataBinding().llImpressionA.setOnLongClickListener(mOnLongClickListener);
        getDataBinding().llImpressionB.setOnLongClickListener(mOnLongClickListener);

        initTouchListener();
        getDataBinding().llImpressionA.setOnTouchListener(mOnTouchListener);
        getDataBinding().llImpressionB.setOnTouchListener(mOnTouchListener);

        getActivityViewModel().hasPic().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String side) {
                if (!TextUtils.isEmpty(side)) {
                    switch (side) {
                        case ABImpressionViewModel.A_SIDE:
                            getDataBinding().llContainer.setA();
                            break;
                        case ABImpressionViewModel.B_SIDE:
                            getDataBinding().llContainer.setB();
                            break;
                    }
                }
            }
        });
    }

    public int getInitLeft(String side) {
        if (!getViewModel().isExChange()) {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initALeft;
            } else {
                return initBLeft;
            }
        } else {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initBLeft;
            } else {
                return initALeft;
            }
        }
    }

    public int getInitTop(String side) {
        if (!getViewModel().isExChange()) {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initATop;
            } else {
                return initBTop;
            }
        } else {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initBTop;
            } else {
                return initATop;
            }
        }
    }

    public int getInitRight(String side) {
        if (!getViewModel().isExChange()) {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initARight;
            } else {
                return initBRight;
            }
        } else {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initBRight;
            } else {
                return initARight;
            }
        }
    }

    public int getInitBottom(String side) {
        if (!getViewModel().isExChange()) {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initABottom;
            } else {
                return initBBottom;
            }
        } else {
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                return initBBottom;
            } else {
                return initABottom;
            }
        }
    }

    @Override
    public void save() {
        getActivityViewModel().saveImpression(getViewModel().isExChange());
    }

    @Override
    public void addA() {
        try {
            getViewModel().setSide(ABImpressionViewModel.A_SIDE);
            if (TextUtils.isEmpty(getActivityViewModel().getPathA().get()) && getActivityViewModel().getInitialUriA() == null) {
                showSelectDialog();
            } else {
                addImage();
            }
        } catch (Exception e) {
            T.ss(e.toString());
        }
    }

    @Override
    public void addB() {
        getViewModel().setSide(ABImpressionViewModel.B_SIDE);
        if (TextUtils.isEmpty(getActivityViewModel().getPathB().get()) && getActivityViewModel().getInitialUriB() == null) {
            showSelectDialog();
        } else {
            addImage();
        }
    }

    private void addImage() {
        boolean isSideA = TextUtils.equals(ABImpressionViewModel.A_SIDE, getViewModel().getSide());
        String url;
        Uri initialUri;
        if (isSideA) {
            url = getActivityViewModel().getPathA().get();
            initialUri = getActivityViewModel().getInitialUriA();
        } else {
            url = getActivityViewModel().getPathB().get();
            initialUri = getActivityViewModel().getInitialUriB();
        }
        if (initialUri != null) {
            startEditFragment(getViewModel().getSide(), initialUri);
        } else if (!TextUtils.isEmpty(url)) {
            getActivityViewModel().downloadBitmap(getViewModel().getSide(), url);
        }
    }

    @Override
    public void delete() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(activity)
                .setTitle("确定要删除该内容吗?删除后将无法恢复")
                .setPositiveIsRed(true)
                .setPositiveText(getResources().getString(R.string.common_delete))
                .setNegativeText(getResources().getString(R.string.common_cancel))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                        getActivityViewModel().delete();
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {

                    }
                });
        builder.create().show();
    }

    private void jumpGallery() {
        PhotoSelectManager.jumpForGalleryOnlyImageToCropResult((FragmentActivity) activity, 1, new AvoidOnResult.Callback() {
            @Override
            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                if (resultCode == RESULT_OK && data != null) {
                    List<Uri> result = Matisse.obtainResult(data);
                    if (LList.getCount(result) == 1) {
                        Uri source = result.get(0);
                        setUri(source);
                    }
                }
            }
        });
    }

    private void jumpCamera() {
        PhotoSelectManager.jumpForOnlyCameraResult((FragmentActivity) activity, new PhotoSelectManager.OnCameraUriCallBack() {
            @Override
            public void onCameraCallback(Uri cameraFileUri) {
                setUri(cameraFileUri);
            }
        });
    }

    private void setUri(Uri uri) {
        if (uri != null) {
//            switch (getViewModel().getSide()) {
//                case ABImpressionViewModel.A_SIDE:
//                    getActivityViewModel().setInitialUriA(uri);
//                    getActivityViewModel().setPathA(uri.toString());
//                    break;
//                case ABImpressionViewModel.B_SIDE:
//                    getActivityViewModel().setInitialUriB(uri);
//                    getActivityViewModel().setPathB(uri.toString());
//                    break;
//            }
            startEditFragment(getViewModel().getSide(), uri);
        }
    }

    private void startEditFragment(String side, Uri uri) {
        Bundle cropOptionsBundle = new Bundle();
//        Uri destination = Uri.fromFile(getActivityViewModel().getPictureOutputFile(side + System.currentTimeMillis()));
        Uri destination = Uri.fromFile(PathUtil.getPictureOutputFile(activity, uri, side + System.currentTimeMillis()));
        cropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, uri);
        cropOptionsBundle.putParcelable(UCrop.EXTRA_OUTPUT_URI, destination);
        cropOptionsBundle.putString(BundleConstants.BUNDLE_A_B_IMPRESSION_SIDE, side);
        ((FragmentActivity) activity).getSupportFragmentManager().beginTransaction()
                .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                        0,
                        0,
                        R.anim.common_activity_slide_from_left_to_right_exit)
                .add(R.id.fragment_container, ABImpressionEditFragment.getInstance(cropOptionsBundle))
                .addToBackStack(null)
                .commitAllowingStateLoss();
    }

    private void showSelectDialog() {
        if (mDialog == null) {
            List<SelectBottomBean> list = new ArrayList();
            list.add(new SelectBottomBean(Constants.CAPTURE_PHOTO, activity.getResources().getString(R.string.common_photo), R.drawable.common_ic_photo));
            list.add(new SelectBottomBean(Constants.SELECT_ALBUM, activity.getResources().getString(R.string.common_album), R.drawable.common_ic_album));
            mDialog = new BottomListDialog.Builder(activity)
                    .setItemLayout(R.layout.common_item_bottom_select_dialog)
                    .setData(list)
                    .setPadding(0, 0)
                    .setSpanCount(2)
                    .setCanceledOnTouchOutside(true)
                    .setOnBottomItemClickListener(this)
                    .create();
        }
        mDialog.show();
    }

    @Override
    public void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean) {
        switch (bottomBean.type) {
            case Constants.CAPTURE_PHOTO:
                jumpCamera();
                break;
            case Constants.SELECT_ALBUM:
                jumpGallery();
                break;
        }
    }

    private View getDraggingView(String side) {
        if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
            return getDataBinding().llImpressionA;
        } else {
            return getDataBinding().llImpressionB;
        }
    }

    private View[] getViewSubsidiary(String side) {
        View[] views = new View[3];
        if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
            views[0] = getDataBinding().ivWordA;
            views[1] = getDataBinding().tvContentA;
            views[2] = getDataBinding().ivAddA;
        } else {
            views[0] = getDataBinding().ivWordB;
            views[1] = getDataBinding().tvContentB;
            views[2] = getDataBinding().ivAddB;
        }
        return views;
    }

    private View getOtherView(String side) {
        if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
            return getDataBinding().llImpressionB;
        } else {
            return getDataBinding().llImpressionA;
        }
    }

    private String getOtherSide(String side) {
        if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
            return ABImpressionViewModel.B_SIDE;
        } else {
            return ABImpressionViewModel.A_SIDE;
        }
    }

    private boolean getExchangeStatusUpToStandard(String draggingSide, int vLeft, int vTop, int width, int height) {
        if (!getViewModel().isExChange()) {
            if (TextUtils.equals(draggingSide, ABImpressionViewModel.A_SIDE)) {
                if (vLeft > getInitLeft(getOtherSide(draggingSide)) - width &&
                        vTop > getInitTop(getOtherSide(draggingSide)) - height) {
                    return true;
                }
            } else {
                if (vLeft < getInitLeft(getOtherSide(draggingSide)) + width &&
                        vTop < getInitTop(getOtherSide(draggingSide)) + height) {
                    return true;
                }
            }
        } else {
            if (TextUtils.equals(draggingSide, ABImpressionViewModel.A_SIDE)) {
                if (vLeft < getInitLeft(getOtherSide(draggingSide)) + width &&
                        vTop < getInitTop(getOtherSide(draggingSide)) + height) {
                    return true;
                }
            } else {
                if (vLeft > getInitLeft(getOtherSide(draggingSide)) - width &&
                        vTop > getInitTop(getOtherSide(draggingSide)) - height) {
                    return true;
                }
            }
        }
        return false;
    }

    private void exChangeIcon(String side) {
        if (!getViewModel().isExChange()) {
            getDataBinding().ivWordA.setImageResource(R.mipmap.me_icon_ab_word_b);
            getDataBinding().ivWordB.setImageResource(R.mipmap.me_icon_ab_word_a);
            getDataBinding().llImpressionA.setBackgroundColor(getResources().getColor(R.color.common_color_7F7F7F, null));
            getDataBinding().llImpressionB.setBackgroundColor(getResources().getColor(R.color.common_color_CCCCCC, null));
        } else {
            getDataBinding().ivWordA.setImageResource(R.mipmap.me_icon_ab_word_a);
            getDataBinding().ivWordB.setImageResource(R.mipmap.me_icon_ab_word_b);
            getDataBinding().llImpressionA.setBackgroundColor(getResources().getColor(R.color.common_color_CCCCCC, null));
            getDataBinding().llImpressionB.setBackgroundColor(getResources().getColor(R.color.common_color_7F7F7F, null));
        }
    }

    private void exChangeView(String side) {
        if (exChanging.compareAndSet(false, true)) {
            boolean gone = false;
            if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                if (TextUtils.isEmpty(getActivityViewModel().getPathB().get()) &&
                        getActivityViewModel().getInitialUriB() == null) {
                    gone = true;
                }
            } else {
                if (TextUtils.isEmpty(getActivityViewModel().getPathA().get()) &&
                        getActivityViewModel().getInitialUriA() == null) {
                    gone = true;
                }
            }
            ValueAnimator valueAnimator = new ValueAnimator();
            valueAnimator.setFloatValues(0, 1);
            valueAnimator.setDuration(300);
            if (!gone) {
                int lengthX = getInitLeft(side) - getInitLeft(getOtherSide(side));
                int lengthY = getInitTop(side) - getInitTop(getOtherSide(side));
                valueAnimator.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
                valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        float value = (float) animation.getAnimatedValue();
                        int durationLengthX = (int) (lengthX * value);
                        int durationLengthY = (int) (lengthY * value);
                        int l = getInitLeft(getOtherSide(side)) + durationLengthX;
                        int t = getInitTop(getOtherSide(side)) + durationLengthY;
                        int r = getInitRight(getOtherSide(side)) + durationLengthX;
                        int b = getInitBottom(getOtherSide(side)) + durationLengthY;
                        getOtherView(side).layout(l, t, r, b);
                        if (value >= 1) {
                            getViewModel().setExChange(!getViewModel().isExChange());
                            getDataBinding().llContainer.setRefresh(getViewModel().isExChange());
                            exChanging.set(false);
                        }
                    }
                });
                valueAnimator.start();
                View[] views = getViewSubsidiary(getOtherSide(side));
                for (int i = 0; i < views.length; i++) {
                    View view = views[i];
                    int finalI = i;
                    YoYo.with(Techniques.AlphaOut).duration(150).withListener(new AnimatorListenerAdapter() {
                                @Override
                                public void onAnimationEnd(Animator animation) {
                                    super.onAnimationEnd(animation);
                                    if (finalI == 0) {
                                        exChangeIcon(side);
                                    }
                                }
                            }).interpolate(InterpolatorUtil.createDefaultBezierInterpolator())
                            .playOn(view);
                }
                for (int i = 0; i < views.length; i++) {
                    View view = views[i];
                    YoYo.with(Techniques.AlphaIn).duration(150).delay(150).interpolate(InterpolatorUtil.createDefaultBezierInterpolator())
                            .playOn(view);
                }
            } else {
                getOtherView(side).layout(getInitLeft(side), getInitTop(side), getInitRight(side), getInitBottom(side));
                exChangeIcon(side);
                getViewModel().setExChange(!getViewModel().isExChange());
                getDataBinding().llContainer.setRefresh(getViewModel().isExChange());
                exChanging.set(false);
            }
        }
    }

    private void homingAnimation(View v, String side) {
        left = v.getLeft();
        top = v.getTop();
        right = v.getRight();
        bottom = v.getBottom();
        ValueAnimator valueAnimator = new ValueAnimator();
        valueAnimator.setFloatValues(0, 1);
        valueAnimator.setDuration(300);
        valueAnimator.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                int lengthX = getInitLeft(side) - left;
                int lengthY = getInitTop(side) - top;
                int durationLengthX = (int) (lengthX * value);
                int durationLengthY = (int) (lengthY * value);
                int l = left + durationLengthX;
                int t = top + durationLengthY;
                int r = right + durationLengthX;
                int b = bottom + durationLengthY;
                v.layout(l, t, r, b);
                v.setTranslationZ((1.f - value) * translationZ);
                if (value >= 1) {
                    View frontView;
                    View backView;
                    if (!getViewModel().isExChange()) {
                        frontView = getDraggingView(ABImpressionViewModel.A_SIDE);
                        backView = getDraggingView(ABImpressionViewModel.B_SIDE);
                    } else {
                        frontView = getDraggingView(ABImpressionViewModel.B_SIDE);
                        backView = getDraggingView(ABImpressionViewModel.A_SIDE);
                    }
                    frontView.setTranslationZ(0.002f);
                    backView.setTranslationZ(0.001f);
                }
            }
        });
        valueAnimator.start();
        for (View view : getViewSubsidiary(side)) {
            YoYo.with(Techniques.AlphaIn).duration(200).interpolate(InterpolatorUtil.createDefaultBezierInterpolator())
                    .playOn(view);
        }
    }

    private void initTouchListener() {
        mOnTouchListener = new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (getActivityViewModel().hasRejected()) {
                    return false;
                }
                if (drag) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_MOVE:
                            if (lastX == 0) {
                                lastX = (int) (event.getRawX());
                                lastY = (int) (event.getRawY());
                            }
                            int dx = (int) (event.getRawX() - lastX);
                            int dy = (int) (event.getRawY() - lastY);
                            if (dx != 0 || dy != 0) {
                                int l = left + dx;
                                int r = right + dx;
                                int t = top + dy;
                                int b = bottom + dy;
                                v.layout(l, t, r, b);
                                String side = (String) v.getTag();
                                if (getExchangeStatusUpToStandard(side, v.getLeft(), v.getTop(), v.getMeasuredWidth() / 3, v.getMeasuredHeight() / 3)) {
                                    exChangeView(side);
                                }
                            }
                            break;
                        case MotionEvent.ACTION_UP:
                            if (drag) {
                                drag = false;
                                lastX = 0;
                                String side = (String) v.getTag();
                                homingAnimation(v, side);
                                getDataBinding().llScrollView.setScrollEnable(true);
                            }
                            break;
                    }
                }
                return false;
            }
        };
    }

    private void initLongClickListener() {
        mOnLongClickListener = new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if (getActivityViewModel().hasRejected()) {
                    return true;
                }
                String side = (String) v.getTag();
                if (TextUtils.equals(side, ABImpressionViewModel.A_SIDE)) {
                    if (TextUtils.isEmpty(getActivityViewModel().getPathA().get()) &&
                            getActivityViewModel().getInitialUriA() == null) {
                        return true;
                    }
                } else {
                    if (TextUtils.isEmpty(getActivityViewModel().getPathB().get()) &&
                            getActivityViewModel().getInitialUriB() == null) {
                        return true;
                    }
                }
                getDataBinding().llScrollView.setScrollEnable(false);
                vibrator.vibrate(80);
                ValueAnimator valueAnimator = new ValueAnimator();
                valueAnimator.setFloatValues(0, 1);
                valueAnimator.setDuration(300);
                valueAnimator.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
                valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        float value = (float) animation.getAnimatedValue();
                        int length = -10;
                        int durationLength = (int) (length * value);
                        int l = getInitLeft(side) + durationLength;
                        int t = getInitTop(side) + durationLength;
                        int r = getInitRight(side) + durationLength;
                        int b = getInitBottom(side) + durationLength;
                        v.layout(l, t, r, b);
                        v.setTranslationZ(value * translationZ);
                        if (value >= 1) {
                            left = v.getLeft();
                            top = v.getTop();
                            right = v.getRight();
                            bottom = v.getBottom();
                            drag = true;
                        }
                    }
                });
                valueAnimator.start();
                for (View view : getViewSubsidiary(side)) {
                    YoYo.with(Techniques.AlphaOut).duration(200).interpolate(InterpolatorUtil.createDefaultBezierInterpolator())
                            .playOn(view);
                }
                return true;
            }
        };
    }
}