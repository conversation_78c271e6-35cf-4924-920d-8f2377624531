package com.kanzhun.marry.me.info.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.common.views.wheel.pick.LinkagePickerOption;
import com.kanzhun.common.views.wheel.pick.listener.OnOptionsSelectChangeListener;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.AreaBean;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityResidenceEditBinding;
import com.kanzhun.marry.me.info.callback.MeResidenceEditCallback;
import com.kanzhun.marry.me.info.viewmodel.MeResidenceEditViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.views.MultiClickUtil;

import io.reactivex.rxjava3.core.Observable;

/**
 * 我的基本信息 - 户口所在地
 *
 * Created by Qu Zhiyong on 2022/4/21
 */
@RouterUri(path = MePageRouter.ME_RESIDENCE_ACTIVITY)
public class MeResidenceEditActivity extends FoundationVMActivity<MeActivityResidenceEditBinding, MeResidenceEditViewModel> implements MeResidenceEditCallback {
    private int hide;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_residence_edit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        hide = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);
        getViewModel().initData();

        getViewModel().getAreaLivaData().observe(this, areaBeans -> {
            getViewModel().areaOptions1Items = areaBeans;
            getViewModel().areaOptions2Items.clear();
            if (!LList.isEmpty(getViewModel().areaOptions1Items)) {
                for (int i = 0; i < getViewModel().areaOptions1Items.size(); i++) {
                    getViewModel().areaOptions2Items.add(getViewModel().areaOptions1Items.get(i).getChildren());
                }
                initPick();
            }
        });

        getViewModel().getUpdateSuccessLivaData().observe(this, code -> {
            if (getViewModel().indexObservable.get() > 0) {
                ProfileHelper.getInstance().checkJump(MeResidenceEditActivity.this, getViewModel().indexObservable.get() + 1);
            } else {
                setResult(RESULT_OK);
                AppUtil.finishActivity(MeResidenceEditActivity.this);
            }
        });

        getDataBinding().checkLink.setChecked(hide == 1);
        getDataBinding().checkLink.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDataBinding().checkLink.setChecked(getDataBinding().checkLink.isChecked());
            }
        });
    }

    public void requestHide(boolean isCheck,Runnable runnable) {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).settingUpdate(UserSettingConfig.PERSONALITY_HIDE_LOCAL, isCheck ? 1 : 0);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                LiveEventBus.post(LivedataKeyMe.USER_TAG_HIDE_COMPANY, isCheck);
                if (runnable != null)runnable.run();
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }


    private void initPick() {
        if (LList.isEmpty(getViewModel().areaOptions1Items) || LList.isEmpty(getViewModel().areaOptions2Items)) {
            getViewModel().getEnableSubmit().set(false);
            return;
        }
        getViewModel().getEnableSubmit().set(true);
        LinkagePickerOption<AreaBean> linkagePickerOption = new LinkagePickerOption<>();
        linkagePickerOption.mOptions1Items = getViewModel().areaOptions1Items;
        linkagePickerOption.mOptions2Items = getViewModel().areaOptions2Items;
        linkagePickerOption.optionsSelectChangeListener = new OnOptionsSelectChangeListener() {
            @Override
            public void onOptionsSelectChanged(int options1, int options2, int options3) {
                getViewModel().areaOptions1position = options1;
                getViewModel().areaOptions2position = options2;
            }
        };

        getViewModel().findWheelPosition();
        linkagePickerOption.options1 = getViewModel().areaOptions1position;
        linkagePickerOption.options2 = getViewModel().areaOptions2position;
        getDataBinding().pickView.setPickerOption(linkagePickerOption);
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()){
            return;
        }
        ProfileHelper.getInstance().checkJump(MeResidenceEditActivity.this, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()){
            return;
        }
        if(hide != (getDataBinding().checkLink.isChecked()? 1:0)){
            requestHide(getDataBinding().checkLink.isChecked(), new Runnable() {
                @Override
                public void run() {
                    doCommit();
                }
            });
        }else {
            doCommit();
        }
    }

    private void doCommit() {
        String selectedCode = getViewModel().getSelectedCode();
        if (getViewModel().indexObservable.get() > 0) {
            getViewModel().updateResidence(selectedCode);
        } else {
            if (!TextUtils.isEmpty(selectedCode) && !TextUtils.equals(selectedCode, getViewModel().initHukouCode)) {// 只有户口所在地改变时才更新
                getViewModel().updateResidence(selectedCode);
            } else {
                AppUtil.finishActivity(MeResidenceEditActivity.this);
            }
        }
    }
}