package com.kanzhun.marry.me.identify.fragment.avatar

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.viewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.BaseComposeFragment
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.base.compose.ui.O2Toolbar
import com.kanzhun.common.dialog.CommonSystemCenterDialog
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.base.H5URL
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.toCompleteH5Link
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.H5Model
import com.kanzhun.foundation.model.NoviceTaskType
import com.kanzhun.foundation.model.profile.ProfileCertModel
import com.kanzhun.foundation.model.profile.UpdateAvatarSuccessModel
import com.kanzhun.foundation.newtasktop.NewTaskTopFragment
import com.kanzhun.foundation.photoselect.upload.UploadResult
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.BuildConfig
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.api.model.PhotoCertStatus
import com.kanzhun.marry.me.databinding.MeAvatarAuthFailBinding
import com.kanzhun.marry.me.identify.BeautyResult
import com.kanzhun.marry.me.identify.FaceDetectInfo
import com.kanzhun.marry.me.identify.activity.AvatarBeautifyActivity
import com.kanzhun.marry.me.identify.activity.AvatarCropActivity
import com.kanzhun.marry.me.identify.activity.AvatarCropActivity.Companion.KEY_FINISH_AVATAR_AUTH
import com.kanzhun.marry.me.identify.decodeUriToBitmap
import com.kanzhun.marry.me.identify.singleDispatcher
import com.kanzhun.marry.me.identify.toFaceDetectInfo
import com.kanzhun.marry.me.identify.viewmodel.AvatarAuthViewModel
import com.kanzhun.marry.me.point.MePointReporter
import com.kanzhun.marry.me.util.AuthFailDialogType
import com.kanzhun.marry.me.util.AuthFailDialogUtil
import com.kanzhun.marry.me.util.IBackPressFragmentDelegate
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorItemType
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorManager
import com.kanzhun.utils.base.LText
import com.kanzhun.utils.platform.Utils
import com.nebula.sdk.ugc.editor.NebulaUGCImageEditor
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class AvatarAuthEditFragment2(
    private val adviceId: String? = null,
    private val photoId: String? = null,
    private val imageFileUri: Uri? = null
) : BaseComposeFragment(), IBackPressFragmentDelegate {
    private val parentViewModel: AvatarAuthViewModel by lazy {
        ViewModelProvider(requireActivity())[AvatarAuthViewModel::class.java]
    }

    private val viewModel: AvatarAuthEditViewModel2 by viewModels()

    override var showInterceptBackPressDialog: Boolean = true

    override fun needInterceptBackPress(): Boolean {
        val viewModel: AvatarAuthEditViewModel2 by viewModels()
        return (showInterceptBackPressDialog && viewModel.hasInput()).also {
            if (it) {
                showInterceptBackPressDialog = false
            }
        }
    }

    override fun showInterceptDialog(activity: FragmentActivity) {
        onBackPressDialogShowCallback()
        activity.showTwoButtonDialog(
            title = "确定离开吗",
            content = "离开后，已上传的图片不再保留",
            negativeText = "确定返回",
            positiveText = "我再想想",
            negativeButtonClick = {
                onBackPressCallback("确定返回")
                activity.finish()
            },
            positiveButtonClick = {
                onBackPressCallback("我再想想")
            })
    }

    @Composable
    override fun OnSetContent() {
        val viewModel: AvatarAuthEditViewModel2 = viewModel()
        var switchLeft by remember { mutableStateOf(true) }
        val uploadResult by viewModel.uploadResult.observeAsState()
        val imageUri = uploadResult?.result?.tinyImage?.url

        val beautyResult by viewModel.beautyResult.observeAsState()
        val serverResult by viewModel.serverResult.observeAsState()

        val context = LocalContext.current
        val onReadAuthorization = { H5Model.URL_AVATAR_PROTOCOL.openWithFullScreen(context) }
        Content(
            onBackClick = { onBackPress(requireActivity()) },
            onMoreClick = {
                MePointReporter.reportAvatarEditHelpExpose()
                ProtocolHelper.parseProtocol(H5URL.URL_H5_AVATAR_FAQ.toCompleteH5Link())
            },
            previewContent = {
                if (imageUri.isNullOrEmpty()) {
                    if (switchLeft) {
                        HomeStub(onSelect = { selectImage() })
                    } else {
                        DetailStub(onSelect = { selectImage() })
                    }
                } else {
                    if (switchLeft) {
                        HomePreview(imageUri = imageUri, onSelect = {
                            selectImage()
                            MePointReporter.reportAvatarEditClick(
                                type = "重新上传",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        })
                    } else {
                        DetailPreview(imageUri = imageUri, onSelect = {
                            selectImage()
                            MePointReporter.reportAvatarEditClick(
                                type = "重新上传",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        })
                    }
                }
            },
            panel = {
                var showReplaceTipsDialog by remember { mutableStateOf(false) }
                if (inReplaceScene(beautyResult = beautyResult, serverResult = serverResult)) {
                    ReplacePanel(
                        beautyResult = beautyResult,
                        serverResult = serverResult,
                        onReSelect = {
                            selectImage()

                            MePointReporter.reportAvatarEditClick(
                                type = "重新上传",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        },
                        onSubmit = {
                            showReplaceTipsDialog = true

                            MePointReporter.reportAvatarEditClick(
                                type = "不更换就这样上传",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        },
                        forceReUpload = beautyResult?.forceReUpload() == true
                    )
                } else {
                    EditPanel(
                        imageUri = imageUri,
                        onSelect = { isReselect ->
                            selectImage()

                            MePointReporter.reportAvatarEditClick(type = if (isReselect) "重新上传" else "点击上传")
                        },
                        onClickAuthorization = onReadAuthorization,
                        onSubmit = {
                            submit()

                            MePointReporter.reportAvatarEditClick(type = "提交")
                        })
                }

                if (showReplaceTipsDialog) {
                    ReplaceTipsDialog(
                        onDismissRequest = {},
                        onClickAuthorization = onReadAuthorization,
                        onCancel = {
                            showReplaceTipsDialog = false

                            MePointReporter.reportAvatarEditClick(
                                type = "取消授权弹窗",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        },
                        onRead = {
                            submit()

                            MePointReporter.reportAvatarEditClick(
                                type = "我已阅读并同意",
                                msg = beautyResult?.getReplaceSuggestion() ?: ""
                            )
                        })
                }
            },
            switchLeft = switchLeft,
            onSwitch = {
                switchLeft = it

                MePointReporter.reportAvatarEditClick(
                    type = if (switchLeft) "首页预览" else "详情预览",
                    msg = beautyResult?.getReplaceSuggestion() ?: ""
                )
            }
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.adviceId = adviceId
        viewModel.photoId = photoId

        viewModel.submitResult.observe(viewLifecycleOwner) { success ->
            dismissProgressDialog()
            if (success) {
                jumpSubmitSuccessPage()
                sendBooleanLiveEvent(LivedataKeyMe.AUTH_AVATAR_COMMIT_SUCCESS, true)
                AuthFailDialogUtil.reset(AuthFailDialogType.AVATAR_AUTH_FAIL)
            }
        }

        viewModel.errorCodeLiveData.observe(viewLifecycleOwner) {
            showSystemDialog()
        }

        ServiceManager.getInstance().noviceTaskService.updateNoviceTaskProgress(NoviceTaskType.NOVICE_TASK_TYPE_AVATAR)
        MePointReporter.reportAvatarEditExpose(
            parentViewModel.mSource,
            parentViewModel.cerBean?.rejectReason,
            parentViewModel.protocolFrom
        )

        if (parentViewModel.cerBean != null && parentViewModel.cerBean?.status == PhotoCertStatus.TYPE_APPROVE_PHOTO_FAILED) {
            AuthFailDialogUtil.show(AuthFailDialogType.AVATAR_AUTH_FAIL) {
                showAuthFailDialog()
            }
        } else {
            if ((
                        !LText.empty(adviceId) // 推荐头像替换
                                || !LText.empty(photoId) // 活动相册里面设置为形象照
                        )
                && imageFileUri != null
            ) {
                // 直接跳转裁剪页面
                AvatarCropActivity.intentInFragment(
                    activity = requireActivity(),
                    fragment = this,
                    cameraFileUri = imageFileUri,
                    isReplaceAvatar = true,
                    isGallery = false
                )
            }
        }

        liveEventBusObserve(KEY_FINISH_AVATAR_AUTH) { it: Boolean ->
            if (it) {
                AppUtil.finishActivity(activity)
            }
        }
    }

    // V 组 sdk 告诉客户端，该图片是否有问题，建议替换
    // 接口返回是否有异常信息
    private fun inReplaceScene(
        beautyResult: BeautyResult? = null,
        serverResult: String? = null
    ): Boolean {
        return (beautyResult?.hasReplaceSuggestion() == true)
                && beautyResult.getReplaceSuggestion().isNotEmpty()
                || (serverResult?.isNotEmpty() == true)
    }

    private fun selectImage() {
        PictureSelectorManager.startSelect(
            activity = requireActivity(),
            type = PictureSelectorManager.SelectType.AVATAR,
            clickCallback = { itemType ->
                when (itemType) {
                    PictureSelectorItemType.CAMERA -> {
                        MePointReporter.reportAvatarEditClick("拍照")
                    }

                    PictureSelectorItemType.GALLERY -> {
                        MePointReporter.reportAvatarEditClick("从相册中选择")
                    }

                    PictureSelectorItemType.CANCEL -> {
                        MePointReporter.reportAvatarEditClick("取消")
                    }
                }
            }) { uri, isGallery ->
            lifecycleScope.launch {
                delay(300)
                val host = activity
                host?.let {
                    AvatarCropActivity.intentInFragment(
                        activity = it,
                        fragment = this@AvatarAuthEditFragment2,
                        cameraFileUri = uri,
                        isGallery = isGallery,
                    )
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                AvatarCropActivity.ME_AVATAR_CROP_REQUEST_CODE -> {
                    val uploadResult =
                        try {
                            data?.getSerializableExtra(AvatarCropActivity.ME_AVATAR_CROP_INFO_KEY) as UploadResult
                        } catch (_: Exception) {
                            null
                        }

                    val beautyResult =
                        try {
                            data?.getSerializableExtra(AvatarBeautifyActivity.ME_AVATAR_BEAUTY_INFO_KEY) as BeautyResult
                        } catch (_: Exception) {
                            val width = uploadResult?.result?.originImage?.width ?: 0
                            val height = uploadResult?.result?.originImage?.height ?: 0
                            val sourceUri = uploadResult?.sourceUri
                            if (width > 0 && height > 0) {
                                BeautyResult(
                                    initialImageUri = sourceUri?.toUri(),
                                    infos = ArrayList(),
                                    width = width,
                                    height = height
                                )
                            } else {
                                null
                            }
                        }

                    if (uploadResult != null) {
                        showImage(uploadResult = uploadResult, beautyResult = beautyResult)
                    }
                }

                else -> {

                }
            }
        }
    }

    private fun showImage(uploadResult: UploadResult, beautyResult: BeautyResult? = null) {
        val viewModel: AvatarAuthEditViewModel2 by viewModels()
        viewModel.setUploadResult(uploadResult)

        if (beautyResult?.rawInfos == null /* 没有走美颜页面 */) {
            viewModel.shouldTriggerFaceDetection(beautyResult = beautyResult)
        } else {
            viewModel.setBeautyResult(result = beautyResult)

            viewModel.reportAvatarUploadParameter(
                uploadResult = uploadResult,
                beautyResult = beautyResult
            )
        }
    }

    private fun submit() {
        showProgressDialog("")
        val viewModel: AvatarAuthEditViewModel2 by viewModels()
        viewModel.submit()
    }

    private fun showSystemDialog() {
        val builder = CommonSystemCenterDialog.Builder(requireContext())
            .setTitle(getString(R.string.me_system_under_maintenance))
            .setPositiveText(resources.getString(R.string.me_ok))
            .setOnlyBtn(true)
            .setButtonClickListener(object : CommonSystemCenterDialog.OnClickListener {
                override fun onPositiveClick(dialog: Dialog, view: View) {
                    AppUtil.finishActivity(requireActivity())
                }

                override fun onNegativeClick(dialog: Dialog, view: View) {}
            })
        builder.create().show()
    }

    private fun jumpSubmitSuccessPage() {
        parentViewModel.avatarAuthStatusLiveData.value = PhotoCertStatus.TYPE_APPROVE_PHOTO_SUBMIT
    }

    private fun showAuthFailDialog() {
        parentViewModel.cerBean?.run {
            val dialog = CommonViewBindingDialog(
                requireActivity(),
                mCancelable = true,
                mCanceledOnTouchOutside = true,
                mGravity = Gravity.BOTTOM,
                mPaddingLeft = 0,
                mPaddingRight = 0,
                mAnimationStyle = R.style.common_window_bottom_to_top_anim,
                onInflateCallback = { inflater, dialog ->
                    val binding = MeAvatarAuthFailBinding.inflate(inflater)
                    binding.apply {
                        ivAvatarAuthPicture.load(value)
                        tvReason.text = rejectReason
                        btnNext.clickWithTrigger {
                            dialog.dismiss()
                            reportPoint("certify-fail-popup-click") {
                                actionp2 = "头像"
                            }
                        }
                        ivAvatarAuthPicture.clickWithTrigger {
                            ivAvatarAuthPicture.imageViewer(value ?: "")
                        }
                    }
                    binding
                })
            dialog.show()
            reportPoint("certify-fail-popup-expo") {
                actionp2 = "头像"
            }
        }
    }

    /**
     * 头像挽留弹框
     */
    override fun onBackPressCallback(msg: String) {
        MePointReporter.reportAvatarEditRetentionPopClick(msg)
    }

    override fun onBackPressDialogShowCallback() {
        MePointReporter.reportAvatarEditRetentionPopExpose()
    }
}

class AvatarAuthEditViewModel2 : BaseViewModel() {
    private val _uploadResult = MutableLiveData<UploadResult>()
    val uploadResult: LiveData<UploadResult> = _uploadResult

    private val _beautyResult = MutableLiveData<BeautyResult?>(null)
    val beautyResult: LiveData<BeautyResult?> = _beautyResult

    private val _serverResult = MutableLiveData<String?>()
    val serverResult: LiveData<String?> = _serverResult

    @SuppressLint("StaticFieldLeak")
    private val imageEditor: NebulaUGCImageEditor =
        NebulaUGCImageEditor.Builder().build(Utils.getApp().applicationContext)

    private val token: String?
        get() {
            return _uploadResult.value?.result?.token
        }

    var adviceId: String? = ""
    var photoId: String? = ""

    var submitResult: MutableLiveData<Boolean> = MutableLiveData()

    fun hasInput() = !token.isNullOrBlank()

    private var postJob: Job? = null

    init {
        imageEditor.setImageEditListener { _, infos, width, height -> // 第一个参数是图片的字节数组，第二个参数是人脸的检测数据
            postJob?.cancel()
            postJob = viewModelScope.launch {
                delay(100)

                /*
                    若命中多人，满足以下任一条件时不作拦截
                        最大人脸ratio ≥ 第二大人脸ratio*5倍
                        第二大人脸ratio ≤ 0.004
                 */

                val ifs =
                    (if (infos.isNullOrEmpty()) emptyList() else infos).map { it.toFaceDetectInfo() }
                        .toMutableList()

                // 倒序
                ifs.sortWith { o1, o2 -> o2.ratio.compareTo(o1.ratio) }

                val nifs = if (ifs.size >= 2) { // 多人脸
                    if (ifs[0].ratio >= ifs[1].ratio * 5 // 最大人脸ratio ≥ 第二大人脸ratio*5倍
                        || ifs[1].ratio <= 0.004 // 第二大人脸ratio≤0.004
                    ) {
                        // 只取第一个
                        listOf(ifs[0])
                    } else {
                        ifs
                    }
                } else {
                    ifs
                }

                val br = _beautyResult.value

                // 上传的图片的尺寸
                val ur = uploadResult.value
                val w = ur?.result?.originImage?.width ?: width
                val h = ur?.result?.originImage?.height ?: height

                val newBr = br?.copy(
                    infos = ArrayList(nifs),
                    rawInfos = ArrayList(ifs),
                    width = w,
                    height = h
                ) ?: BeautyResult(
                    infos = ArrayList(nifs),
                    rawInfos = ArrayList(ifs),
                    width = w,
                    height = h
                )

                _beautyResult.postValue(newBr)

                reportAvatarUploadParameter(ur, newBr)
            }
        }
    }

    fun reportAvatarUploadParameter(
        uploadResult: UploadResult? = null,
        beautyResult: BeautyResult? = null
    ) {
        /*
            新增：avatar-upload-parameter 记录上传照片的参数
                peer_id 记录上传用户的uid
                actionp2 记录上传的照片url
                actionp3 记录人脸占比面积比例ratio，若识别到多张人脸仅记录ratio值最大的一个
                actionp4 记录识别到的人脸数量
                actionp5 记录照片分辨率，格式a*b
                actionp6 记录是否命中了拦截，若是记录拦截的策略，多个用英文分号隔开：人脸占比面积过大;照片分辨率过低;不要使用多人合照
         */
        val peerId = AccountHelper.getInstance().account?.userId
        reportPoint("avatar-upload-parameter") {
            peer_id = peerId
            actionp1 = beautyResult?.rawInfos?.map { it.ratio }?.joinToString(";") ?: ""
            actionp2 = uploadResult?.result?.originImage?.url ?: ""
            actionp3 = beautyResult?.infos?.maxByOrNull { it.ratio }?.ratio?.toString() ?: ""
            actionp4 = beautyResult?.infos?.size?.toString() ?: ""
            actionp5 =
                "${uploadResult?.result?.originImage?.width ?: 0}*${uploadResult?.result?.originImage?.height ?: 0}"
            actionp6 =
                beautyResult?.getReplaceSuggestionList()?.joinToString(";") { it.first } ?: ""
        }
    }

    fun shouldTriggerFaceDetection(beautyResult: BeautyResult?) {
        beautyResult?.initialImageUri?.run {
            viewModelScope.launch {
                val initialImageBitmap = decodeUriToBitmap(this@run)
                initialImageBitmap?.let {
                    imageEditor.addImage(it)
                    imageEditor.enableFaceDetect(true)

                    //region 触发人脸识别
                    viewModelScope.launch(context = singleDispatcher) {
                        delay(100)
                        imageEditor.setBeautyLevel(1)
                        imageEditor.setWhitenessLevel(1)
                    }
                    //endregion
                }
            }
        }
    }

    fun setUploadResult(result: UploadResult) {
        _uploadResult.postValue(result)
    }

    fun setBeautyResult(result: BeautyResult? = null) {
        _beautyResult.postValue(result)
    }

    fun submit() {
        val observable =
            RetrofitManager.getInstance().createApi(MeApi::class.java)
                .requestUpdateAvatar(
                    /* avatar = */ token,
                    /* adviceId = */ adviceId,
                    /* photoId = */ photoId
                )
        HttpExecutor.execute(
            observable,
            object : BaseRequestCallback<UpdateAvatarSuccessModel?>(true) {
                override fun onSuccess(data: UpdateAvatarSuccessModel?) {
                    if (isQaDebugUser()) {
                        _serverResult.postValue("服务端提示：请更换图片")
                        submitResult.value = false
                    } else {
                        ExecutorFactory.execMainTaskDelay({
                            requestProfileCert(data?.id)
                        }, 1000)

                        reportAvatarSubmit(submitResult = true)
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                    submitResult.value = false

                    val errCode = reason.errCode

                    if (errCode == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                        errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE)
                    } else if (errCode == 3900 || errCode == 3901) { // 增加两个异常状态码3900,3901
                        _serverResult.postValue(reason.errReason)
                    }

                    reportAvatarSubmit(submitResult = false)
                }
            })
    }

    private fun reportAvatarSubmit(submitResult: Boolean = true) {
        val uploadResult = uploadResult.value
        val beautyResult = beautyResult.value
        reportPoint("avatar-submit-click") {
            actionp2 = uploadResult?.result?.originImage?.url ?: "" // 记录上传的照片url
            result = if (submitResult) "提交成功" else "提交失败" // 记录上传结果：提交成功、提交失败
            msg =
                beautyResult?.getReplaceSuggestionList()?.joinToString(";") { it.first }
                    ?: "" // 当result='拦截上传'、'建议更换'时，记录当前触发的拦截策略，多个用英文分号隔开：未识别到人脸、人脸占比面积过大、照片分辨率过低、不要使用多人合照
        }
    }

    private fun requestProfileCert(id: String?) {
        val profileCert =
            RetrofitManager.getInstance().createApi(MeApi::class.java)
                .getProfileCert(id, 10)
        HttpExecutor.execute(
            profileCert,
            object : BaseRequestCallback<ProfileCertModel?>(true) {
                override fun onSuccess(data: ProfileCertModel?) {
                    submitResult.value = true
                }

                override fun dealFail(reason: ErrorReason) {
                    submitResult.value = false
                }
            })
    }
}

@Composable
private fun Content(
    onBackClick: () -> Unit = {},
    onMoreClick: () -> Unit = {},
    previewContent: @Composable () -> Unit = {},
    panel: @Composable () -> Unit = {},
    switchLeft: Boolean = true,
    onSwitch: (Boolean) -> Unit = {},
    inPreview: Boolean = false
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(state = rememberScrollState())
        ) {
            O2Toolbar(
                onBackClick = onBackClick,
                iconRight = R.drawable.common_ic_help,
                onMoreClick = onMoreClick
            )

            if (!inPreview) {
                AndroidView(
                    factory = { context ->
                        FragmentContainerView(context).apply {
                            id = R.id.fragment_container
                            layoutParams = FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.MATCH_PARENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            )
                        }
                    },
                    update = { view ->
                        // 获取 FragmentManager
                        val fragmentManager =
                            (view.context as FragmentActivity).supportFragmentManager

                        // 开始 Fragment 事务
                        fragmentManager.beginTransaction()
                            .replace(view.id, NewTaskTopFragment(), "NewTaskTopFragment")
                            .commit()
                    }
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            Text(
                text = "上传你的形象照片",
                style = TextStyle(
                    color = colorResource(R.color.common_color_292929),
                    fontSize = 28.sp,
                    fontWeight = FontWeight(weight = 700)
                ),
                modifier = Modifier.padding(horizontal = 20.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "为维护平台交友安全，需要上传您本人的真实照片",
                style = TextStyle(
                    color = colorResource(R.color.common_color_797979),
                    fontSize = 14.sp,
                    fontWeight = FontWeight(weight = 400)
                ),
                modifier = Modifier.padding(horizontal = 20.dp)
            )

            Spacer(modifier = Modifier.height(22.dp))

            previewContent()
        }

        Column(
            modifier = Modifier.align(Alignment.BottomCenter),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Switcher(switchLeft = switchLeft, onSwitch = onSwitch)

            Spacer(modifier = Modifier.height(16.dp))

            panel()
        }
    }
}

/**
 * 首页预览（未上传形象照）
 */
@Composable
private fun HomeStub(onSelect: () -> Unit = {}) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 45.dp)
    ) {
        val (phone) = createRefs()

        Image(
            painter = painterResource(R.mipmap.me_ic_avatar_home_stub),
            contentScale = ContentScale.FillWidth,
            contentDescription = null,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(phone) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    end.linkTo(parent.end)
                }
                .noRippleClickable { onSelect() }
        )
    }
}

/**
 * 首页预览（已上传形象照）
 */
@Composable
private fun HomePreview(imageUri: String = "", onSelect: () -> Unit = {}) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 45.dp)
    ) {
        val (phone, avatar) = createRefs()

        AsyncImage(
            model = imageUri,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .padding(horizontal = 30.dp)
                .padding(top = 78.dp)
                .background(
                    color = if (BuildConfig.DEBUG) Color.LightGray else Color.Transparent,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                )
                .aspectRatio(ratio = 221 / 236f)
                .clip(shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .constrainAs(avatar) {
                    start.linkTo(phone.start)
                    top.linkTo(phone.top)
                    end.linkTo(phone.end)
                }
        )

        Image(
            painter = painterResource(R.mipmap.me_ic_avatar_home_preview),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(phone) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    end.linkTo(parent.end)
                }
                .noRippleClickable { onSelect() }
        )
    }
}

/**
 * 详情预览（未上传形象照）
 */
@Composable
private fun DetailStub(onSelect: () -> Unit = {}) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 45.dp)
    ) {
        val (phone) = createRefs()

        Image(
            painter = painterResource(R.mipmap.me_ic_avatar_detail_stub),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(phone) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    end.linkTo(parent.end)
                }
                .noRippleClickable { onSelect() }
        )
    }
}

/**
 * 详情预览（已上传形象照）
 */
@Composable
private fun DetailPreview(imageUri: String = "", onSelect: () -> Unit = {}) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 45.dp)
    ) {
        val (phone, avatar) = createRefs()

        AsyncImage(
            model = imageUri,
            contentDescription = null,
            modifier = Modifier
                .padding(horizontal = 3.dp)
                .background(
                    color = if (BuildConfig.DEBUG) Color.LightGray else Color.Transparent,
                    shape = RoundedCornerShape(topStart = 43.dp, topEnd = 43.dp)
                )
                .aspectRatio(1f)
                .clip(shape = RoundedCornerShape(topStart = 43.dp, topEnd = 43.dp))
                .constrainAs(avatar) {
                    start.linkTo(phone.start)
                    top.linkTo(phone.top)
                    end.linkTo(phone.end)
                }
        )

        Image(
            painter = painterResource(R.mipmap.me_ic_avatar_detail_preview),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(phone) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    end.linkTo(parent.end)
                }
                .noRippleClickable { onSelect() }
        )
    }
}

@Composable
private fun Switcher(switchLeft: Boolean = true, onSwitch: (switchLeft: Boolean) -> Unit = {}) {
    Row(
        modifier = Modifier
            .background(
                color = colorResource(R.color.common_color_191919_50),
                shape = RoundedCornerShape(74.dp)
            )
            .padding(horizontal = 2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val textBgColor = colorResource(R.color.common_color_191919)

        Box(modifier = Modifier.noRippleClickable { onSwitch(true) }) {
            Text(
                text = "首页预览",
                style = TextStyle(
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight(if (switchLeft) 600 else 400)
                ),
                modifier = Modifier
                    .then(
                        if (switchLeft) {
                            Modifier
                                .background(
                                    color = textBgColor,
                                    shape = RoundedCornerShape(74.dp)
                                )
                                .padding(horizontal = 12.dp, vertical = 6.dp)
                        } else {
                            Modifier.padding(
                                start = 14.dp,
                                end = 12.dp,
                                top = 8.dp,
                                bottom = 8.dp
                            )
                        }
                    )
            )
        }

        Box(modifier = Modifier.noRippleClickable { onSwitch(false) }) {
            Text(
                text = "详情预览",
                style = TextStyle(
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight(if (switchLeft) 400 else 600)
                ),
                modifier = Modifier
                    .then(
                        if (switchLeft) {
                            Modifier.padding(
                                start = 12.dp,
                                end = 14.dp,
                                top = 8.dp,
                                bottom = 8.dp
                            )
                        } else {
                            Modifier
                                .background(
                                    color = textBgColor,
                                    shape = RoundedCornerShape(74.dp)
                                )
                                .padding(horizontal = 12.dp, vertical = 6.dp)
                        }
                    )
            )
        }
    }
}

@Composable
private fun EditPanel(
    userChecked: Boolean = false,
    imageUri: String? = null,
    onSelect: (isReselect: Boolean) -> Unit = {},
    onClickAuthorization: () -> Unit = {},
    onSubmit: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .shadow(elevation = 16.dp)
            .background(color = Color.White)
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (imageUri != null) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.noRippleClickable { onSelect(true) }) {
                Image(
                    painter = painterResource(R.drawable.common_black_camera),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "重新上传",
                    style = TextStyle(
                        color = colorResource(R.color.common_color_292929),
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500)
                    ),
                )
            }

            Spacer(modifier = Modifier.height(20.dp))
        }

        var isChecked by remember { mutableStateOf(userChecked) }
        Auth(
            isChecked = isChecked,
            onCheckedChange = {
                isChecked = it

                MePointReporter.reportAvatarEditClick(type = if (isChecked) "勾选协议" else "取消勾选协议")
            },
            onClickAuthorization = onClickAuthorization
        )

        Spacer(modifier = Modifier.height(24.dp))

        if (imageUri != null) {
            O2Button(
                text = "同意并继续",
                enabled = isChecked,
                onClick = onSubmit
            )
        } else {
            O2Button(
                iconStart = {
                    Image(
                        painter = painterResource(R.drawable.common_black_camera),
                        colorFilter = ColorFilter.tint(color = Color.White),
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )

                    Spacer(modifier = Modifier.width(6.dp))
                },
                text = "点击上传",
                onClick = { onSelect(false) }
            )
        }
    }
}

@Composable
private fun Auth(
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit = {},
    onClickAuthorization: () -> Unit = {}
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            painter = painterResource(if (isChecked) R.drawable.common_check_box_selected else R.drawable.common_check_box),
            contentDescription = null,
            modifier = Modifier
                .size(18.dp)
                .noRippleClickable {
                    onCheckedChange(!isChecked)
                }
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = buildAnnotatedString {
                val start = "已阅读并同意"
                val middle = "《真实头像核验授权书》"
                val end = "，我们将严格保护您的信息"

                append(start)

                append(middle)
                addStyle(
                    style = SpanStyle(
                        color = colorResource(R.color.common_color_003580),
                        fontSize = 12.sp,
                    ),
                    start = start.length,
                    end = start.length + middle.length
                )

                append(end)
            },
            style = TextStyle(
                color = colorResource(R.color.common_color_191919),
                fontSize = 12.sp,
                fontWeight = FontWeight(400)
            ),
            modifier = Modifier.noRippleClickable { onClickAuthorization() }
        )
    }
}

@Composable
private fun ReplacePanel(
    beautyResult: BeautyResult? = null,
    serverResult: String? = null,
    onReSelect: () -> Unit = {},
    onSubmit: () -> Unit = {},
    forceReUpload: Boolean = false,
    inPreview: Boolean = false
) {
    Column(
        modifier = Modifier
            .shadow(elevation = 16.dp)
            .background(color = Color.White)
            .padding(20.dp),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Image(
                painter = painterResource(id = R.mipmap.me_ic_img_replace),
                contentDescription = "image description",
                modifier = Modifier
                    .width(16.dp)
                    .height(16.dp)
            )

            Text(
                text = "更换图片建议：",
                style = TextStyle(
                    color = Color(0xFF191919),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500)
                ),
                modifier = Modifier.noRippleClickable { }
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        val replaceReason =
            (beautyResult?.getReplaceSuggestion(inPreview = inPreview) ?: "").ifEmpty {
                serverResult ?: ""
            }
        Text(
            text = replaceReason,
            style = TextStyle(
                color = colorResource(R.color.common_color_FF3F4B),
                fontSize = 12.sp,
                fontWeight = FontWeight(400)
            ),
            modifier = Modifier.noRippleClickable { }
        )

        Spacer(modifier = Modifier.height(24.dp))

        O2Button(
            iconStart = {
                Image(
                    painter = painterResource(R.drawable.common_black_camera),
                    colorFilter = ColorFilter.tint(color = Color.White),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(6.dp))
            },
            text = "重新上传",
            onClick = onReSelect
        )

        Spacer(modifier = Modifier.height(20.dp))

        if (!forceReUpload) { // 是否允许用户上传
            Text(
                text = "不更换，就这样上传",
                style = TextStyle(
                    color = colorResource(R.color.common_color_7F7F7F),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400)
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .noRippleClickable { onSubmit() }
            )
        }
    }
}

@Composable
fun ReplaceTipsDialog(
    isUserChecked: Boolean = false,
    onDismissRequest: () -> Unit = {},
    onClickAuthorization: () -> Unit = {},
    onCancel: () -> Unit = {},
    onRead: () -> Unit = {}
) {
    Dialog(onDismissRequest = { onDismissRequest() }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(32.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(24.dp)
            ) {
                var isChecked by remember { mutableStateOf(isUserChecked) }
                Auth(
                    isChecked = isChecked,
                    onCheckedChange = { isChecked = it },
                    onClickAuthorization = onClickAuthorization
                )

                Spacer(modifier = Modifier.height(28.dp))

                Row(modifier = Modifier.fillMaxWidth()) {
                    O2Button(
                        modifier = Modifier.weight(1f),
                        text = "取消",
                        onClick = onCancel,
                        reversed = true
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    O2Button(
                        modifier = Modifier.weight(2f),
                        text = "我已阅读并同意",
                        onClick = onRead,
                        enabled = isChecked
                    )
                }
            }
        }
    }
}

//region Previewer
@Preview(showBackground = true)
@Composable
private fun PreviewHomeStub() {
    Content(
        previewContent = { HomeStub() },
        panel = { EditPanel() },
        inPreview = true
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewHomePreview() {
    Content(
        previewContent = { HomePreview() },
        panel = { EditPanel(userChecked = true, imageUri = "xxx") },
        inPreview = true
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewDetailStub() {
    Content(
        previewContent = { DetailStub() },
        panel = { EditPanel() },
        switchLeft = false,
        inPreview = true
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewDetailPreview() {
    Content(
        previewContent = { DetailPreview() },
        panel = { EditPanel(imageUri = "xxx") },
        switchLeft = false,
        inPreview = true
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewHomeReplacePreview() {
    Content(
        previewContent = { HomePreview() },
        panel = {
            ReplacePanel(
                beautyResult = BeautyResult(infos = ArrayList<FaceDetectInfo>().apply {
                    add(
                        FaceDetectInfo().apply {
                            width = 0
                            height = 0
                            ratio = 0.0f
                        }
                    )
                    add(
                        FaceDetectInfo().apply {
                            width = 200
                            height = 200
                            ratio = 0.5f
                        }
                    )
                }),
                forceReUpload = true,
                inPreview = true
            )
        },
        inPreview = true
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewDetailReplacePreview() {
    Content(
        previewContent = { DetailPreview() },
        panel = {
            ReplacePanel(
                beautyResult = BeautyResult(infos = ArrayList<FaceDetectInfo>().apply {
//                    add(
//                        FaceDetectInfo().apply {
//                            width = 0
//                            height = 0
//                            ratio = 0.0f
//                        }
//                    )
                    add(
                        FaceDetectInfo().apply {
                            width = 200
                            height = 200
//                            ratio = 0.5f
                        }
                    )
                }),
                inPreview = true
            )
        },
        switchLeft = false,
        inPreview = true
    )
}

@Preview
@Composable
private fun PreviewReplaceTipsDialog() {
    ReplaceTipsDialog(isUserChecked = true)
}
//endregion