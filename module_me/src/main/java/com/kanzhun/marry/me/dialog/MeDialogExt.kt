package com.kanzhun.marry.me.dialog

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.marry.me.R

fun FragmentActivity.showAnswerDeleteDialog(callback: () -> Unit) {
    showTwoButtonDialog(title = R.string.me_question_answer_text_del_title.toResourceString()+R.string.me_question_answer_text_del_content.toResourceString(),
        positiveText = R.string.common_delete.toResourceString(),
        deleteBtn = true,
        positiveButtonClick = {
            callback()
        },
        negativeText = R.string.common_cancel.toResourceString(),
        negativeButtonClick = {

        }
    )
}
fun FragmentActivity.showAnswerExitDialog(callback: () -> Unit) {
    showTwoButtonDialog(title = "",
        content = R.string.me_intro_text_dialog_title.toResourceString(),
        positiveText = R.string.me_intro_text_dialog_p.toResourceString(),
        positiveButtonClick = {
        },
        negativeText = R.string.me_intro_text_dialog_n.toResourceString(),
        negativeButtonClick = {
            callback()
        }
    )
}