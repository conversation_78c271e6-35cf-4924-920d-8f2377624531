package com.kanzhun.marry.me.setting.fragment;

import static android.app.Activity.RESULT_OK;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.common.AvoidOnResult;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.api.bean.ServerHighlightListBean;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.model.H5Model;
import com.kanzhun.foundation.model.WebViewBean;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.imageviewer.ImageViewer;
import com.kanzhun.imageviewer.ImageViewerBuilder;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentUserReportSubmitBinding;
import com.kanzhun.marry.me.databinding.MeItemReportPicAddBinding;
import com.kanzhun.marry.me.databinding.MeItemReportPicBinding;
import com.kanzhun.marry.me.setting.bean.ReportPicBean;
import com.kanzhun.marry.me.setting.callback.UserReportSubmitCallback;
import com.kanzhun.marry.me.setting.viewmodel.UserReportViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;
import com.zhihu.matisse.Matisse;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/15
 */
public class UserReportSubmitFragment extends FoundationVMShareFragment<MeFragmentUserReportSubmitBinding, FoundationViewModel, UserReportViewModel>
        implements UserReportSubmitCallback {

    private BaseBinderAdapter adapter;

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().tvTitle.setText(getResources().getString(R.string.me_report_reason, getActivityViewModel().selectReason.name));
        initStatusDesc();
        initProtocol();
        initAdapter();
        getActivityViewModel().getSuccessLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    getActivityViewModel().getSuccessLiveData().setValue(false);
                    Navigation.findNavController(getDataBinding().btnSubmit).navigate(R.id.action_reportSubmitFragment_to_successFragment);
                }
            }
        });
    }

    private void initAdapter() {
        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(String.class, new BaseDataBindingItemBinder<String, MeItemReportPicAddBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_report_pic_add;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemReportPicAddBinding> holder, MeItemReportPicAddBinding binding, String item) {

            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemReportPicAddBinding> holder, @NonNull View view, String data, int position) {
                super.onClick(holder, view, data, position);
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                PhotoSelectManager.jumpForGalleryOnlyImageResult((FragmentActivity) activity, false, 6 - getActivityViewModel().getImageUris().size(), new AvoidOnResult.Callback() {
                    @Override
                    public void onActivityResult(int requestCode, int resultCode, Intent data) {
                        if (resultCode == RESULT_OK && data != null) {
                            List<Uri> result = Matisse.obtainResult(data);
                            setPicData(result);
                        }
                    }
                });
            }
        });

        adapter.addItemBinder(ReportPicBean.class, new BaseDataBindingItemBinder<ReportPicBean, MeItemReportPicBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_report_pic;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemReportPicBinding> holder, MeItemReportPicBinding binding, ReportPicBean item) {
                binding.setItem(item);
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_delete, R.id.iv_pic);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemReportPicBinding> holder, @NonNull View view, ReportPicBean data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                int id = view.getId();
                if (id == R.id.iv_delete) {
                    setPicDelete(data.uri);
                } else if (id == R.id.iv_pic) {
                    ImageViewer.Companion.start(activity, new Function1<ImageViewerBuilder, Unit>() {
                        @Override
                        public Unit invoke(ImageViewerBuilder imageViewerBuilder) {
                            imageViewerBuilder.views(getDataBinding().recyclerview, R.id.iv_pic, getShowPicData(adapter.getData()));
                            imageViewerBuilder.urls(getActivityViewModel().getImageUrisString());
                            imageViewerBuilder.position(position);
                            return null;
                        }
                    });
                }
            }
        });
        getDataBinding().recyclerview.setClipToPadding(false);
        getDataBinding().recyclerview.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false));
        getDataBinding().recyclerview.setAdapter(adapter);

        List<Object> data = new ArrayList<>();
        for (Uri uri : getActivityViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add("add");
        }

        adapter.setList(data);
    }

    private List<? extends Object> getShowPicData(List<Object> data) {
        List<Object> pics = new ArrayList<>(data.size());
        for (Object o : data) {
            if (o instanceof ReportPicBean) {
                pics.add(o);
            } else {
                pics.add(null);
            }
        }
        return pics;
    }

    private void setPicDelete(Uri delUri) {
        getActivityViewModel().getImageUris().remove(delUri);
        List<Object> data = new ArrayList<>();
        for (Uri uri : getActivityViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add("add");
        }
        adapter.setList(data);
    }

    private void setPicData(List<Uri> result) {
        if (LList.isEmpty(result)) {
            return;
        }
        getActivityViewModel().getImageUris().addAll(result);
        List<Object> data = new ArrayList<>();
        for (Uri uri : getActivityViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add("add");
        }
        adapter.setList(data);
    }

    private void initStatusDesc() {
        if (!TextUtils.isEmpty(getActivityViewModel().getTitle())) {
            getDataBinding().tvDesc.setText(getActivityViewModel().getTitle());
        }
//        switch (getActivityViewModel().status) {
//            case MessageConstants.MATCHING_STATUS_MY_LIKE:
//            case MessageConstants.MATCHING_STATUS_LIKE_YOU:
//                getDataBinding().tvDesc.setText(R.string.me_report_desc);
//                break;
//            case MessageConstants.MATCHING_STATUS_FRIEND:
//                getDataBinding().tvDesc.setText(R.string.me_report_desc_2);
//                break;
//            case MessageConstants.MATCHING_STATUS_DATING:
//                getDataBinding().tvDesc.setText(R.string.me_report_desc_3);
//                break;
//            case MessageConstants.MATCHING_STATUS_LOVERS:
//                getDataBinding().tvDesc.setText(R.string.me_report_desc_4);
//                break;
//            default:
//                break;
//        }
    }

    private void initProtocol() {
        String content = getString(R.string.me_report_protocol);
        List<ServerHighlightListBean> indexList = getProtocolHighlightList(content);
        int length = content.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(content);
        int size = indexList.size();
        int startIndex;
        int endIndex;
        for (int i = 0; i < size; i++) {
            ServerHighlightListBean bean = indexList.get(i);
            if (bean == null) continue;
            startIndex = bean.startIndex;
            endIndex = bean.endIndex;
            if (startIndex < 0 || endIndex <= startIndex || endIndex > length) continue;
            builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.common_color_003580)), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            builder.setSpan(new ClickableSpan() {
                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(ContextCompat.getColor(activity, R.color.common_color_003580));
                    ds.setUnderlineText(false);
                }

                @Override
                public void onClick(@NonNull View widget) {
                    WebViewBean webViewBean = new WebViewBean();
                    webViewBean.setUrl(bean.subUrl);
                    webViewBean.setStyle(WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
                    AppUtil.startUri(activity, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);
                }
            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        getDataBinding().tvLink.setMovementMethod(LinkMovementMethod.getInstance());
        getDataBinding().tvLink.setText(builder);
    }

    private List<ServerHighlightListBean> getProtocolHighlightList(String content) {
        String protocol11String = activity.getString(R.string.me_protocol_link);
        String protocol12String = activity.getString(R.string.me_policy_link);
        List<ServerHighlightListBean> indexList = new ArrayList<>();
        // 《用户协议》
        ServerHighlightListBean protocol11 = new ServerHighlightListBean();
        protocol11.startIndex = getIndexes(protocol11String, content)[0];
        protocol11.endIndex = getIndexes(protocol11String, content)[1];
        protocol11.subUrl = H5Model.URL_H5_PROTOCOL_USER.toH5Url();
        // 《隐私协议》
        ServerHighlightListBean protocol12 = new ServerHighlightListBean();
        protocol12.startIndex = getIndexes(protocol12String, content)[0];
        protocol12.endIndex = getIndexes(protocol12String, content)[1];
        protocol12.subUrl = H5Model.URL_H5_PROTOCOL_PRIVACY.toH5Url();
        indexList.add(protocol11);
        indexList.add(protocol12);
        return indexList;
    }

    private int[] getIndexes(@NonNull String target, @NonNull String total) {
        int[] idx = new int[2];
        idx[0] = total.indexOf(target);
        idx[1] = idx[0] + target.length();
        return idx;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_user_report_submit;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public void clickLeft(View view) {
        activity.onBackPressed();
    }

    @Override
    public void submit() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
//        getActivityViewModel().getSuccessLiveData().setValue(true);
        getActivityViewModel().requestSubmit();
    }
}
