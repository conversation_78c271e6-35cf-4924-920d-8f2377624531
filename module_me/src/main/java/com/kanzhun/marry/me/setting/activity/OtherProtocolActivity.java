package com.kanzhun.marry.me.setting.activity;

import static com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt.fullScreenAndBlackText;

import android.os.Bundle;
import android.view.View;

import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.H5Model;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.ActivityOtherProtocolBinding;
import com.kanzhun.marry.me.setting.callback.OtherProtocolCallback;
import com.kanzhun.marry.me.setting.viewmodel.OtherProtocolViewModel;

/**
 * 其他协议
 */
@RouterUri(path = AppPageRouter.OTHER_PROTOCOL_ACTIVITY)
public class OtherProtocolActivity extends FoundationVMActivity<ActivityOtherProtocolBinding, OtherProtocolViewModel> implements OtherProtocolCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.activity_other_protocol;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fullScreenAndBlackText(this, getDataBinding().llRoot, true);
        if (AccountHelper.getInstance().isParent()) {
            getDataBinding().idFaceVerify.setVisibility(View.GONE);
        }
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(OtherProtocolActivity.this);
    }

    @Override
    public void clickRight(View view) {
    }

    @Override
    public void faceProtocol(View view) {
        H5Model.URL_H5_PROTOCOL_FACE.openWithFullScreen(OtherProtocolActivity.this);
    }


    @Override
    public void logOffProtocol(View view) {
        H5Model.URL_H5_LOGOUT.openWithFullScreen(OtherProtocolActivity.this);
    }
}