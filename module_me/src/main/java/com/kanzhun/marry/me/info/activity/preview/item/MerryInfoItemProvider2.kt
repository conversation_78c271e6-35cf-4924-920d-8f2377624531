package com.kanzhun.marry.me.info.activity.preview.item

import com.kanzhun.common.kotlin.ext.blurry
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.unBlurry
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData
import com.kanzhun.foundation.api.model.clickEnable
import com.kanzhun.foundation.api.model.isLock
import com.kanzhun.foundation.api.model.lockTxt
import com.kanzhun.foundation.api.model.pointStr
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.PreviewLikeView
import com.kanzhun.marry.me.databinding.MePreviewUserMerryInfoSection2Binding
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel

/**
 * 结婚、家庭信息
 */
class MerryInfoItemProvider2(val viewModel: MeUserInfoViewModel,val click:MeInfoEditFragmentCallbackImp) : BaseItemProvider<UserPreviewItemBean, MePreviewUserMerryInfoSection2Binding>() {
    override fun onBindItem(binding: MePreviewUserMerryInfoSection2Binding, item: UserPreviewItemBean) {
        binding.run {
            val profileMetaModel = item.data
            //单身原因
            val showSingleReason = !item.data.baseInfo?.singleReason.isNullOrBlank()
            tvSingleReasonTitle.visible(showSingleReason)
            tvSingleReason.visible(showSingleReason)

            idBlurryLayoutSingle.visible(showSingleReason)

            val singleReasonLoginUserCertStatusItem = item.data.loginUserCertStatus?.singleReason
            if(!item.data.userId.isMyself() &&  singleReasonLoginUserCertStatusItem?.isLock() == true){
                val content = if (singleReasonLoginUserCertStatusItem?.clickEnable() == true) {
                    "填写我的单身原因 解锁查看对方资料"
                } else {
                    "你的资料正在审核中 通过后可解锁查看"
                }
                tvSingleReason.blurry(idBlurryLayoutSingle,singleReasonLoginUserCertStatusItem.lockTxt(),content,singleReasonLoginUserCertStatusItem.clickEnable()){
                    if(singleReasonLoginUserCertStatusItem.status == 2){
                        ProtocolHelper.parseProtocol(singleReasonLoginUserCertStatusItem.url)
                    }else{
                        click.clickSingle("",0,"")
                    }
                    reportPoint("user-detail-locked-module-click"){
                        type = "单身原因"
                    }
                }
                reportPoint("user-detail-locked-module-expo"){
                    status = singleReasonLoginUserCertStatusItem.pointStr()
                    type = "单身原因"
                }
                idLike.gone()
                tvMyIdealType.maxLines = 20
            }else{
                tvSingleReason.unBlurry(idBlurryLayoutSingle)
                idLike.visible()
                idLike.setData(
                    item.data.baseInfo.hideThumb,
                    item.data.thumbInfo?.singleReason,
                    false,
                    item.data.userId,
                    PreviewLikeView.LIKE_TYPE.SINGLE_REASON,
                    "",
                    viewModel.getMSendLikeBean(),
                    SendLikeBeanItemData(content = profileMetaModel.baseInfo?.singleReason)
                )
                tvMyIdealType.maxLines = Int.MAX_VALUE
            }
            tvSingleReason.text = item.data.baseInfo?.singleReason

            //理想伴侣
            val showIdealType = !item.data.baseInfo?.idealPartnerDesc.isNullOrBlank()
            tvMyIdealTypeTitle.visible(showIdealType)
            tvMyIdealType.visible(showIdealType)


            idBlurryLayoutIdeal.visible(showIdealType)
            val idealPartnerDescLoginUserCertStatusItem = item.data.loginUserCertStatus?.idealPartnerDesc
            if(!item.data.userId.isMyself() &&  idealPartnerDescLoginUserCertStatusItem?.isLock() == true){
                val content = if (idealPartnerDescLoginUserCertStatusItem?.clickEnable()==true) {
                    "填写我的理想型 解锁查看对方资料"
                } else {
                    "你的资料正在审核中 通过后可解锁查看"
                }
                tvMyIdealType.blurry(idBlurryLayoutIdeal,idealPartnerDescLoginUserCertStatusItem.lockTxt(),content,idealPartnerDescLoginUserCertStatusItem.clickEnable()){
                    if(idealPartnerDescLoginUserCertStatusItem.status == 2){
                        ProtocolHelper.parseProtocol(idealPartnerDescLoginUserCertStatusItem.url)
                    }else{
                        click.clickBeauty("",0,"")
                    }
                    reportPoint("user-detail-locked-module-click"){
                        type = "我的理想型"
                    }
                }
                reportPoint("user-detail-locked-module-expo"){
                    status = idealPartnerDescLoginUserCertStatusItem.pointStr()
                    type = "我的理想型"
                }
                idLike2.gone()
                tvSingleReason.maxLines = 20
            }else{
                tvMyIdealType.unBlurry(idBlurryLayoutIdeal)
                idLike2.visible()
                idLike2.setData(
                    item.data.baseInfo.hideThumb,
                    item.data.thumbInfo?.idealPartnerDesc,
                    false,
                    item.data.userId,
                    PreviewLikeView.LIKE_TYPE.IDEAL_PARTNER_DESC,
                    "",
                    viewModel.getMSendLikeBean(),
                    SendLikeBeanItemData(content = profileMetaModel.baseInfo?.idealPartnerDesc)
                )
                tvSingleReason.maxLines = Int.MAX_VALUE
            }
            tvMyIdealType.text = item.data.baseInfo?.idealPartnerDesc
        }

    }
}