package com.kanzhun.marry.me.info.callback

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.common.AvoidOnResult
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.dialog.BottomListDialogNew
import com.kanzhun.common.dialog.model.SelectBottomBean
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ui.activity.BaseActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.model.ABFace
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.base.LoveGoal
import com.kanzhun.foundation.base.LoveGoalListBean
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.dialog.StoryGuideDialog
import com.kanzhun.foundation.kotlin.ui.dialog.showWheelOneSelectDialog
import com.kanzhun.foundation.model.WebViewBean
import com.kanzhun.foundation.model.profile.OpenScreen
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.model.profile.QuestionTemplateModel
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.foundation.router.AppPageRouter
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.MePageRouter.jumpToUpdateNicknameActivity
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.api.bean.EthnicityListResponse
import com.kanzhun.marry.me.dialog.BirthDialogUtil
import com.kanzhun.marry.me.dialog.showHometownDialog
import com.kanzhun.marry.me.dialog.showHuKouDialog
import com.kanzhun.marry.me.dialog.showLivingPlaceDialog
import com.kanzhun.marry.me.dialog.showStatureDialog
import com.kanzhun.marry.me.dialog.showWeightDialog
import com.kanzhun.marry.me.info.activity.ABImpressionActivity
import com.kanzhun.marry.me.info.activity.MeBaseInfoEditActivity
import com.kanzhun.marry.me.info.activity.MeHouseCarEditActivity
import com.kanzhun.marry.me.info.activity.MeMarryLoveStatusActivity
import com.kanzhun.marry.me.info.activity.MeRevenueEditActivity
import com.kanzhun.marry.me.info.bean.MyNickAndIntroduction
import com.kanzhun.marry.me.info.bean.VideoStoryItem
import com.kanzhun.marry.me.info.fragment.StoryListEditFragmentNew3
import com.kanzhun.marry.me.info.util.getMeEditInfoSourceStrByStatus
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel
import com.kanzhun.marry.me.point.MePointAction
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LList
import com.zhihu.matisse.Matisse
import kotlin.random.Random

class MeInfoEditFragmentCallbackImp(val activity: BaseActivity,val isNew:Boolean = false) : MeInfoEditFragmentCallback {

    private var viewModel: MyInfoEditViewModel? = null
    private var mDialog: BottomListDialogNew? = null

    fun setActivityViewModel(model: MyInfoEditViewModel) {
        viewModel = model
    }

    override fun clickLeft(view: View?) {
    }

    override fun clickRight(view: View?) {
    }

    override fun clickAvatar(
        status: Int,
        rejectReason: String?,
        value: String?,
    ) {
        ProtocolHelper.userProtocolCoreByLocal(Constants.TO_AVATAR_CERT_401, emptyMap())
        reportPoint(MePointAction.MYINFO_AVATAR_CLICK) {
            this.status = getMeEditInfoSourceStrByStatus(value, status)
        }
    }

    override fun clickNick(nick: String?, status: Int, rejectReason: String?) {
        jumpToUpdateNicknameActivity(context = activity, name = nick, rejectReason = rejectReason)
        reportPoint(MePointAction.MYINFO_NICKNAME_CLICK) {
            this.status = getMeEditInfoSourceStrByStatus(nick, status)
        }
    }

    override fun clickIntroduction(nickAndIntroduction: MyNickAndIntroduction?) {
        var intro: String? = ""
        var certInfo: String? = ""
        if (nickAndIntroduction != null) {
            intro = nickAndIntroduction.intro
            certInfo = nickAndIntroduction.introCertInfo
        }

        MePageRouter.jumpToUpdateIntroActivity(
            activity,
            content = intro,
            certInfo = certInfo,
            type = 0,
            certStatus = nickAndIntroduction?.introCertStatus.toString()
        )

    }

    override fun clickAddAB() {
        MePageRouter.jumpToAddABFaceActivity(
            activity,
            RequestCodeConstants.REQUEST_CODE_ME_AB_FACE_IMPRESSION
        )
    }

    override fun clickAddVoice() {

        val responseObservable =
            RetrofitManager.getInstance().createApi(MeApi::class.java).getQuestionTemplate(2)
        HttpExecutor.execute(
            responseObservable,
            object : BaseRequestCallback<QuestionTemplateModel>(true) {
                override fun onSuccess(data: QuestionTemplateModel) {
                    val list = mutableListOf<QuestionTemplateModel.QuestionInfoBean>()
                    data.questionGroup.forEach {
                        list.addAll(it.questionInfos)
                    }
                    val size = list.size
                    val i = Random.nextInt(size)
                    MePageRouter.jumpToEditVoiceAnswerActivity(null, list[i])
                }

                override fun dealFail(reason: ErrorReason) {

                }

            })

//        MePageRouter.jumpToSelectQuestionActivity(activity, MePageRouter.TYPE_ANSWER_VOICE, false)

    }

    override fun clickVoice(voice: ProfileInfoModel.QuestionAnswer?) {
        if (voice != null) {
            MePageRouter.jumpToEditVoiceAnswerActivity(voice, null)

        }
    }

    override fun clickAddAnswer() {
        MePageRouter.jumpToSelectQuestionActivity(activity, MePageRouter.TYPE_ANSWER_TEXT, false)
    }

    override fun clickAddOpenScreen() {
        MePageRouter.jumpMBTITestActivity(activity)
    }

    override fun clickGoTestResult(openScreen: OpenScreen?) {
        if (openScreen?.status == OpenScreen.TEST_DONE) {
            if (TextUtils.isEmpty(openScreen.linkUrl)) {
                T.ss("恋爱测试结果地址异常")
                return
            }
            val webViewBean = WebViewBean()
            webViewBean.url = openScreen.linkUrl
            webViewBean.style = WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT
            webViewBean.requestCode = RequestCodeConstants.REQUEST_CODE_LOVE_TEST
            val bundle = Bundle()
            bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean)
            AppUtil.startUriForResult(
                activity,
                AppPageRouter.WEB_VIEW_ACTIVITY,
                RequestCodeConstants.REQUEST_CODE_LOVE_TEST,
                bundle
            )
        }
    }

    override fun clickBirthday(baseInfo: ProfileInfoModel.BaseInfo) {
        BirthDialogUtil().showBirthDayDialog(activity, baseInfo.birthday.orEmpty(), {}, {})
    }

    override fun clickEducation(baseMeta: ProfileInfoModel.BaseInfo, pageSource: PageSource) {
        if (baseMeta.certifyProgress == ProfileInfoModel.PROGRESS_TYPE_UN_CERTIFY) { //未实名认证
            T.bSS("请先完成实名认证")
        } else {
            if (baseMeta.schoolCertStatus == ProfileMetaModel.STATUS_UNDER_REVIEW) {
                T.ss("学历信息审核中，请耐心等待")
                return
            }
            MePageRouter.jumpToEducationIdentifyActivity(
                activity,
                CertificationIdentifySource.ME_INFO,
                pageSource,
                btnText = "提交",
                mustEdit = true,
                editEnterCer = false
            )
        }
    }

    override fun clickCompany(baseMeta: ProfileInfoModel.BaseInfo?, pageSource: PageSource) {
        if (baseMeta?.careerCertStatus == ProfileMetaModel.STATUS_UNDER_REVIEW || baseMeta?.workCompanyStatus == ProfileMetaModel.STATUS_UNDER_REVIEW) {
            T.ss("工作信息审核中，请耐心等待")
            return
        }
        MePageRouter.jumpToMeCompanyAuthActivity(
            activity,
            pageSource,
            btnText = "提交",
            mustEdit = true,
            editEnterCer = true
        )
    }

    override fun clickLivingPlace(baseMeta: ProfileInfoModel.BaseInfo) {
        activity.showLivingPlaceDialog(baseMeta.addressCode.orEmpty())
    }

    override fun clickHometown(baseMeta: ProfileInfoModel.BaseInfo) {
        activity.showHometownDialog(baseMeta.hometownCode.orEmpty())
    }

    override fun clickHeight(baseMeta: ProfileInfoModel.BaseInfo) {
        activity.showStatureDialog(baseMeta.height)
    }

    override fun clickOccupation(baseMeta: ProfileInfoModel.BaseInfo?) {

    }

    //年薪
    override fun clickRevenue(baseMeta: ProfileInfoModel.BaseInfo?) {
        val intent = Intent(activity, MeRevenueEditActivity::class.java)
        AppUtil.startActivityForResult(
            activity,
            intent,
            RequestCodeConstants.REQUEST_CODE_REVENUE_EDIT
        )
    }

    override fun clickHouseCar() {
        val intent = Intent(activity, MeHouseCarEditActivity::class.java)
        AppUtil.startActivityForResult(
            activity,
            intent,
            RequestCodeConstants.REQUEST_CODE_HOUSE_CAR_EDIT
        )
    }

    override fun clickPermanentResidence(baseMeta: ProfileInfoModel.BaseInfo) {
        activity.showHuKouDialog(baseMeta.hukouCode.orEmpty())
    }

    override fun clickPrivacyNotice(view: View?) {
    }

    //type
    override fun clickLabel(bean: MutableList<ProfileInfoModel.Label>?, type: Int) {
        //去标签页
        LoginPageRouter.jumpToKeyLabel(
            activity,
            bean,
            RequestCodeConstants.REQUEST_CODE_SELECT_LABEL,
            type
        )
    }

    override fun clickPic(data: BaseStoryShowItem, nowAllCount: Int) {
        if (TextUtils.isEmpty(data.id)) {
            return
        }
        if (data.file == null) {
            return
        }
        if (!data.addFailed.get() && data.upLoadPercent.get() < 100) { //故事上传中不可点击
            return
        }
        reportPoint(MePointAction.MYINFO_LIFEPHOTO_CLICK) {
            this.type = "查看"
            this.status = getMeEditInfoSourceStrByStatus(data.url, data.certStatus.get())
            this.count = nowAllCount
            this.actionp2 = "打开"
        }
        if (data.certStatus.get() == ProfileMetaModel.STATUS_REJECTED) {
            reEditRejectedStoryFiles(data)
            return
        }
        reEdit(data)
    }

    override fun clickPicAdd(max: Int) {
        if (max == 0) {
            return
        }
//        val hasShowGuide = SpManager.get().user().getBoolean(Constants.HAS_SHOW_STORY_GUIDE, false)
//        if (hasShowGuide) {
//            addStory(max)
//        } else {
//            SpManager.putUserBoolean(Constants.HAS_SHOW_STORY_GUIDE, true)
        val builder = StoryGuideDialog.Builder(activity as FragmentActivity)
            .setPadding(0, 0)
            .add(R.id.btn_finish)
            .setOnItemClickListener { dialog, _ ->
                addStory(max)
                dialog.dismiss()
            }
        builder.create().show()
//        }
    }

    //家庭介绍
    override fun clickFamily(familyDesc: String?, familyDescStatus: Int, familyDescInfo: String?) {
        MePageRouter.jumpToUpdateIntroActivity(
            context = activity, content = familyDesc.orEmpty(),
            certInfo = familyDescInfo.orEmpty(), type = 1, certStatus = familyDescStatus.toString()
        )

    }

    //兴趣爱好
    override fun clickLike(
        interestDesc: String?,
        interestDescStatus: Int,
        interestDescInfo: String?,
    ) {
        MePageRouter.jumpToUpdateIntroActivity(
            context = activity,
            content = interestDesc.orEmpty(),
            certInfo = interestDescInfo.orEmpty(),
            type = 4,
            certStatus = interestDescStatus.toString()
        )

    }

    override fun clickActivityPhoto(model: MyInfoEditViewModel?) {
        MePageRouter.jumpToActivityPhoto(activity)
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_ACTIVITY_ALBUM_READ,null,null)
    }

    override fun clickSingle(
        singleReason: String?,
        singleReasonCertStatus: Int,
        singleReasonCertInfo: String?,
    ) {
        MePageRouter.jumpToUpdateIntroActivity(
            context = activity,
            content = singleReason.orEmpty(),
            certInfo = singleReasonCertInfo.orEmpty(),
            type = 3,
            certStatus = singleReasonCertStatus.toString()
        )
    }

    override fun clickBeauty(
        idealPartnerDesc: String?,
        idealPartnerDescStatus: Int,
        idealPartnerDescInfo: String?,
    ) {
        MePageRouter.jumpToUpdateIntroActivity(
            context = activity,
            content = idealPartnerDesc.orEmpty(),
            certInfo = idealPartnerDescInfo.orEmpty(),
            type = 2,
            certStatus = idealPartnerDescStatus.toString()
        )
    }

    override fun clickAB(data: ABFace) {
        val abFace = ABFace()
        abFace.id = data.id
        abFace.titleA = data.titleA
        abFace.photoA = data.photoA
        abFace.tinyPhotoA = data.tinyPhotoA
        abFace.titleB = data.titleB
        abFace.photoB = data.photoB
        abFace.tinyPhotoB = data.tinyPhotoB
        abFace.aPictureReject = data.aPictureReject
        abFace.aTitleReject = data.aTitleReject
        abFace.bPictureReject = data.bPictureReject
        abFace.bTitleReject = data.bTitleReject
        abFace.certInfo = data.certInfo
        if (data.certStatus == ProfileMetaModel.STATUS_REJECTED && data.aPictureReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && data.aTitleReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && data.bPictureReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && data.bTitleReject != ProfileInfoModel.CONTENT_TYPE_REJECTED) {
            abFace.aPictureReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
            abFace.aTitleReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
            abFace.bPictureReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
            abFace.bTitleReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
        }
        val intent = Intent(activity, ABImpressionActivity::class.java)
        intent.putExtra(BundleConstants.BUNDLE_A_B_IMPRESSION, abFace)
        AppUtil.startActivityForResult(
            activity,
            intent,
            RequestCodeConstants.REQUEST_CODE_ME_AB_FACE_IMPRESSION
        )
        reportPoint(MePointAction.MYINFO_ABSIDE_CLICK) {
            actionp2 = getMeEditInfoSourceStrByStatus(data.photoA, data.certStatus)
            actionp3 = getMeEditInfoSourceStrByStatus(data.photoB, data.certStatus)
        }
    }

    override fun clickAnswer(data: ProfileInfoModel.QuestionAnswer) {
        MePageRouter.jumpToEditTextAnswerActivity(data, null)
    }

    override fun clickLoveMarryStatus() {
        AppUtil.startActivityForResult(
            activity,
            MeMarryLoveStatusActivity.createIntent(activity),
            RequestCodeConstants.REQUEST_CODE_MARRY_STATUS
        )
    }

    override fun clickInfo() {
        AppUtil.startActivityForResult(
            activity,
            MeBaseInfoEditActivity.createIntent(activity),
            RequestCodeConstants.REQUEST_CODE_BASE_INFO
        )
    }

    override fun clickWeight(baseInfo: ProfileInfoModel.BaseInfo) {
        activity.showWeightDialog(baseInfo.weight)
    }

    override fun clickEthnickty(bean: ProfileInfoModel.BaseInfo?) {
        if (bean == null) return

        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestEthnicityList()
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<EthnicityListResponse>() {

                override fun onSuccess(data: EthnicityListResponse) {
                    val codes: ArrayList<String> = arrayListOf()
                    val names: ArrayList<String> = arrayListOf()

                    data.result.forEach {
                        codes.add(it.code.toString())
                        names.add(it.name)
                    }

                    activity.showWheelOneSelectDialog(
                        codes,
                        names,
                        bean.ethnicityCode.toString(),
                        "民族"
                    ) { _, code ->
                        saveE(code)
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                    T.ss(reason?.errReason)
                }

            })
    }

    fun saveE(code: String) {
        val params: MutableMap<String, Any> = HashMap()
        params["code"] = code
        HttpExecutor.requestSimplePost(
            URLConfig.URL_USER_UPDATE_ETHNICITY,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {
                    LiveEventBus.post(LivedataKeyMe.USER_TAG_UPDATE_ETHNICITY, true)
                }

                override fun dealFail(reason: ErrorReason?) {
                    T.ss(reason?.errReason)
                }
            })
    }

    override fun clickSmoke(bean: ProfileInfoModel.BaseInfo?) {
        if (bean == null) return
        val names = activity.resources.getStringArray(R.array.matching_array_smoke_name)
        val codes = activity.resources.getStringArray(R.array.matching_array_smoke_code)
        var currentPosition = codes.indexOf(bean.smokingHabit.toString())
        if (currentPosition < 0) {
            currentPosition = -1
        }
        val mDialog: BottomListDialogNew
        val list = mutableListOf<SelectBottomBean>()
        names.forEach {
            list.add(
                SelectBottomBean(it)
            )
        }
        mDialog = BottomListDialogNew.Builder(activity)
            .setData(list)
            .setPadding(0, 0)
            .setShowTitle("是否吸烟")
            .setSelect(currentPosition)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { _, pos, _ ->
                val params: MutableMap<String, Any> = HashMap()
                params["code"] = codes[pos]
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_USER_UPDATE_SMOKING_HABIT,
                    params,
                    object : SimpleRequestCallback() {
                        override fun onSuccess() {
                            LiveEventBus.post(LivedataKeyMe.USER_TAG_UPDATE_SMOKE, true)
                        }

                        override fun dealFail(reason: ErrorReason?) {
                            T.ss(reason?.errReason)
                        }

                    })

            }
            .create()
        mDialog.show()
    }

    override fun clickDrink(bean: ProfileInfoModel.BaseInfo?) {
        if (bean == null) return
        val names = activity.resources.getStringArray(R.array.matching_array_drink_name)
        val codes = activity.resources.getStringArray(R.array.matching_array_drink_code)
        var currentPosition = codes.indexOf(bean.drinkingHabit.toString())
        if (currentPosition < 0) {
            currentPosition = -1
        }
        val mDialog: BottomListDialogNew
        val list = mutableListOf<SelectBottomBean>()
        names.forEach {
            list.add(
                SelectBottomBean(it)
            )
        }
        mDialog = BottomListDialogNew.Builder(activity)
            .setData(list)
            .setPadding(0, 0)
            .setShowTitle("是否喝酒")
            .setSelect(currentPosition)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { _, pos, _ ->
                val params: MutableMap<String, Any> = HashMap()
                params["code"] = codes[pos]
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_USER_UPDATE_DRINKING_HABIT,
                    params,
                    object : SimpleRequestCallback() {
                        override fun onSuccess() {
                            LiveEventBus.post(LivedataKeyMe.USER_TAG_UPDATE_DRINK, true)
                        }

                        override fun dealFail(reason: ErrorReason?) {
                            T.ss(reason?.errReason)
                        }

                    })

            }
            .create()
        mDialog.show()
    }

    override fun clickMarrayStatus(bean: ProfileInfoModel.BaseInfo?) {
        if (bean == null) return
        val names = activity.resources.getStringArray(R.array.matching_array_marry_status_name)
        val codes = activity.resources.getStringArray(R.array.matching_array_marry_status_code)
        var currentPosition = codes.indexOf(bean.maritalStatus.toString())
        if (currentPosition < 0) {
            currentPosition = -1
        }
        val list = mutableListOf<SelectBottomBean>()
        names.forEach {
            list.add(
                SelectBottomBean(it)
            )
        }
        mDialog = BottomListDialogNew.Builder(activity)
            .setData(list)
            .setPadding(0, 0)
            .setShowTitle("婚姻状况")
            .setSelect(currentPosition)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { view, pos, bottomBean ->
                if (currentPosition == pos) {
                    mDialog?.dismiss()
                    return@setOnBottomItemClickListener
                }
                val params: MutableMap<String, Any> = HashMap()
                params["status"] = codes[pos]
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_USER_UPDATE_MARITAL_STATUS,
                    params,
                    object : SimpleRequestCallback() {
                        override fun onSuccess() {
                            if (bean.marriageCertStatus == 3) {
                                activity.showTwoButtonDialog(
                                    title = "温馨提示",
                                    content = "您已认证过婚姻状态信息，修改后婚姻状态认证会自动取消，是否继续？",
                                    cancelable = false,
                                    canceledOnTouchOutside = false,
                                    positiveText = "继续",
                                    negativeText = "取消",
                                    positiveButtonClick = {
                                        LiveEventBus.post(
                                            LivedataKeyMe.LOVE_MARRY_STATUS,
                                            true
                                        )
                                    },
                                    negativeButtonClick = {
                                    })
                            } else if (bean.marriageCertStatus == 1) {
                                T.ss("认证审核进行中，暂不支持修改")
                            } else {
                                LiveEventBus.post(LivedataKeyMe.LOVE_MARRY_STATUS, true)
                            }
                        }

                        override fun dealFail(reason: ErrorReason?) {
                            T.ss(reason?.errReason)
                        }

                    })

            }
            .create()
        mDialog?.show()
    }

    override fun clickBabyPlan(bean: ProfileInfoModel.BaseInfo?) {
        if (bean == null) return
        val names = activity.resources.getStringArray(R.array.matching_array_baby_love_name)
        val codes = activity.resources.getStringArray(R.array.matching_array_baby_love_code)
        var currentPosition = codes.indexOf(bean.childbearingPreference.toString())
        if (currentPosition < 0) {
            currentPosition = -1
        }
        val mDialog: BottomListDialogNew
        val list = mutableListOf<SelectBottomBean>()
        names.forEach {
            list.add(
                SelectBottomBean(it)
            )
        }
        mDialog = BottomListDialogNew.Builder(activity)
            .setData(list)
            .setPadding(0, 0)
            .setShowTitle("是否要孩子")
            .setSelect(currentPosition)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { view, pos, bottomBean ->
                val params: MutableMap<String, Any> = HashMap()
                params["code"] = codes[pos]
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_USER_UPDATE_CHILD_PREFERENCE,
                    params,
                    object : SimpleRequestCallback() {
                        override fun onSuccess() {
                            LiveEventBus.post(LivedataKeyMe.LOVE_BABY_LOVE, true)
                        }

                        override fun dealFail(reason: ErrorReason?) {
                            T.ss(reason?.errReason)
                        }

                    })

            }
            .create()
        mDialog.show()
    }

    override fun clickLovePlan(bean: ProfileInfoModel.BaseInfo?, love: LoveGoalListBean?) {
        if (bean == null) return
        if (love == null) return
        var currentPosition = -1
        var i = 0
        val list = mutableListOf<SelectBottomBean>()
        love.loveGoalList?.forEach { it: LoveGoal? ->
            if (it?.code == bean.loveGoal) {
                currentPosition = i
            }
            list.add(
                SelectBottomBean(it?.name).setCode(it?.code ?: 0).setRecommend(it?.suggest == true)
            )
            i++
        }
        if (currentPosition < 0) {
            currentPosition = -1
        }
        val mDialog: BottomListDialogNew = BottomListDialogNew.Builder(activity)
            .setData(list)
            .setPadding(0, 0)
            .setShowTitle("恋爱目标")
            .setSelect(currentPosition)
            .setCanceledOnTouchOutside(true)
            .setOnBottomItemClickListener { _, pos, _ ->
                val params: MutableMap<String, Any> = HashMap()
                params["code"] = list[pos].code
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_USER_UPDATE_LOVE_GOAL,
                    params,
                    object : SimpleRequestCallback() {
                        override fun onSuccess() {
                            LiveEventBus.post(LivedataKeyMe.LOVE_GOAL_UPDATE, true)
                        }

                        override fun dealFail(reason: ErrorReason?) {
                            T.ss(reason?.errReason)
                        }

                    })

            }
            .create()
        mDialog.show()
    }

    override fun clickDeletePic(story: BaseStoryShowItem, nowAllCount: Int) {
        reportPoint(MePointAction.MYINFO_LIFEPHOTO_CLICK) {
            this.type = "删除"
            this.status = getMeEditInfoSourceStrByStatus(story.url, story.certStatus.get())
            this.count = nowAllCount
            this.actionp2 = "打开"
        }
        if (TextUtils.isEmpty(story.id)) { //未生成故事
            viewModel?.deleteStoryLocalData(story)
        } else {
            viewModel?.deleteStory(story)
        }
    }

    override fun clickRetryStory(data: PicStoryItem, nowAllCount: Int) {
        data.setAddFailed(false)
        if (data.upLoadPercent.get() >= 100) { //文件上传成功
            viewModel?.addPicStory(data)
        } else {
            viewModel?.upLoadImage(data)
        }
    }

    override fun clickRetryStory(data: VideoStoryItem, nowAllCount: Int) {
        data.setAddFailed(false)
        if (data.upLoadPercent.get() >= 100) { //文件上传成功
            viewModel?.addVideoStory(data)
        } else {
            viewModel?.upLoadIVideo(data)
        }
    }

    override fun clickRetryDown(data: BaseStoryShowItem, imageView: OImageView) {
        data.setDownLoadFailed(false)
        data.setDownLoadPercent(0)
        viewModel?.downLoadItemFile(data, imageView)
    }

    private fun addStory(max: Int) {

        selectStoryFile(max)
    }

    private fun selectStoryFile(count: Int) {
        PhotoSelectManager.jumpForGalleryMultipleCropResult(
            activity as FragmentActivity,
            count,
            object : AvoidOnResult.Callback {
                override fun onActivityResult(
                    requestCode: Int,
                    resultCode: Int,
                    data: Intent?
                ) {
                    if (resultCode == Activity.RESULT_OK) {
                        val files = Matisse.obtainResult(data)

                        if (LList.isEmpty(files)) {
                            return
                        }

                        viewModel?.selectFiles = files
                        val storyListEditFragment = StoryListEditFragmentNew3.newInstance(
                            StoryListEditFragmentNew3.TYPE_ADD, viewModel
                        ).apply {
                            // 记录最多可选的图片数量
                            setMaxImageCount(count)
                        }

                        activity.supportFragmentManager.beginTransaction()
                            .setCustomAnimations(
                                R.anim.common_activity_slide_from_right_to_left_enter,
                                0,
                                0,
                                R.anim.common_activity_slide_from_left_to_right_exit
                            )
                            .add(
                                if (isNew)R.id.fragment_container2 else R.id.fragment_container,
                                storyListEditFragment
                            )
                            .addToBackStack(null)
                            .commitAllowingStateLoss()
                    }
                }
            })
    }

    private fun reEditRejectedStoryFiles(baseStoryShowItem: BaseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.id) && !baseStoryShowItem.addFailed.get()) {
            var textReject = baseStoryShowItem.textReject
            var photoReject = baseStoryShowItem.getPhotoReject()
            if (baseStoryShowItem.certStatus.get() == ProfileMetaModel.STATUS_REJECTED && textReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && photoReject != ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                textReject =
                    if (!TextUtils.isEmpty(baseStoryShowItem.storyText)) ProfileInfoModel.CONTENT_TYPE_REJECTED else ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
                photoReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
            }

            viewModel?.reEditSelectId = baseStoryShowItem.id
            (activity as FragmentActivity).supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.common_activity_slide_from_right_to_left_enter,
                    0,
                    0,
                    R.anim.common_activity_slide_from_left_to_right_exit
                )
                .add(
                    if (isNew)R.id.fragment_container2 else R.id.fragment_container,
                    StoryListEditFragmentNew3.newInstance(
                        StoryListEditFragmentNew3.TYPE__REJECTED_EDIT,
                        textReject,
                        photoReject,
                        baseStoryShowItem.certInfo,
                        viewModel
                    )
                )
                .addToBackStack(null)
                .commitAllowingStateLoss()
        }
    }

    private fun reEdit(baseStoryShowItem: BaseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.id) && !baseStoryShowItem.addFailed.get()) {
            viewModel?.reEditSelectId = baseStoryShowItem.id
            (activity as FragmentActivity).supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.common_activity_slide_from_right_to_left_enter,
                    0,
                    0,
                    R.anim.common_activity_slide_from_left_to_right_exit
                )
                .add(
                    if (isNew)R.id.fragment_container2 else R.id.fragment_container,
                    StoryListEditFragmentNew3.newInstance(
                        StoryListEditFragmentNew3.TYPE_EDIT, viewModel
                    ), "StoryListEditFragmentNew"
                )
                .addToBackStack(null)
                .commitAllowingStateLoss()
        }
    }

}