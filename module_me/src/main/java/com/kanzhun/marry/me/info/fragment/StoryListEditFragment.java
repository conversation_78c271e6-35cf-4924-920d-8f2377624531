package com.kanzhun.marry.me.info.fragment;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.transition.Fade;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.util.Pair;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.foundation.utils.PathUtil;
import com.kanzhun.foundation.utils.StringUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.common.util.BitmapUtils;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.views.LinearLayoutManagerWrapper;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.FragmentBackHandler;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.bean.BaseStoryEditItem;
import com.kanzhun.foundation.page.VideoAudioFloatPageManager;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentStoryListEditBinding;
import com.kanzhun.marry.me.databinding.MeItemPicStoryCropBinding;
import com.kanzhun.marry.me.databinding.MeItemPicStoryEditBinding;
import com.kanzhun.marry.me.databinding.MeItemVideoStoryCropBinding;
import com.kanzhun.marry.me.databinding.MeItemVideoStoryEditBinding;
import com.kanzhun.marry.me.info.activity.StoryTextEditActivity;
import com.kanzhun.marry.me.info.bean.PicStoryEditItem;
import com.kanzhun.marry.me.info.bean.VideoStoryEditItem;
import com.kanzhun.marry.me.info.callback.StoryListEditCallback;
import com.kanzhun.marry.me.info.viewmodel.StoryFileSelectViewModel;
import com.kanzhun.marry.me.info.viewmodel.StoryListEditViewModel;
import com.kanzhun.marry.me.views.MeStoryUCropView;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.file.FileUtils;
import com.kanzhun.utils.rxbus.RxBus;
import com.yalantis.ucrop.UCrop;
import com.yalantis.ucrop.UCropFragment;
import com.yalantis.ucrop.UCropFragmentCallback;

import org.alita.config.AfantyConfig;
import org.alita.core.AlitaMediaCore;
import org.alita.core.AlitaPlayer;
import org.alita.webrtc.VideoRenderer;

import java.io.File;
import java.util.List;

public class StoryListEditFragment extends FoundationVMShareFragment<MeFragmentStoryListEditBinding, StoryListEditViewModel, StoryFileSelectViewModel> implements StoryListEditCallback, FragmentBackHandler {
    public static final String TAG = "StoryListEditFragment";
    BaseBinderAdapter adapter;
    BaseBinderAdapter pagerAdapter;
    public static final int TYPE_ADD = 0;//添加新的故事
    public static final int TYPE_EDIT = 1;//对原有的故事重新编辑
    public static final int TYPE__REJECTED_EDIT = 2;//对审核拒绝的故事重新编辑

    int type;
    AlitaPlayer player;
    VideoRenderer mVideoRender;

    public static StoryListEditFragment newInstance(int type) {
        return newInstance(type, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED,"");
    }

    public static StoryListEditFragment newInstance(int type, int textRejectType, int photoRejectType,String certInfo) {
        StoryListEditFragment fragment = new StoryListEditFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_TYPE, type);
        bundle.putInt(BundleConstants.BUNDLE_TEXT_REJECT_TYPE, textRejectType);
        bundle.putInt(BundleConstants.BUNDLE_PHOTO_REJECT_TYPE, photoRejectType);
        bundle.putString(BundleConstants.BUNDLE_STATURE, certInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_story_list_edit;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getViewModel().init(getArguments().getInt(BundleConstants.BUNDLE_TEXT_REJECT_TYPE, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED),
                getArguments().getInt(BundleConstants.BUNDLE_PHOTO_REJECT_TYPE, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED),getArguments().getString(BundleConstants.BUNDLE_STATURE,""));
        player = new AlitaPlayer(getContext());
        getDataBinding().svPlayer.init(AlitaMediaCore.getInstance().getEglInstance().getEglBaseContext(), null);
        mVideoRender = new VideoRenderer(getDataBinding().svPlayer);
        player.setRenderer(mVideoRender.GetRenderPointer());
        AfantyConfig config = new AfantyConfig();
        config.enableHardwareDecode = true;
        player.setConfig(config);
        player.setLoop(true);
        getDataBinding().clContent.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        RxBus.getInstance().subscribe(this, TAG, new RxBus.Callback<String>() {

            @Override
            public void onEvent(String s) {
                getViewModel().setCurrentEditContent(s);
                int index = getViewModel().getSelectIndex().get();
                List<BaseStoryEditItem> baseStoryItems = getViewModel().getStoryEditLiveData().getValue();
                if (!LList.isEmpty(baseStoryItems) && index < baseStoryItems.size()) {
                    BaseStoryEditItem baseStoryItem = baseStoryItems.get(index);
                    baseStoryItem.setChangedStoryText(s);
                    if (getViewModel().getOriginTextRejectType() == ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                        getViewModel().getTextRejectType().set(TextUtils.equals(s, baseStoryItem.getStoryText()) ? ProfileInfoModel.CONTENT_TYPE_REJECTED : ProfileInfoModel.CONTENT_TYPE_UN_REJECTED);
                    }
                }
            }
        });
        initBottomAdapter();
        initPagerAdapter();
        getViewModel().getStoryEditLiveData().observe(this, new Observer<List<BaseStoryEditItem>>() {
            @Override
            public void onChanged(List<BaseStoryEditItem> baseStoryItems) {
                if (LList.isEmpty(baseStoryItems)) {
                    return;
                }
                int selectIndex = getViewModel().getSelectIndex().get();
                pagerAdapter.setList(baseStoryItems);
                getDataBinding().storyContent.setOffscreenPageLimit(baseStoryItems.size());
                getDataBinding().storyContent.setCurrentItem(selectIndex, false);
                if (baseStoryItems.size() > 1) {
                    getDataBinding().rvStory.setVisibility(View.VISIBLE);
                    adapter.setList(baseStoryItems);
                } else {
                    getDataBinding().rvStory.setVisibility(View.GONE);
                }
                getViewModel().setCount(baseStoryItems.size());
                ExecutorFactory.execMainTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        play(selectIndex);
                        getDataBinding().rvStory.smoothScrollToPosition(selectIndex);
                    }
                }, 200);
            }
        });
        type = getArguments().getInt(BundleConstants.BUNDLE_TYPE);
        if (type == TYPE_ADD) {
            getViewModel().loadStoryFiles(getActivityViewModel().getSelectFiles());
        } else if (type == TYPE_EDIT || type == TYPE__REJECTED_EDIT) {
            getViewModel().loadStoryReEditFiles(getActivityViewModel().getStorySelectLiveData().getValue(), getActivityViewModel().getReEditSelectId());
        }

        requireActivity().getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                onBackPressed();
            }
        });
        getDataBinding().idLeftIcon.post(new Runnable() {
            @Override
            public void run() {
                getDataBinding().idLeftIcon.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onBackPressed();
                    }
                });
            }
        });

    }

    private void initPagerAdapter() {
        pagerAdapter = new BaseBinderAdapter();
        pagerAdapter.addItemBinder(PicStoryEditItem.class, new BaseDataBindingItemBinder<PicStoryEditItem, MeItemPicStoryCropBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_pic_story_crop;
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_voice, R.id.tv_reselect);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemPicStoryCropBinding> holder, @NonNull View view, PicStoryEditItem data, int position) {
                int id = view.getId();
                if (id == R.id.tv_reselect) {
                    PhotoSelectManager.jumpForGalleryFromResult((FragmentActivity) activity, 1, PhotoSelectManager.SELECT_MIME_TYPE_IMAGE, true, new PhotoSelectManager.OnGalleryCountCallBack() {
                        @Override
                        public void onGalleryListener(List<Uri> files, boolean originalEnable) {
                            if (LList.isEmpty(files)) {
                                return;
                            }
                            getViewModel().reSelectLoadStoryFiles(files, getViewModel().getSelectIndex().get());
                        }
                    });
                }
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemPicStoryCropBinding> holder, MeItemPicStoryCropBinding binding, PicStoryEditItem item) {
                Bundle cropOptionsBundle = new Bundle();
                cropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, item.getFile());
//                Uri destination = Uri.fromFile(getViewModel().getPictureOutputFile(TAG + System.currentTimeMillis()));
                Uri destination = Uri.fromFile(PathUtil.getPictureOutputFile(activity, item.getFile(), TAG + System.currentTimeMillis()));
                cropOptionsBundle.putParcelable(UCrop.EXTRA_OUTPUT_URI, destination);
                item.setCropView(binding.cropView);
                binding.cropView.setSetupInitialImageTypeByImageMinScale(type == TYPE_ADD ? true : false);
                binding.cropView.initData(cropOptionsBundle);
                binding.cropView.setCallback(new UCropFragmentCallback() {
                    @Override
                    public void loadingProgress(boolean showLoader) {

                    }

                    @Override
                    public void onCropFinish(UCropFragment.UCropResult result) {

                    }
                });
            }
        });
        pagerAdapter.addItemBinder(VideoStoryEditItem.class, new BaseDataBindingItemBinder<VideoStoryEditItem, MeItemVideoStoryCropBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_video_story_crop;
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_voice, R.id.tv_reselect);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemVideoStoryCropBinding> holder, @NonNull View view, VideoStoryEditItem data, int position) {
                int id = view.getId();
                if (id == R.id.iv_voice) {
                    boolean newVoiceStatus = !data.getSilence().get();
                    if (!VideoAudioFloatPageManager.getInstance().hasNoFloatPage() && !newVoiceStatus) {
                        T.ss("您正在通话中，请稍后再试");
                        return;
                    }
                    if (player != null) {
                        data.setSilence(newVoiceStatus);
                        player.setMute(newVoiceStatus);
                    }
                } else if (id == R.id.tv_reselect) {
                    PhotoSelectManager.jumpForGalleryFromResult((FragmentActivity) activity, 1, PhotoSelectManager.SELECT_MIME_TYPE_IMAGE, true, new PhotoSelectManager.OnGalleryCountCallBack() {
                        @Override
                        public void onGalleryListener(List<Uri> files, boolean originalEnable) {
                            if (LList.isEmpty(files)) {
                                return;
                            }
                            getViewModel().reSelectLoadStoryFiles(files, getViewModel().getSelectIndex().get());
                        }
                    });
                }
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemVideoStoryCropBinding> holder, MeItemVideoStoryCropBinding binding, VideoStoryEditItem item) {
                binding.setBean(item);
            }
        });
        getDataBinding().storyContent.setAdapter(pagerAdapter);
        getDataBinding().storyContent.setUserInputEnabled(false);
        getDataBinding().storyContent.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                play(position);
            }
        });
    }

    private void play(int position) {
        List editItems = pagerAdapter.getData();
        if (LList.isEmpty(editItems) || position >= editItems.size()) {
            return;
        }
        Object o = editItems.get(position);
        Log.e(TAG, "play obj=" + o);
        if (o instanceof VideoStoryEditItem) {
            getDataBinding().svPlayer.setVisibility(View.VISIBLE);
            VideoStoryEditItem video = (VideoStoryEditItem) o;
            String path = FileUtils.getPath(getContext(), video.getFile());
            if (!TextUtils.isEmpty(path) && player != null) {
                player.pause();
                player.setMute(video.getSilence().get());
                player.startPlay(path);
            }
        } else {
            getDataBinding().svPlayer.setVisibility(View.GONE);
            if (player != null) {
                player.pause();
            }
        }
        if (o instanceof BaseStoryEditItem) {
            getViewModel().setCurrentEditContent(((BaseStoryEditItem) o).getChangedStoryText());
        }
    }

    private void initBottomAdapter() {
        adapter = new BaseBinderAdapter() {
            @Override
            public void onBindViewHolder(@NonNull BaseViewHolder holder, int position) {
                super.onBindViewHolder(holder, position);
                holder.getBinding().setVariable(BR.index, position);

            }
        };
        getDataBinding().rvStory.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = QMUIDisplayHelper.dpToPx(8);
            }
        });
        adapter.addItemBinder(PicStoryEditItem.class, new BaseDataBindingItemBinder<PicStoryEditItem, MeItemPicStoryEditBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_pic_story_edit;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemPicStoryEditBinding> holder, MeItemPicStoryEditBinding binding, PicStoryEditItem item) {
                binding.setBean(item);
                binding.setSelectIndex(getViewModel().getSelectIndex());
                if (item.getFile() != null) {
                    binding.ovStory.loadRoundUri(item.getFile());
                }
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemPicStoryEditBinding> holder, @NonNull View view, PicStoryEditItem data, int position) {
                getViewModel().setSelectIndex(position);
                getDataBinding().storyContent.setCurrentItem(position, false);
            }
        });
        adapter.addItemBinder(VideoStoryEditItem.class, new BaseDataBindingItemBinder<VideoStoryEditItem, MeItemVideoStoryEditBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_video_story_edit;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemVideoStoryEditBinding> holder, MeItemVideoStoryEditBinding binding, VideoStoryEditItem item) {
                binding.setBean(item);
                binding.setSelectIndex(getViewModel().getSelectIndex());
                if (item.getFile() != null) {
                    binding.ovStory.loadRoundUri(item.getFile());
                }
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemVideoStoryEditBinding> holder, @NonNull View view, VideoStoryEditItem data, int position) {
                getViewModel().setSelectIndex(position);
                getDataBinding().storyContent.setCurrentItem(position, false);
            }
        });
        LinearLayoutManagerWrapper linearLayoutManager = new LinearLayoutManagerWrapper(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        getDataBinding().rvStory.setLayoutManager(linearLayoutManager);
        getDataBinding().rvStory.setAdapter(adapter);
    }

    @Override
    public void clickLeft(View view) {
        onBackPressed();
    }

    @Override
    public void clickRight(View view) {
        if (type == TYPE_ADD) {
            getActivityViewModel().upLoadAddStoryFile(getViewModel().getStoryEditLiveData().getValue());
        } else if (type == TYPE_EDIT || type == TYPE__REJECTED_EDIT) {
            getActivityViewModel().upLoadStoryReEditFile(getViewModel().getStoryEditLiveData().getValue());
        }
        ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();

    }

    @Override
    public void clickAddStoryContent() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                int index = getViewModel().getSelectIndex().get();
                List<BaseStoryEditItem> cache = getViewModel().getStoryEditLiveData().getValue();
                if (LList.isEmpty(cache) || cache.size() <= index) {
                    return;
                }
                BaseStoryEditItem baseStoryEditItem = cache.get(index);
                Bitmap bitmap = null;
                if (baseStoryEditItem instanceof PicStoryEditItem) {
                    MeStoryUCropView cropView = (MeStoryUCropView) pagerAdapter.getViewByPosition(index, R.id.crop_view);
                    bitmap = BitmapUtils.getCacheBitmapFromView(cropView);
                } else if (baseStoryEditItem instanceof VideoStoryEditItem) {
                    bitmap = FileUtils.getVideoThumbnailBitmap(getContext(), baseStoryEditItem.getFile());
                }
                if (bitmap != null) {
//                    File file = new File(getViewModel().getPictureOutputFile(TAG + index).getPath());
                    File file = new File(PathUtil.getPictureOutputFile(activity, null, TAG + index).getPath());
                    if (file.exists()) {
                        file.delete();
                    }
                    file = FileUtils.saveBitmapToFile(bitmap, PathUtil.getPictureOutputFile(activity, null, System.currentTimeMillis() + "").getPath());
                    if (file == null) {
                        return;
                    }
                    File finalFile = file;
                    ExecutorFactory.execMainTask(new Runnable() {
                        @Override
                        public void run() {
                            android.transition.Fade fade = new Fade(); //渐隐
                            fade.setDuration(300);
                            setReenterTransition(fade);
                            setExitTransition(fade);
                            setAllowEnterTransitionOverlap(false);
                            setAllowReturnTransitionOverlap(false);
                            Intent intent = StoryTextEditActivity.createIntent(activity, Uri.fromFile(finalFile), getViewModel().getCurrentEditContent().get(), TAG);
                            Pair<View, String>[] pairs = new Pair[]{Pair.create(getDataBinding().storyContent, ViewCompat.getTransitionName(getDataBinding().storyContent)), Pair.create(getDataBinding().tvCurrentStoryContent, ViewCompat.getTransitionName(getDataBinding().tvCurrentStoryContent))};
                            ActivityOptionsCompat transitionActivityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(activity, pairs);
                            startActivity(intent, transitionActivityOptions.toBundle());
                        }
                    });
                }
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        List baseStoryEditItems = pagerAdapter.getData();
        if (!LList.isEmpty(baseStoryEditItems)) {
            int selectIndex = getViewModel().getSelectIndex().get();
            if (selectIndex < baseStoryEditItems.size()) {
                Object o = baseStoryEditItems.get(selectIndex);
                if (o instanceof VideoStoryEditItem) {
                    if (player != null) {
                        player.resume();
                    }
                }
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (player != null) {
            player.pause();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
        if (this.player != null) {
            this.player.destroy();
        }
        if (mVideoRender != null) {
            mVideoRender.dispose();
        }
        getDataBinding().svPlayer.release();
    }

    @Override
    public boolean onBackPressed() {
        if (!TextUtils.equals(getViewModel().preIntroText, StringUtil.trimEAndN(getViewModel().getCurrentEditContent().get()))) {
            showExitDialog();
        } else {
            ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
        }
        return true;
    }

    private void showExitDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(activity)
                .setTitle(getString(R.string.me_intro_text_dialog_title))
                .setPositiveText(getResources().getString(R.string.me_intro_text_dialog_p))
                .setNegativeText(getResources().getString(R.string.me_intro_text_dialog_n))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {

                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {
                        ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
                    }
                });
        builder.create().show();
    }
}