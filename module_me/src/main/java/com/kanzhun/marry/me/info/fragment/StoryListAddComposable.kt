package com.kanzhun.marry.me.info.fragment

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import com.common.AvoidOnResult
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.foundation.bean.BaseStoryEditItem
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.info.viewmodel.StoryListEditViewModel3
import com.kanzhun.utils.base.LList
import com.zhihu.matisse.Matisse
import lib.twl.picture.util.Const
import java.io.File
import java.util.Collections

fun bindStoryListAdd(
    composeView: ComposeView,
    viewModel: StoryListEditViewModel3,
    maxImageCount: Int = 0,
) {
    bindStoryListAdd(
        composeView = composeView,
        viewModel = viewModel,
        maxImageCount = maxImageCount,
        enableReselect = true,
        onReselect = { index ->
            PhotoSelectManager.jumpForGallerySingleCropOnlyResult(
                composeView.context as FragmentActivity,
                object : AvoidOnResult.Callback {
                    override fun onActivityResult(
                        requestCode: Int,
                        resultCode: Int,
                        data: Intent?
                    ) {
                        if (resultCode == RESULT_OK && data != null) {
                            val path = data.getStringExtra(Const.EXTRA_IMAGE_SAVE_PATH)
                            if (path != null) {
                                val pathUri = Uri.fromFile(File(path))
                                val uris = ArrayList<Uri>()
                                uris.add(pathUri)

                                if (LList.getCount(uris) == 1) {
                                    val source = uris[0]
                                    viewModel.reSelectLoadStoryFiles(
                                        Collections.singletonList(
                                            source
                                        ), index
                                    )
                                }
                            }
                        }

                    }
                }
            )
        })
}

fun bindStoryListAdd(
    composeView: ComposeView,
    viewModel: StoryListEditViewModel3,
    maxImageCount: Int = 0,
    enableReselect: Boolean = false,
    onReselect: (index: Int) -> Unit = {},
) {
    composeView.onSetWindowContent {
        val storyEditItems by viewModel.storyEditLiveData.observeAsState(emptyList<BaseStoryEditItem>())
        val selectedItem by viewModel.selectIndex.observeAsState(0)

        ImageCardList(
            storyEditItems = storyEditItems,
            maxImageCount = maxImageCount,
            selectedItem = selectedItem,
            onItemSelected = {
                viewModel.setSelectIndex(it)
            },
            enableRemove = false,
            onRemove = {
                viewModel.removeStoryEditItem(it)
            },
            enableAdd = true,
            enableReselect = enableReselect,
            onReselect = onReselect,
            onAddImage = {
                PhotoSelectManager.jumpForGalleryMultipleCropResult(
                    composeView.context as FragmentActivity,
                    maxImageCount - storyEditItems.size,
                    object : AvoidOnResult.Callback {
                        override fun onActivityResult(
                            requestCode: Int,
                            resultCode: Int,
                            data: Intent?
                        ) {
                            if (resultCode == RESULT_OK) {
                                val files = Matisse.obtainResult(data)

                                if (LList.isEmpty(files)) {
                                    return
                                }

                                viewModel.appendStoryFiles(files)
                            }
                        }
                    }
                )
            }
        )
    }
}

@Composable
private fun ImageCardList(
    storyEditItems: List<BaseStoryEditItem> = emptyList<BaseStoryEditItem>(),
    maxImageCount: Int = 0,
    selectedItem: Int = 0,
    onItemSelected: (index: Int) -> Unit = { _ -> },
    enableRemove: Boolean = false,
    onRemove: (index: Int) -> Unit = { _ -> },
    enableReselect: Boolean = false,
    onReselect: (index: Int) -> Unit = {},
    enableAdd: Boolean = false,
    onAddImage: () -> Unit = {},
    inPreview: Boolean = false,
) {
    LazyRow(
        modifier = Modifier
            .conditional(storyEditItems.size > 1) {
                height(216.dp)
            },
        contentPadding = PaddingValues(horizontal = 20.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        itemsIndexed(items = storyEditItems) { index, item ->
            Card(
                storyEditItem = item,
                isSelected = selectedItem == index,
                dimenCompare =
                    if (item.dimens?.size == 2
                        && ((item.dimens?.get(0) ?: 0) > 0 && (item.dimens?.get(1)
                            ?: 0) > 0)
                    ) {
                        val width = item.dimens?.get(0) ?: 0
                        val height = item.dimens?.get(1) ?: 0
                        if (width > height) 1 else if (width == height) 0 else -1
                    } else {
                        -1
                    },
                onClick = {
                    if (selectedItem != index) {
                        onItemSelected(index)
                    }
                },
                enableRemove = enableRemove,
                onRemove = {
                    onRemove(index)
                },
                enableReselect = enableReselect,
                onReselect = {
                    onReselect(index)
                },
                inPreview = inPreview
            )
        }

        if (enableAdd && storyEditItems.size < maxImageCount) {
            item {
                CardAdd(
                    onAddImage = onAddImage
                )
            }
        }
    }
}

/**
 * 3:4/4:3
 */
@Composable
private fun Card(
    storyEditItem: BaseStoryEditItem = BaseStoryEditItem(),
    isSelected: Boolean = false,
    dimenCompare: Int = 0, // 1: 横图；0：正方形；-1：竖图
    onClick: () -> Unit = {},
    enableRemove: Boolean = false,
    onRemove: () -> Unit = {},
    enableReselect: Boolean = false,
    onReselect: () -> Unit = {},
    inPreview: Boolean = false
) {
    val colorStops = arrayOf(
        0.0f to Color(0xFF69A0FC),
        1f to Color(0xFFF78DFF)
    )

    val bgWidth = if (dimenCompare >= 0) 200.dp else 150.dp
    val bgHeight = if (dimenCompare <= 0) 200.dp else 150.dp
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .conditional(isSelected) {
                border(
                    width = 4.dp,
                    brush = Brush.verticalGradient(colorStops = colorStops),
                    shape = RoundedCornerShape(16.dp)
                )
            }
            .width(bgWidth)
            .height(bgHeight)
            .noRippleClickable {
                onClick()
            }
    ) {
        val color = colorResource(R.color.common_color_000000_30)
        if (inPreview) {
            Spacer(
                modifier = Modifier
                    .width(bgWidth)
                    .height(bgHeight)
                    .clip(RoundedCornerShape(16.dp))
                    .background(color = color)
                    .align(alignment = Alignment.Center)
            )
        } else {
            OImageView2(
                imageUrl = storyEditItem.file?.toString(),
                modifier = Modifier
                    .width(bgWidth)
                    .height(bgHeight)
                    .clip(RoundedCornerShape(16.dp))
                    .align(alignment = Alignment.Center)
            )
        }

        Image(
            painter = painterResource(id = R.mipmap.me_ic_edit),
            contentDescription = "image description",
            modifier = Modifier
                .align(alignment = Alignment.TopEnd)
                .padding(8.dp)
                .size(24.dp)
        )

        if (enableRemove) {
            Image(
                painter = painterResource(id = R.mipmap.me_ic_close),
                contentDescription = "image description",
                modifier = Modifier
                    .align(alignment = Alignment.TopEnd)
                    .conditional(inPreview) {
                        alpha(0.3f)
                    }
                    .noRippleClickable {
                        onRemove()
                    }
            )
        }

        if (enableReselect) {
            Row(
                modifier = Modifier
                    .align(alignment = Alignment.BottomEnd)
                    .padding(8.dp)
                    .background(color = Color(0x66191919), shape = RoundedCornerShape(64.dp))
                    .padding(horizontal = 12.dp, vertical = 6.dp)
                    .noRippleClickable {
                        onReselect()
                    },
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(id = R.drawable.me_ic_story_reselect),
                    contentDescription = "image description",
                    modifier = Modifier
                )

                Text(
                    text = "重选",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
        }
    }
}

@Composable
private fun CardAdd(
    modifier: Modifier = Modifier,
    onAddImage: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .noRippleClickable(onClick = onAddImage)
    ) {
        Image(
            painter = painterResource(id = R.mipmap.me_ic_image_add),
            contentDescription = null,
            modifier = Modifier
                .width(150.dp)
                .height(200.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewCard() {
    Column {
        Card(isSelected = true, dimenCompare = 1, enableRemove = false, inPreview = true)
        Card(dimenCompare = 0, enableRemove = true, inPreview = true)
        Card(dimenCompare = -1, enableRemove = true, inPreview = true)
        CardAdd()
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewImageCardList() {
    ImageCardList(
        storyEditItems = mutableListOf<BaseStoryEditItem>().apply {
            add(BaseStoryEditItem().apply {
                dimens = intArrayOf(280, 160)
            })

            add(BaseStoryEditItem().apply {
                dimens = intArrayOf(280, 280)
            })

            add(BaseStoryEditItem().apply {
                dimens = intArrayOf(120, 160)
            })
        },
        maxImageCount = 6,
        enableRemove = false,
        enableAdd = true,
        enableReselect = true,
        inPreview = true
    )
}