package com.kanzhun.marry.me.info.activity;

import static com.kanzhun.foundation.Constants.FEMALE_DEFAULT_WEIGHT_KG;
import static com.kanzhun.foundation.Constants.MALE_DEFAULT_WEIGHT_KG;
import static com.kanzhun.foundation.Constants.MAX_WEIGHT_KG;
import static com.kanzhun.foundation.Constants.MIN_WEIGHT_KG;

import android.os.Bundle;
import android.view.View;

import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.views.wheel.pick.adapter.ArrayWheelAdapter;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityWeightEditBinding;
import com.kanzhun.marry.me.info.callback.MeStatureEditCallback;
import com.kanzhun.marry.me.info.viewmodel.MeWeightEditViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 我的基本信息 - 体重
 * <p>
 * Created by Qu Zhiyong on 2022/4/20
 */
public class MeWeightEditActivity extends FoundationVMActivity<MeActivityWeightEditBinding, MeWeightEditViewModel> implements MeStatureEditCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_weight_edit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);
        getViewModel().initData();

        List<String> list = new ArrayList<>();
        list.add("40kg以下");
        for (int i = MIN_WEIGHT_KG; i <= MAX_WEIGHT_KG; i++) {
            list.add(i+"kg");
        }
        list.add("120kg以上");

        getDataBinding().wvStature.setAdapter(new ArrayWheelAdapter(list));

        int currentPosition;
        if (getViewModel().initStature >= MIN_WEIGHT_KG && getViewModel().initStature <= MAX_WEIGHT_KG) {
            currentPosition = getViewModel().initStature - MIN_WEIGHT_KG + 1;
        } else if(getViewModel().initStature < MIN_WEIGHT_KG){
            currentPosition = 0;
        } else if(getViewModel().initStature > MAX_WEIGHT_KG){
            currentPosition = list.size() - 1;
        }else {
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            int gender = user.getGender();
            currentPosition = gender == Account.ACCOUNT_GENDER_MEN ?
                    MALE_DEFAULT_WEIGHT_KG - MIN_WEIGHT_KG : FEMALE_DEFAULT_WEIGHT_KG - MIN_WEIGHT_KG;
            currentPosition++;
        }
        getDataBinding().wvStature.setCurrentItem(currentPosition);// 初始化时显示的数据

        getViewModel().getUpdateSuccessLivaData().observe(this, stature -> {
            if (getViewModel().indexObservable.get() > 0) {
                ProfileHelper.getInstance().checkJump(MeWeightEditActivity.this, getViewModel().indexObservable.get() + 1);
            } else {
                setResult(RESULT_OK);
                AppUtil.finishActivity(MeWeightEditActivity.this);
            }
        });
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(MeWeightEditActivity.this, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        int realStature = getDataBinding().wvStature.getCurrentItem() + MIN_WEIGHT_KG - 1;

        if (getViewModel().indexObservable.get() > 0) {
            getViewModel().updateWeight(realStature);
        } else {
            if (realStature != getViewModel().initStature) {// 只有身高改变时才更新
                getViewModel().updateWeight(realStature);
            } else {
                AppUtil.finishActivity(MeWeightEditActivity.this);
            }
        }
    }
}