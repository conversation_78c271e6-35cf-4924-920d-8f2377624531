package com.kanzhun.marry.me.info.fragment;

import android.view.View;

import androidx.lifecycle.Observer;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.callback.TitleBarOnlyLeftCallback;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentIndustrySelectBinding;
import com.kanzhun.marry.me.databinding.MeItemIndustryBinding;
import com.kanzhun.marry.me.info.viewmodel.IndustrySelectFragmentViewModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/13.
 */
public class IndustrySelectFragment extends FoundationVMFragment<MeFragmentIndustrySelectBinding, IndustrySelectFragmentViewModel> implements TitleBarOnlyLeftCallback {
    BaseBinderAdapter adapter;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_industry_select;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(String.class, new BaseDataBindingItemBinder<String, MeItemIndustryBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_industry;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemIndustryBinding> holder, MeItemIndustryBinding binding, String item) {
                binding.setBean(item);
            }
        });
        getDataBinding().rvIndustry.setLayoutManager(new FlexboxLayoutManager(getContext()));
        getDataBinding().rvIndustry.setAdapter(adapter);
        getViewModel().getIndustryLiveData().observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> strings) {
                adapter.setList(strings);
            }
        });
    }

    @Override
    public void clickLeft(View view) {

    }
}
