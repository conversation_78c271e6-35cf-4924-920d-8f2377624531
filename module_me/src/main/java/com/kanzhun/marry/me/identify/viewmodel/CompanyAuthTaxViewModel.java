package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.net.Uri;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.kotlin.ext.LiveEventBusExtKt;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.callback.VideoUploadRequestCallback;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.api.model.VideoUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.api.model.CompanyCertInfoModelNew;
import com.kanzhun.utils.T;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;

public class CompanyAuthTaxViewModel extends FoundationViewModel {

    private final ObservableField<String> imageUrl = new ObservableField<>("");
    private final MutableLiveData<String> uploadSuccessLiveData = new MutableLiveData<>();
    private final MutableLiveData<Boolean> successLiveData = new MutableLiveData<>();

    private String videoToken;
    private String imageToken;

    public CompanyAuthTaxViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<String> getUploadSuccessLiveData() {
        return uploadSuccessLiveData;
    }

    public ObservableField<String> getImageUrl() {
        return imageUrl;
    }

    /**
     * @noinspection deprecation
     */
    public void uploadFile(Uri videoUri) {
        setShowProgressBar();
        UploadFileUtil.uploadVideo(UploadFileUtil.AUTH_SOURCE, videoUri, new VideoUploadRequestCallback<>() {
            @Override
            public void onThumbnailSuccess(ImageUploadModel imageUploadModel) {
                super.onThumbnailSuccess(imageUploadModel);
                imageUrl.set(imageUploadModel.getOriginImageUrl());
                imageToken = imageUploadModel.token;
            }

            @Override
            public void onSuccess(VideoUploadModel data) {
                if (data != null && data.originImage != null) {
                    videoToken = data.token;
                    uploadSuccessLiveData.setValue(imageUrl.get());
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getSuccessLiveData() {
        return successLiveData;
    }

    public MutableLiveData<String> errorLiveData = new MutableLiveData<>();

    public void requestSubmit(String companyName) {
        showEmptyLoading();
        Map<String, Object> params = new HashMap<>();
        params.put("company", companyName);
        params.put("certVideoToken", videoToken); // 认证视频 token
        params.put("certImageToken", imageToken); // 认证视频封面 token
        params.put("type", 5); // 1 社保； 2 工卡 ；3-邮件认证；4-企微/钉钉；5-个人所得税APP
        HttpExecutor.requestSimplePost(URLConfig.URL_COMPANY_CERT_SUBMIT, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                successLiveData.setValue(true);
                AccountHelper.getInstance().updateProfile();
                LiveEventBusExtKt.sendStringLiveEvent(LivedataKeyMe.AUTH_WORK_COMMIT_SUCCESS, "", true, true);
                showEmptySuccess();
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason.getErrCode() == 1529) {
                    errorLiveData.postValue(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
                showEmptySuccess();
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public MutableLiveData<CompanyCertInfoModelNew> companyLiveData = new MutableLiveData<>();

    public void getData() {
        Observable<BaseResponse<CompanyCertInfoModelNew>> responseObservable =
                RetrofitManager.getInstance().createApi(MeApi.class).getCompanyCertInfo();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<>(true) {
            @Override
            public void onSuccess(CompanyCertInfoModelNew data) {
                companyLiveData.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public String getCompanyName() {
        if (companyLiveData.getValue() == null) return "";
        return companyLiveData.getValue().getCompanyName();
    }

}
