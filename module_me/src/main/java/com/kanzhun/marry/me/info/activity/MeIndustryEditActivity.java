package com.kanzhun.marry.me.info.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.model.profile.IndustryBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityIndustryEditBinding;
import com.kanzhun.marry.me.databinding.MeItemIndustrySelectBinding;
import com.kanzhun.marry.me.info.callback.MeIndustryEditCallback;
import com.kanzhun.marry.me.info.viewmodel.MeIndustryEditViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;

/**
 * 我的基本信息 - 职业
 * <p>
 * Created by Qu Zhiyong on 2022/4/24
 */
@RouterUri(path = MePageRouter.ME_INDUSTRY_ACTIVITY)
public class MeIndustryEditActivity extends FoundationVMActivity<MeActivityIndustryEditBinding, MeIndustryEditViewModel> implements MeIndustryEditCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_industry_edit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);
        getViewModel().initData(getIntent());

        BaseBinderAdapter adapter = new BaseBinderAdapter();
        IndustryItemBinder itemBinder = new IndustryItemBinder();
        adapter.addItemBinder(IndustryBean.class, itemBinder);
        getDataBinding().rvIndustry.setLayoutManager(new FlexboxLayoutManager(this));
        getDataBinding().rvIndustry.setAdapter(adapter);
        itemBinder.setSelectedIndustryCode(getViewModel().initIndustryCode);

        adapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                IndustryBean data = (IndustryBean) adapter.getItem(position);
                itemBinder.setSelectedIndustryCode(data.getCode());
                adapter.notifyDataSetChanged();

                ProfileHelper.getInstance().setTmpIndustryBean(data);

                goNext(index,data.getCode(),data.getName());
            }
        });

        getViewModel().getIndustryLivaData().observe(this, industries -> {
            if (LList.isEmpty(industries)) return;
            adapter.setList(industries);
        });
    }

    private void goNext(int index,String code,String name) {
        if(getIntent().getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN,false)){
            Intent intent = new Intent();
            intent.putExtra("code",code);
            intent.putExtra("name",name);
            setResult(Activity.RESULT_OK,intent);
            AppUtil.finishActivity(this);
        }else {
            if (getViewModel().indexObservable.get() > 0) {
                ProfileHelper.getInstance().checkJump(MeIndustryEditActivity.this, getViewModel().indexObservable.get() + 1);
            } else {
                MePageRouter.jumpToOccupationActivity(MeIndustryEditActivity.this, index,false);
            }
        }
    }

    private static final class IndustryItemBinder extends BaseDataBindingItemBinder<IndustryBean, MeItemIndustrySelectBinding> {

        private String selectedIndustryCode;

        public void setSelectedIndustryCode(String selectedIndustryCode) {
            this.selectedIndustryCode = selectedIndustryCode;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.me_item_industry_select;
        }

        @Override
        protected void bind(BinderDataBindingHolder<MeItemIndustrySelectBinding> holder,
                            MeItemIndustrySelectBinding binding, IndustryBean item) {
            binding.setSelectedIndustryCode(selectedIndustryCode);
            binding.setBean(item);
        }
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(MeIndustryEditActivity.this, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

}