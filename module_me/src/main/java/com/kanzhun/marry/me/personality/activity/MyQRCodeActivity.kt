package com.kanzhun.marry.me.personality.activity

import android.content.Intent
import android.graphics.Bitmap
import android.text.TextUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.hpbr.apm.common.utils.Base64
import com.hpbr.apm.common.utils.FileUtils
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.common.kpswitch.util.ViewUtils
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.CommonPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.ImageSaveUtil
import com.kanzhun.marry.me.databinding.MeActivityMyQrcodeBinding
import com.kanzhun.marry.me.personality.viewmodel.MeActivityMyQrcodeViewModel
import com.kanzhun.utils.T
import com.sankuai.waimai.router.annotation.RouterUri
import java.io.File
import java.io.FileOutputStream


@RouterUri(path = [MePageRouter.ME_MY_QRCODE_ACTIVITY])
class MyQRCodeActivity :
    BaseBindingActivity<MeActivityMyQrcodeBinding, MeActivityMyQrcodeViewModel>() {
    override fun preInit(intent: Intent) {
        mViewModel.sourceType = intent.getStringExtra(BundleConstants.BUNDLE_SOURCE_TYP)
    }

    override fun initView() {
        fullScreenAndBlackText(marginView = mBinding.clRoot, marginFalsePaddingTrue = true)
        mBinding.titleView.asBackButton()
        mViewModel.qrBeanLiveData.observe(this){
            mBinding.idStateLayout.showContent()
            Glide.with(this).load(it.userInfo?.tinyAvatar).apply(RequestOptions.circleCropTransform()).into(mBinding.ivAvatar)
//            mBinding.ivAvatar.load(it.userInfo?.tinyAvatar)
            mBinding.idName.text = it.userInfo?.nickName
            if(it.userInfo?.addressLevel2?.isNotEmpty() == true){
                mBinding.idLocol.text = it.userInfo?.addressLevel1 +"·"+ it.userInfo?.addressLevel2
            }else{
                mBinding.idLocol.text = it.userInfo?.addressLevel1
            }
            mBinding.idQRCode.loadData(Base64.decode(it.qrData))
            mBinding.idTImeOutBtn.onClick {
                onRetry()
            }
        }

        mBinding.idBottomLeft.onClick {
            CommonPageRouter.jumpToScanActivity(this@MyQRCodeActivity)
        }
        mBinding.idBottomRight.onClick {
            if(mViewModel.qrBeanLiveData.value != null && !TextUtils.isEmpty(mViewModel.qrBeanLiveData.value!!.qrData)){
                //保存二维码
                PermissionHelper.getAlbumHelper(this@MyQRCodeActivity)
                    .setPermissionCallback { yes, permission ->
                        if (yes) {
                            mBinding.idQRCode.post {
//                                val bitmap = BitmapFactory.decodeByteArray(Base64.decode(mViewModel.listData.value!!.qrData),0,Base64.decode(mViewModel.listData.value!!.qrData).size)
                                val bitmap = ViewUtils.getBitmap(mBinding.idSaveCenter)
                                val path = FileUtils.getCacheDirInternalPath()+"/o2"+System.currentTimeMillis()+".png"
                                val fos:FileOutputStream = FileOutputStream(path)
                                bitmap.compress(Bitmap.CompressFormat.PNG,100,fos);
                                fos.close()
                                ImageSaveUtil.saveFileToGallery(this@MyQRCodeActivity, File(path))
                                bitmap.recycle()
                            }

                        }else{
                            T.ss("需开启相册权限后，才能保存二维码图片")
                        }
                    }.requestPermission()
            }else{
                T.ss("图片加载中,请稍等")
            }
        }
        getQr()
    }

    private fun getQr() {
        mViewModel.getQRCode()
        mBinding.idStateLayout.showLoading()
    }

    override fun onResume() {
        super.onResume()
        if(System.currentTimeMillis() > mViewModel.timeOutTime && mViewModel.timeOutTime != 0L){
            mBinding.idQRCodeTimeOut.visible()
        }else{
            mBinding.idQRCodeTimeOut.gone()
        }
    }

    override fun initData() {

    }
    override fun onRetry() {
        getQr()
    }
    override fun getStateLayout() = mBinding.idStateLayout



}