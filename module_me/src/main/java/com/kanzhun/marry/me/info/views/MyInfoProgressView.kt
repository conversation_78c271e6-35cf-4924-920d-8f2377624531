package com.kanzhun.marry.me.info.views

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeInfoProgressViewBinding
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIDisplayHelper

class MyInfoProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {


    val binding: MeInfoProgressViewBinding

    var beforePoint:Int = 0
    var screenWidth:Int = 0
    var animator:ValueAnimator? = null

    init {
        val rootView = LayoutInflater.from(context).inflate(R.layout.me_info_progress_view, this,false)
        binding = MeInfoProgressViewBinding.bind(rootView)
        addView(rootView)
        screenWidth =  QMUIDisplayHelper.getScreenWidth(getContext())
    }

    fun setData(point:Int,useNew:Boolean = false){
        if(useNew){
            binding.apply {
                idEditProgressBar.visible()
                idQMUIFrameLayout.gone()
                idAnimLine.gone()
                setLine(point,binding.idLine2, binding.idText,true)
            }
        }else{
            binding.apply {
                idEditProgressBar.gone()
                idQMUIFrameLayout.visible()
                idAnimLine.visible()
                setLine(point,binding.idAnimLine, binding.tvRevenue)
            }
        }
    }

    private fun setLine(point: Int,view:View,textView:TextView,isNew:Boolean = false) {
        if (point > beforePoint && point != 0) {
            if (animator?.isRunning == true) {
                animator?.cancel()
            }
            animator = ValueAnimator.ofInt(beforePoint, point)
                .setDuration(500)
            animator?.addUpdateListener { animation ->
                val value = animation.getAnimatedValue() as Int
                setLocal(value, view,textView,isNew)
            }
            animator?.start()
            val c = point - beforePoint
            if (beforePoint != 0) {
                T.ss("保存成功！资料完整度+$c%")
            }
        } else {
            setLocal(point, view,textView,isNew)
        }
    }

    private fun setLocal(value: Int,view:View,textView:TextView,isNew:Boolean = false) {
        val layoutParams = view.layoutParams
        layoutParams.width = ((screenWidth - if(isNew)56.dp else 24.dp) * value / 100.0f).toInt()
        view.layoutParams = layoutParams
        beforePoint = value
        if(!isNew){
            textView.text = "$value%"
        }
    }

}