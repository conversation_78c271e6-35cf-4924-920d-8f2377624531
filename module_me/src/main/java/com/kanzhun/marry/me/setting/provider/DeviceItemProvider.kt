package com.kanzhun.marry.me.setting.provider

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.toSimpleDateFormatText
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.bean.DeviceBean
import com.kanzhun.marry.me.databinding.MeItemDeviceManagerListBinding
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 顶部基本信息
 */
class DeviceItemProvider(
    val activity: FragmentActivity,
    val onItemClick: (item: DeviceBean) -> Unit
) :
    BaseItemProvider<DeviceBean, MeItemDeviceManagerListBinding>() {
    override fun onBindItem(binding: MeItemDeviceManagerListBinding, item: DeviceBean) {
        binding.run {
            tvCurDevice.text = if (item.currentDevice) "当前设备" else "删除设备"
            tvCurDevice.setTextColor(if (item.currentDevice) R.color.common_color_B2B2B2.toResourceColor() else R.color.common_color_FF0000.toResourceColor())
            tvCurDevice.clickWithTrigger {
                onItemClick(item)
            }
            tvTitle.text = item.deviceInfo
//            val timeStamp = SimpleDateFormat(
//                "yyyy-MM-dd HH:mm:ss",
//                Locale.getDefault()
//            ).format(item.lastLoginTime)
            tvLastLoginTime.text = "最近登录时间：${item.lastLoginTime.toSimpleDateFormatText("yyyy-MM-dd HH:mm:ss")}"


        }
    }


}

