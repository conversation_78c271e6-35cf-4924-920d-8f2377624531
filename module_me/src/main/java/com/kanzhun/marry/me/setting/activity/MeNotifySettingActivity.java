package com.kanzhun.marry.me.setting.activity;

import static com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt.fullScreenAndBlackText;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;

import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityNotifySettingBinding;
import com.kanzhun.marry.me.setting.callback.MeNotifySettingCallback;
import com.kanzhun.marry.me.setting.viewmodel.MeNotifySettingViewModel;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.sankuai.waimai.router.annotation.RouterUri;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;


@RouterUri(path = MePageRouter.ME_NOTIFY_SETTING_ACTIVITY)
public class MeNotifySettingActivity extends FoundationVMActivity<MeActivityNotifySettingBinding, MeNotifySettingViewModel> implements MeNotifySettingCallback {

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_notify_setting;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fullScreenAndBlackText(this, getDataBinding().activityMain, true);
        getDataBinding().switcherNotify.setChecked(TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_NOTIFY_DETAIL), "1") ? true : false);
        getDataBinding().switcherNotify.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (!isChecked) {
                    getDataBinding().switcherNotify.setEnabled(false);
                    showDialog();
                } else {
                    if (TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_NOTIFY_DETAIL), "1") != isChecked) {
                        getViewModel().requestSetUpdate(UserSettingConfig.PERSONALITY_SETTING_NOTIFY_DETAIL, isChecked ? 1 : 0);
                    }
                }
                PointHelperKt.reportPoint("notification-setting-page-click", new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType(getDataBinding().idText2.getText().toString());
                        pointBean.setStatus(isChecked ? "开启" : "关闭");
                        return null;
                    }
                });
            }
        });
        boolean b = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "1") ? true : false;
        setViewVisiable(b);
        getDataBinding().switcherNotify2.setChecked(b);

        getDataBinding().switcherNotify2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (!isChecked) {
                    getDataBinding().switcherNotify2.setEnabled(false);
                    showDialog2();
                } else {
                    if (TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "1") != isChecked) {
                        getViewModel().requestSetUpdate(UserSettingConfig.PERSONALITY_PUSH_115, isChecked ? 1 : 0, new MeNotifySettingViewModel.CallBack() {
                            @Override
                            public void onSuccess() {
                                boolean b = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "1") ? true : false;
                                setViewVisiable(b);
                            }
                        });
                    }
                }

                PointHelperKt.reportPoint("notification-setting-page-click", new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType(getDataBinding().idText1.getText().toString());
                        pointBean.setStatus(isChecked ? "开启" : "关闭");
                        return null;
                    }
                });
            }
        });
    }

    private void setViewVisiable(boolean b) {
        if (b) {
            getDataBinding().idItem1.setVisibility(View.VISIBLE);
        } else {
            getDataBinding().idItem1.setVisibility(View.GONE);
        }
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    private void showDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                .setTitle(getString(R.string.me_notification_detail_close))
                .setPositiveText(getResources().getString(R.string.common_sure))
                .setNegativeText(getResources().getString(R.string.common_cancel))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                        getViewModel().requestSetUpdate(UserSettingConfig.PERSONALITY_SETTING_NOTIFY_DETAIL, 0);
                        getDataBinding().switcherNotify.setEnabled(true);
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {
                        getDataBinding().switcherNotify.setChecked(true);
                        getDataBinding().switcherNotify.setEnabled(true);
                    }
                });
        builder.create().show();
    }


    private void showDialog2() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                .setTitle("温馨提示")
                .setContent("关闭后可能会错过重要消息，是否确定关闭")
                .setPositiveText(getResources().getString(R.string.common_sure))
                .setNegativeText(getResources().getString(R.string.common_cancel))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                        getViewModel().requestSetUpdate(UserSettingConfig.PERSONALITY_PUSH_115, 0, new MeNotifySettingViewModel.CallBack() {
                            @Override
                            public void onSuccess() {
                                boolean b = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "1") ? true : false;
                                setViewVisiable(b);
                            }
                        });
                        getDataBinding().switcherNotify2.setEnabled(true);
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {
                        getDataBinding().switcherNotify2.setChecked(true);
                        getDataBinding().switcherNotify2.setEnabled(true);
                    }
                });
        builder.create().show();
    }
}