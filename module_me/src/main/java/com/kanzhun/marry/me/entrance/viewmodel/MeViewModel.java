package com.kanzhun.marry.me.entrance.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.kotlin.ui.statelayout.StateLayout;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.OfficeActivityBean;
import com.kanzhun.foundation.model.profile.GuideItemsResponse;
import com.kanzhun.foundation.model.profile.UserTabModel;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.utils.T;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/30
 */
public class MeViewModel extends FoundationViewModel {
//    public static final String KEY_USER_TAB_MODEL = "key_user_tab_model";
    public static final String KEY_NEWCOMER_TASK_FINISHED = "isNewcomerTaskFinished";


    private MutableLiveData<UserTabModel> userTabModelMutableLiveData = new MutableLiveData<>();
    private MutableLiveData<OfficeActivityBean> officeActivityBeanLiveData = new MutableLiveData<>();
    private MutableLiveData<GuideItemsResponse> guideItemsLiveData = new MutableLiveData<>();

    public MeViewModel(Application application) {
        super(application);
        initUserModel();
    }

    private void initUserModel() {
//        String json = SpManager.get().user().getString(KEY_USER_TAB_MODEL, "");
//        if (!TextUtils.isEmpty(json)) {
//            try {
//                mCurrUserTabModel = GsonUtils.getGson().fromJson(json, UserTabModel.class);
//                if (mCurrUserTabModel != null) {
//                    userTabModelMutableLiveData.setValue(mCurrUserTabModel);
//                }
//            } catch (Exception e) {
//                CrashReport.postCatchedException(e);
//            }
//        }
    }

    public MutableLiveData<UserTabModel> getUserTabModelMutableLiveData() {
        return userTabModelMutableLiveData;
    }

    public MutableLiveData<OfficeActivityBean> getOfficeActivityBeanLiveData() {
        return officeActivityBeanLiveData;
    }

    private boolean isShowLoad = false;

    public void requestUserTabInfo(StateLayout stateLayout) {
        Observable<BaseResponse<UserTabModel>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestUserTabInfo();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<UserTabModel>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
//                if (mCurrUserTabModel == null) {
//                    setShowProgressBar();
//                }
                if(stateLayout != null && !isShowLoad){
                    isShowLoad = true;
                    stateLayout.showLoading(null,  false,false);
                }
            }

            @Override
            public void handleInChildThread(UserTabModel data) {
                super.handleInChildThread(data);
                userTabModelMutableLiveData.postValue(data);
                if(data.meetupCard != null && !TextUtils.isEmpty(data.meetupCard.getPicUrl())){
                }else {
                    Observable<BaseResponse<OfficeActivityBean>> responseObservable2 = RetrofitManager.getInstance().createApi(MeApi.class).officeActivityBean();
                    HttpExecutor.execute(responseObservable2, new BaseRequestCallback<OfficeActivityBean>() {

                        @Override
                        public void handleInChildThread(OfficeActivityBean data) {
                            super.handleInChildThread(data);
                            officeActivityBeanLiveData.postValue(data);
                        }

                        @Override
                        public void onSuccess(OfficeActivityBean data) {
                        }

                        @Override
                        public void dealFail(ErrorReason reason) {
                        }
                    });
                }

            }

            @Override
            public void onSuccess(UserTabModel data) {
                if(stateLayout != null)stateLayout.showContent(null);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                userTabModelMutableLiveData.setValue(null);
                if(stateLayout != null)stateLayout.showError(null);
            }

        });



    }

    /**
     * 获取新手流程信息
     */
    public void getGuideItems() {
        Observable<BaseResponse<GuideItemsResponse>> responseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).getGuideItems();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<GuideItemsResponse>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess(GuideItemsResponse data) {
                ProfileHelper.getInstance().setGuideItemsResponse(data);
                guideItemsLiveData.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                ProfileHelper.getInstance().setExtInfo(null);
                ProfileHelper.getInstance().clearUploadCache();
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<GuideItemsResponse> getGuideItemsLiveData() {
        return guideItemsLiveData;
    }
}
