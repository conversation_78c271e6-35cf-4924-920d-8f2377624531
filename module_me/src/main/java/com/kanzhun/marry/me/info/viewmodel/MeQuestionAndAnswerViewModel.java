package com.kanzhun.marry.me.info.viewmodel;

import android.app.Application;
import android.net.Uri;
import android.text.TextUtils;
import android.util.ArrayMap;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.views.AudioRecorderPlayView;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.bean.VoiceExtInfoBean;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.api.model.VoiceUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.model.profile.QuestionAnswerSuccessModel;
import com.kanzhun.foundation.model.profile.QuestionInfoResponse;
import com.kanzhun.foundation.model.profile.QuestionTemplateModel;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.download.callback.DownLoadListener;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.views.MeVoiceView;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.T;
import com.kanzhun.utils.file.FileUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/18
 */
public class MeQuestionAndAnswerViewModel extends FoundationViewModel {
    private int answerType;//1-语音; 2-文本
    private ObservableField<String> textAnswerObservable = new ObservableField<>();
    private ObservableField<QuestionTemplateModel.QuestionInfoBean> currQuestionObservable = new ObservableField<>();
    private MutableLiveData<QuestionAnswerSuccessModel> successLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> updateSuccessLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> voiceSaveLiveData = new MutableLiveData<>();
    private MutableLiveData<File> voiceDownloadLiveData = new MutableLiveData<>();
    private MutableLiveData<QuestionTemplateModel.QuestionInfoBean> questionDetailLiveData = new MutableLiveData<>();

    private MutableLiveData<Boolean> showQuestionDeletedDialog = new MutableLiveData<>();

    private String answerId;
    private String voiceUrl;

    private long questionId;

    private VoiceExtInfoBean voiceInfoBean;

    public int[] waveArray = new int[AudioRecorderPlayView.LUMP_COUNT];
    public int duration = 0;
    public int voiceStatus;
    public boolean isRelSel;
    public String voiceFilePath;
    public boolean haveVoiceFile;

    private ProfileInfoModel.QuestionAnswer mQuestionAnswer;

    public String preTextAnswer;
    public String preErrorDesc;
    public ObservableField<String> errorDescObservable = new ObservableField<>();

    public MeQuestionAndAnswerViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<QuestionTemplateModel.QuestionInfoBean> getQuestionDetailLiveData() {
        return questionDetailLiveData;
    }

    public MutableLiveData<Boolean> getShowQuestionDeletedDialog() {
        return showQuestionDeletedDialog;
    }


    public void queryQuestionInfo() {
        if(questionId <= 0){
            return;
        }
        Observable<BaseResponse<QuestionInfoResponse>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).getQuestionDetail(questionId);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<QuestionInfoResponse>() {
            @Override
            public void onSuccess(QuestionInfoResponse data) {
                questionDetailLiveData.setValue(data.getQuestionInfo());
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if(reason.getErrCode() == 1003){
                    showQuestionDeletedDialog.setValue(true);
                }else{
                    T.ss(reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public ObservableField<String> getTextAnswerObservable() {
        return textAnswerObservable;
    }

    public boolean textAnswerHaveContent(String content) {
        if (TextUtils.isEmpty(content)) {
            return false;
        }
        if (!TextUtils.isEmpty(preErrorDesc) && TextUtils.equals(content, preTextAnswer)) {
            errorDescObservable.set(preErrorDesc);
            return false;
        }
        return true;
    }

    public boolean hasOption(){
        QuestionTemplateModel.QuestionInfoBean questionInfoBean = getCurrQuestionObservable().get();
        if(questionInfoBean != null){
            String optionString = questionInfoBean.getOptionString();
            return !TextUtils.isEmpty(optionString);
        }
        return false;
    }

    public boolean canSubmit(){
        String content = textAnswerObservable.get();
        if (!TextUtils.isEmpty(preErrorDesc)) {//如果是驳回编辑
            if (TextUtils.equals(content, preTextAnswer)) {
                errorDescObservable.set(preErrorDesc);
                return false;
            }
            //有选项被选中，可以提交
            QuestionTemplateModel.QuestionInfoBean questionInfoBean = getCurrQuestionObservable().get();
            if(questionInfoBean != null){
                String optionString = questionInfoBean.getOptionString();
                if(!TextUtils.isEmpty(optionString)){
                    return true;
                }
            }
            //有选项，可以提交
            return !TextUtils.isEmpty(textAnswerObservable.get());
        }else{
            //有选项被选中，可以提交
            QuestionTemplateModel.QuestionInfoBean questionInfoBean = getCurrQuestionObservable().get();
            if(questionInfoBean != null){
                String optionString = questionInfoBean.getOptionString();
                if(!TextUtils.isEmpty(optionString)){
                    return true;
                }
            }
            //有选项，可以提交
            return !TextUtils.isEmpty(textAnswerObservable.get());
        }
    }


    public ObservableField<QuestionTemplateModel.QuestionInfoBean> getCurrQuestionObservable() {
        return currQuestionObservable;
    }

    public void setCurrQuestion(QuestionTemplateModel.QuestionInfoBean currQuestion) {
        this.questionId = currQuestion.id;
        this.currQuestionObservable.set(currQuestion);
    }

    public int getAnswerType() {
        return answerType;
    }


    public void setAnswerType(int answerType) {
        this.answerType = answerType;
    }


    public MutableLiveData<QuestionAnswerSuccessModel> getSuccessLiveData() {
        return successLiveData;
    }

    private String getCurrentQuestion(){
        QuestionTemplateModel.QuestionInfoBean questionInfoBean = currQuestionObservable.get();
        if(questionInfoBean != null){
            return questionInfoBean.name;
        }
        return "";
    }

    private long getCurrentQuestionId(){
        QuestionTemplateModel.QuestionInfoBean questionInfoBean = currQuestionObservable.get();
        if(questionInfoBean != null){
            return questionInfoBean.id;
        }
        return 0;
    }

    /**
     * 文本回答保存
     */
    public void textAnswerSave() {
        answerSave(textAnswerObservable.get());
    }

    public void answerSave(String answer) {
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("answerType", answerType);
        map.put("question", getCurrentQuestion());
        map.put("answer", answer);
        long questionId =  getCurrentQuestionId();
        if(questionId > 0){
            map.put("questionId", getCurrentQuestionId());
        }
        QuestionTemplateModel.QuestionInfoBean questionInfoBean = getCurrQuestionObservable().get();
        if(questionInfoBean != null){
            String optionIdString = questionInfoBean.getOptionString();
            if(!TextUtils.isEmpty(optionIdString)){
                map.put("answerOptions",optionIdString);
            }
        }
        Observable<BaseResponse<QuestionAnswerSuccessModel>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestAddQuestionAnswer(map);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<QuestionAnswerSuccessModel>(true) {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess(QuestionAnswerSuccessModel data) {
                successLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                hideShowProgressBar();
            }
        });

    }

    public MutableLiveData<Boolean> getUpdateSuccessLiveData() {
        return updateSuccessLiveData;
    }

    public void textAnswerUpdate() {
        answerUpdate(textAnswerObservable.get());
    }

    public void answerUpdate(String answer) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", answerId);
        params.put("answer", answer);
        QuestionTemplateModel.QuestionInfoBean questionInfoBean = getCurrQuestionObservable().get();
        if(questionInfoBean != null){
            String optionIdString = questionInfoBean.getOptionString();
            if(!TextUtils.isEmpty(optionIdString)){
                params.put("answerOptions",optionIdString);

            }
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_QUESTION_ANSWER, params, new SimpleRequestCallback(true) {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess() {
                updateSuccessLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void answerDel() {
        Map<String, Object> params = new HashMap<>();
        params.put("id", answerId);
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_DEL_QUESTION_ANSWER, params, new SimpleRequestCallback(true) {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess() {
                updateSuccessLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void setQuestionAnswer(ProfileInfoModel.QuestionAnswer questionAnswer) {
        if (questionAnswer == null) {
            return;
        }
        this.mQuestionAnswer = questionAnswer;
        questionId = questionAnswer.questionId;
        answerId = questionAnswer.id;
        if (answerType == MePageRouter.TYPE_ANSWER_TEXT) {
            //文本问答
            textAnswerObservable.set(questionAnswer.answer);
            preTextAnswer = questionAnswer.answer;
            if (questionAnswer.certStatus == ProfileMetaModel.STATUS_REJECTED){
                preErrorDesc = questionAnswer.certInfo;
            }
        } else if (answerType == MePageRouter.TYPE_ANSWER_VOICE) {
            //语音问答
            voiceUrl = questionAnswer.answer;
            try {
                voiceInfoBean = GsonUtils.getGson().fromJson(questionAnswer.extendInfo, VoiceExtInfoBean.class);
            } catch (Exception e) {

            }
        }
    }

    public ProfileInfoModel.QuestionAnswer getQuestionAnswer() {
        return mQuestionAnswer;
    }

    public String getAnswerId() {
        return answerId;
    }

    public long getQuestionId() {
        return questionId;
    }

    public void resetVoiceData() {
        waveArray = new int[40];
        duration = 0;
        voiceStatus = MeVoiceView.STATUS_START;
        isRelSel = false;
        voiceFilePath = null;
    }

    public void voiceSave(File file, int duration, int[] wave) {
        VoiceExtInfoBean infoBean = new VoiceExtInfoBean();
        infoBean.duration = duration;
        infoBean.wave = wave;
        setShowProgressBar();
        UploadFileUtil.uploadVoice(UploadFileUtil.PROFILE, Uri.fromFile(file), infoBean, new UploadRequestCallback<VoiceUploadModel>() {

            @Override
            public void handleInChildThread(VoiceUploadModel data) {
                super.handleInChildThread(data);
                FileUtils.reNameFile(data.url, file.getAbsolutePath());
            }

            @Override
            public void onSuccess(VoiceUploadModel data) {
                if (TextUtils.isEmpty(answerId)) {
                    answerSave(data.token);
                } else {
                    answerUpdate(data.token);
                }

            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
            }
        });
    }

    public void setHaveVoiceFile(boolean haveSuccess) {
        haveVoiceFile = haveSuccess;
    }

    public void voiceAnswerSave() {
        voiceSaveLiveData.postValue(true);
    }

    public MutableLiveData<Boolean> getVoiceSaveLiveData() {
        return voiceSaveLiveData;
    }

    public VoiceExtInfoBean getVoiceInfoBean() {
        return voiceInfoBean;
    }

    public void voiceDownload() {
        if (TextUtils.isEmpty(voiceUrl)) {
            return;
        }
        setShowProgressBar();
        HttpExecutor.downLoadFile(voiceUrl, new DownLoadListener<File>() {
            @Override
            public void onSuccess(File file) {
                hideShowProgressBar();
                voiceDownloadLiveData.setValue(file);
            }

            @Override
            public void onFail(Throwable e) {
                hideShowProgressBar();
                T.ss(e.getMessage());
            }

            @Override
            public void onProgress(int percent) {

            }
        });
    }

    public MutableLiveData<File> getVoiceDownloadLiveData() {
        return voiceDownloadLiveData;
    }
}
