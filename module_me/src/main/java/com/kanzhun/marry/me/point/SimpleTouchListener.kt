package com.kanzhun.marry.me.point

import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View

class SimpleTouchListener(val callback:()->Unit): View.OnTouchListener {
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        if(event?.action == MotionEvent.ACTION_DOWN){
            callback.invoke()
        }
        return false
    }

}