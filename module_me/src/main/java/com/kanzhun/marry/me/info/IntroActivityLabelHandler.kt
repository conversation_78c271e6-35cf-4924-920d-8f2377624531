package com.kanzhun.marry.me.info

import android.view.View
import android.widget.FrameLayout
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.views.label.LabelView
import com.kanzhun.foundation.ai.AgcDialog
import com.kanzhun.foundation.ai.AgcGrantLayout
import com.kanzhun.foundation.ai.GrantAiProtocolDialog
import com.kanzhun.foundation.api.FoundationApiK
import com.kanzhun.foundation.api.SCENE_IDEAL
import com.kanzhun.foundation.api.response.GetUserTagResponse
import com.kanzhun.foundation.api.response.RecommendTagContentGenerateResponse
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle
import com.kanzhun.foundation.router.TaskPageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityMeUpdateIntroBinding


class IntroActivityLabelHandler {

    fun handlerAIGC(
        context: FragmentActivity,
        mBinding: MeActivityMeUpdateIntroBinding,
        onReplace: (String) -> Unit,
        onAppend: (String) -> Unit,
        onClose: () -> Unit
    ) {
        mBinding.composeView.apply {
            visibility = View.GONE
            onSetViewTreeContent {
                AgcGrantLayout(onGrant = {
                    shouldTriggerGrant(
                        activity = context,
                        mBinding = mBinding,
                        onReplace = onReplace,
                        onAppend = onAppend,
                        onClose = onClose
                    )
                })
            }
        }

        shouldTriggerGrant(
            activity = context,
            mBinding = mBinding,
            onReplace = onReplace,
            onAppend = onAppend,
            onClose = onClose
        )
    }

    private fun shouldTriggerGrant(
        activity: FragmentActivity,
        mBinding: MeActivityMeUpdateIntroBinding,
        onReplace: (String) -> Unit,
        onAppend: (String) -> Unit,
        onClose: () -> Unit
    ) {
        GrantAiProtocolDialog.shouldShow(
            activity = activity,
            onShowGrantDialog = {
                reportPoint("aigenerate-authorize-popup-expo") {
                    actionp2 = "我的理想型" // 记录触发的类型：自我介绍、我的理想型
                }
            },
            onGrant = { isUserTrigger ->
                showAgcDialog(
                    activity = activity,
                    mBinding = mBinding,
                    onReplace = onReplace,
                    onAppend = onAppend,
                    onClose = onClose
                )

                if (isUserTrigger) {
                    reportPoint("aigenerate-authorize-popup-click") {
                        actionp2 = "我的理想型" // 记录触发的类型：自我介绍、我的理想型
                        type = "同意并继续" // 记录点击的位置：同意并继续、关闭
                    }
                }
            },
            onClose = {
                // 没有授权，显示引导授权入口
                mBinding.composeView.visibility = View.VISIBLE
                onClose()

                reportPoint("aigenerate-authorize-popup-click") {
                    actionp2 = "我的理想型" // 记录触发的类型：自我介绍、我的理想型
                    type = "关闭" // 记录点击的位置：同意并继续、关闭
                }
            }
        )
    }

    private fun showAgcDialog(
        activity: FragmentActivity,
        mBinding: MeActivityMeUpdateIntroBinding,
        onReplace: (String) -> Unit,
        onAppend: (String) -> Unit,
        onClose: () -> Unit
    ) {
        AgcDialog.shouldShow(
            activity = activity,
            scene = SCENE_IDEAL,
            onReplace = {
                onReplace(it)
            },
            onAppend = {
                onAppend((it))
            },
            onClose = {
                onClose()
            }
        )

        // 隐藏入口
        mBinding.composeView.visibility = View.GONE
    }


    fun handler(
        context: FragmentActivity,
        binding: MeActivityMeUpdateIntroBinding,
        scene: String,
        certInfo: String,
        certState: String?,
        content: String,
        doText: Boolean = false,
        onTextFinish: (String) -> Unit
    ) {

        binding.llBottomFloat3.gone()
        binding.llBottomFloat3.onClick {

            val bundle = getPageSourceBundle(PageSource.NONE)
            bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 2)
            bundle.putString("certInfo", certInfo)
            bundle.putString("certState", certState?:"")
            bundle.putString(BundleConstants.BUNDLE_CONTENT_DATA, content)
            TaskPageRouter.jumpToLoverTagActivity(context, bundleFrom = bundle)

        }
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            FoundationApiK::class.java
        ).getUserTagUserTag(scene)
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<GetUserTagResponse>() {

                override fun onSuccess(data: GetUserTagResponse) {
                    if (data.idealPartnerTags == null) return
                    binding.llBottomFloat3.visible()
                    binding.idRVStory.removeAllViews()
                    data.idealPartnerTags?.forEach { item ->
                        val loginKeySelectLabelView = LabelView(context)
                        loginKeySelectLabelView.setText(item.content)
                        loginKeySelectLabelView.setTextColor(context.getColor(R.color.common_color_292929))

                        binding.idRVStory.addView(
                            loginKeySelectLabelView, FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            )
                        )
                    }

                }

                override fun handleInChildThread(data: GetUserTagResponse?) {
                    super.handleInChildThread(data)
                    if (doText && data?.idealPartnerTags?.isNotEmpty() == true) {
                        val str = data.idealPartnerTags?.joinToString(",") { it.id.toString() }
                        doText(str, scene, onTextFinish)
                    }


                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })


    }

    private fun doText(
        str: String?,
        scene: String,
        onTextFinish: (String) -> Unit,
    ) {
        val bRS = RetrofitManager.getInstance().createApi(
            FoundationApiK::class.java
        ).getRecommendTagContentGenerate(str ?: "", scene.toInt())
        HttpExecutor.execute(
            bRS,
            object : BaseRequestCallback<RecommendTagContentGenerateResponse>() {

                override fun onSuccess(data: RecommendTagContentGenerateResponse) {
                    data.content?.let {
                        onTextFinish(it)
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })
    }
}

