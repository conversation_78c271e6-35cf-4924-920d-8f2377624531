package com.kanzhun.marry.me.personality.callback;

import com.kanzhun.common.callback.SubmitCallback;
import com.kanzhun.common.callback.TitleBarOnlyLeftCallback;
import com.kanzhun.marry.me.personality.bean.QuestionBean;

/**
 * <AUTHOR>
 * @date 2022/3/28.
 */
public interface PersonalityTestCallback extends TitleBarOnlyLeftCallback, SubmitCallback {
    void onQuestionSelected(QuestionBean questionBean, int position, int optionIndex);

    void onClickSkip();
}
