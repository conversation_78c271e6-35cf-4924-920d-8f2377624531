package com.kanzhun.marry.me.info.activity.preview.performance

import android.content.Context
import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ui.button.enableButton
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.marry.me.databinding.MeAddUserTagBottomDialogBinding
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel

/**
 * 添加标签逻辑
 */
class AddTagPerformance(val viewModel: MeUserInfoViewModel, val context: Context) : AbsPerformance() {

    /**
     * @param type 0个性标签，1 兴趣爱好
     */
    fun onClickTag(bean: ProfileInfoModel.Label,type:Int ,data:ProfileInfoModel) {
        if(data.userId.isMyself()){//自己的主页不可添加
            return
        }
        CommonViewBindingDialog(context,
                    mCancelable = true,
                    mCanceledOnTouchOutside = true,
                    mGravity = Gravity.BOTTOM,
                    mPaddingLeft = 0,
                    mPaddingRight = 0,
                    onInflateCallback = { inflater, dialog ->
                        val binding = MeAddUserTagBottomDialogBinding.inflate(inflater)
                        binding.apply {
                            this.tvParentName.text =if(bean.parentName.isNullOrBlank()){
                                if(type == 1){
                                    "兴趣爱好"
                                }else{
                                    "个性标签"
                                }
                            }else{
                                if(type == 1){
                                    "兴趣爱好·${bean.parentName}"
                                }else{
                                    "个性标签·${bean.parentName}"
                                }
                            }
                            this.tvTagName.text = if(bean.icon.isNullOrBlank()) bean.tagName else bean.icon + bean.tagName
                            this.ivClose.clickWithTrigger {
                                dialog.dismiss()
                            }
                            if(bean.sameTag){
                                this.btnSubmit.text = "已添加为我的标签"
                            }
                            this.btnSubmit.enableButton(!bean.sameTag,false)
                            this.btnSubmit.clickWithTrigger {
                                dialog.dismiss()
                                viewModel.addTag(bean,type)
                            }
                        }
                        binding
                    })
                    .show()
    }
}