package com.kanzhun.marry.me.info.activity;

import static com.kanzhun.foundation.Constants.HAS_CAR;
import static com.kanzhun.foundation.Constants.HAS_HOUSE;
import static com.kanzhun.foundation.Constants.HAS_NO_CAR;
import static com.kanzhun.foundation.Constants.HAS_NO_HOUSE;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.StringRes;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.dialog.DialogExtKt;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityHouseCarEditBinding;
import com.kanzhun.marry.me.info.callback.MeHouseCarEditCallback;
import com.kanzhun.marry.me.info.viewmodel.MeHouseCarEditViewModel;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 我的隐私信息 - 房车
 * <p>
 * Created by Qu Zhiyong on 2022/4/20
 */
@RouterUri(path = MePageRouter.ME_HOUSE_CAR_ACTIVITY)
public class MeHouseCarEditActivity extends FoundationVMActivity<MeActivityHouseCarEditBinding, MeHouseCarEditViewModel> implements MeHouseCarEditCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_house_car_edit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);
        getViewModel().initData();

        getViewModel().getUpdateSuccessLivaData().observe(this, o -> {
            if (getViewModel().indexObservable.get() > 0) {
                ProfileHelper.getInstance().checkJump(MeHouseCarEditActivity.this, getViewModel().indexObservable.get() + 1);
            } else {
                setResult(RESULT_OK);
                AppUtil.finishActivity(MeHouseCarEditActivity.this);
            }
        });
        getDataBinding().idBtnHasCar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getViewModel().carHoldObservable.set(2);
                initSelectUI();
            }
        });
        getDataBinding().idBtnNoCar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getViewModel().carHoldObservable.set(1);
                initSelectUI();
            }
        });
        getDataBinding().idBtnNoHouse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getViewModel().houseHoldObservable.set(1);
                initSelectUI();
            }
        });
        getDataBinding().idBtnHasHouse.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getViewModel().houseHoldObservable.set(2);
                initSelectUI();
            }
        });
        initSelectUI();
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(MeHouseCarEditActivity.this, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        int houseHold = getViewModel().houseHoldObservable.get() == HAS_HOUSE ? HAS_HOUSE : HAS_NO_HOUSE;
        int carHold = getViewModel().carHoldObservable.get() == HAS_CAR ? HAS_CAR : HAS_NO_CAR;

        if (getViewModel().indexObservable.get() > 0) {
            getViewModel().updateHouseCar();
        } else {
            if (getViewModel().initHouseHold == houseHold && getViewModel().initCarHold == carHold) {// 只有房车改变时才更新
                AppUtil.finishActivity(MeHouseCarEditActivity.this);
            } else {

                boolean carShowAuthingToast = getViewModel().isCarAuthing() && getViewModel().initCarHold != carHold;
                boolean houseShowAuthingToast = getViewModel().isHouseAuthing() && getViewModel().initHouseHold != houseHold;
                if (carShowAuthingToast || houseShowAuthingToast) {
                    T.ss("审核进行中，暂不支持修改");
                    return;
                }
                //都没有认证
                if (!getViewModel().isHouseCert() && !getViewModel().isCarCert()) {
                    getViewModel().updateHouseCar();
                } else if (getViewModel().isHouseCert() && !getViewModel().isCarCert()) {//房认证了，车没有
                    if (getViewModel().initHouseHold > houseHold) {
                        showAuthCancelDialog(R.string.me_house_auth_cancel_tips);
                    } else {
                        getViewModel().updateHouseCar();
                    }
                } else if (!getViewModel().isHouseCert() && getViewModel().isCarCert()) {//车认证了，房没有
                    if (getViewModel().initCarHold > carHold) {
                        showAuthCancelDialog(R.string.me_car_auth_cancel_tips);
                    } else {
                        getViewModel().updateHouseCar();
                    }
                } else {//房车都认证了
                    if (getViewModel().initCarHold > carHold && getViewModel().initHouseHold <= houseHold) {
                        showAuthCancelDialog(R.string.me_car_auth_cancel_tips);

                    } else if (getViewModel().initHouseHold > houseHold && getViewModel().initCarHold <= carHold) {
                        showAuthCancelDialog(R.string.me_house_auth_cancel_tips);

                    } else if (getViewModel().initHouseHold > houseHold && getViewModel().initCarHold > carHold) {
                        showAuthCancelDialog(R.string.me_house_and_car_auth_cancel_tips);

                    } else {
                        getViewModel().updateHouseCar();
                    }
                }

            }
        }
    }

    private void showAuthCancelDialog(@StringRes int resId) {
        String dialogContent = getResources().getString(resId);
        DialogExtKt.showTwoButtonDialog(this,
                getString(R.string.common_warm_tips),
                dialogContent, false, false,  getString(R.string.common_sure), new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        getViewModel().updateHouseCar();
                        return null;
                    }
                }, getString(R.string.common_cancel), new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        return null;
                    }
                },false);
    }


    private void initSelectUI() {
        if (getViewModel().carHoldObservable.get() == 2) {
            getDataBinding().idBtnHasCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929);
            getDataBinding().idBtnNoCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5);
        } else if(getViewModel().carHoldObservable.get() == 1){
            getDataBinding().idBtnHasCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5);
            getDataBinding().idBtnNoCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929);
        }
        if (getViewModel().houseHoldObservable.get() == 2) {
            getDataBinding().idBtnHasHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929);
            getDataBinding().idBtnNoHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5);
        } else if (getViewModel().houseHoldObservable.get() == 1){
            getDataBinding().idBtnHasHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5);
            getDataBinding().idBtnNoHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929);
        }
    }
}