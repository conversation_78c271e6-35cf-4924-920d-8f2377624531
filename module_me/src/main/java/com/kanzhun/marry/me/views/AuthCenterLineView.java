package com.kanzhun.marry.me.views;


import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.kanzhun.common.keyboard.util.DensityUtil;
import com.kanzhun.marry.me.R;

public class AuthCenterLineView extends View {
    Paint paint;
    public AuthCenterLineView(Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(context.getColor(R.color.common_color_FFE0E0E0));
        paint.setStrokeWidth(DensityUtil.dp2px(context, 1f));
        paint.setStyle(Paint.Style.STROKE);
        paint.setPathEffect(new DashPathEffect(new float[] {10, 10}, 0));

    }

    public AuthCenterLineView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public AuthCenterLineView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
        
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawLine(getMeasuredWidth()/2,0,getMeasuredWidth()/2,getMeasuredHeight(),paint);
    }
}
