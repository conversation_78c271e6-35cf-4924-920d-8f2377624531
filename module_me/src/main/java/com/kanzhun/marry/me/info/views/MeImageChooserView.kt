package com.kanzhun.marry.me.info.views

import android.animation.Animator
import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.Observable
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.viewbinding.ViewBinding
import com.common.AvoidOnResult
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.views.ItemTouchChangeListener
import com.kanzhun.foundation.adapter.BindingViewHolder
import com.kanzhun.foundation.adapter.MultiTypeBindingAdapter
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.EmptyStoryItem
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.databinding.MeItemPicStoryShowBinding
import com.kanzhun.foundation.databinding.MeItemStoryAddNewBinding
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.imageviewer.ext.storyViewer
import com.kanzhun.marry.me.databinding.MeItemPicVideoShowBinding
import com.kanzhun.marry.me.databinding.MeViewImageChooserBinding
import com.kanzhun.marry.me.ext.getEmptyCount
import com.kanzhun.marry.me.ext.getPicCount
import com.kanzhun.marry.me.ext.toCertStatusName
import com.kanzhun.marry.me.ext.toImageViewList
import com.kanzhun.marry.me.ext.toPointAction
import com.kanzhun.marry.me.info.bean.VideoStoryItem
import com.kanzhun.marry.me.info.callback.ItemTouchHelperCallback
import com.kanzhun.marry.me.info.viewmodel.MeImageChooserViewModel
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel
import com.kanzhun.utils.base.LList
import com.kanzhun.utils.file.FileUtils
import com.zhihu.matisse.Matisse

/**
 * 自我介绍、兴趣爱好、家庭介绍
 * 图片选择、上传、预览
 */
class MeImageChooserView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    val mBinding = MeViewImageChooserBinding.inflate(LayoutInflater.from(context), this, true)

    private var storyAdapter: MultiTypeBindingAdapter<BaseStoryShowItem, ViewBinding>? = null
    private var itemTouchHelperCallback: ItemTouchHelperCallback? = null
    private var helper: ItemTouchHelper? = null
    val mViewModel: MeImageChooserViewModel = MeImageChooserViewModel()

    private val activity: AppCompatActivity by lazy {
        context as AppCompatActivity
    }

    var pageSource: PageSource = PageSource.NONE

    var myInfoEditViewModel: MyInfoEditViewModel? = null

    init {

        storyAdapter = storyAdapter()
        mBinding.rvImg.adapter = storyAdapter

        storyAdapter?.apply {
            itemTouchHelperCallback = ItemTouchHelperCallback(
                this,
                mViewModel.mList.getPicCount() > 2,
                mViewModel.mList.getPicCount()
            ) {
                mViewModel.mList
            }
            itemTouchHelperCallback?.setLongPressDragEnabled(true)
            itemTouchHelperCallback?.setListener(object : ItemTouchChangeListener {
                override fun touchChanged() {
                    if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                        reportPoint(mViewModel.chooserType.toPointAction()) {
                            this.type = "长按拖动"
                            this.count = mViewModel.mList.getPicCount()
                        }
                    }
                }

                override fun onDragFinish() {
                    mViewModel.mList.apply {
                        mViewModel.sortStory(this)
                    }
                }
            })
        }
        helper = ItemTouchHelper(itemTouchHelperCallback!!)
        helper?.attachToRecyclerView(mBinding.rvImg)

        //更新完成度
        mViewModel.updateProgressLiveData.observe(activity) {
            if (it) {
                myInfoEditViewModel?.updateProgress()
            }
        }

        //更新列表
        mViewModel.refreshListLiveData.observe(activity) {
            if (it) {
                notifyStoryAdapter()
            }
        }

    }

    private fun checkData(block: () -> Unit) {
        if (mViewModel.chooserType != ChooserType.NO
            && myInfoEditViewModel != null
            && mViewModel.mList.isNotEmpty()
        ) {
            block()
        }
    }

    fun notifyStoryAdapter() {
        checkData {
            storyAdapter?.replaceData(mViewModel.mList)
            itemTouchHelperCallback?.setCount(mViewModel.mList.getPicCount())
        }
    }

    /**
     * 是否有图片正在上传
     */
    fun isUploading(): Boolean {
        for ((_, value) in mViewModel.countUpload) {
            if (!value) {
                return true
            }
        }
        return false
    }

    /**
     * 结束上传
     */
    fun disposeRequest() {
        for (value in mViewModel.disposableCache.values) {
            mViewModel.disposeRequest(value)
        }
    }


    /**
     * 适配器
     */
    private fun storyAdapter() = buildMultiTypeAdapterByType {
        /**
         * 图片
         */
        layout(MeItemPicStoryShowBinding::inflate) { _, story: PicStoryItem ->
            if (StringUtil.trimEAndN(story.storyText).isNullOrEmpty()) {
                binding.idTxtIcon.gone()
            } else {
                binding.idTxtIcon.visible()
            }
            if (story.certStatus.get() == ProfileMetaModel.STATUS_REJECTED) {
                binding.idIdentify.visible()
            } else {
                binding.idIdentify.gone()
            }
            if (story.getFile() != null) {
                binding.ovStory.loadRoundUri(story.getFile())
            } else {
                if (!TextUtils.isEmpty(story.getUrl())) {
                    mViewModel.downLoadItemFile(story)
                }
            }
            story.upLoadPercent.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)
                }
            })
            story.addFailed.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)
                }
            })
            story.downLoadPercent.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    if (activity.isDestroyed) return
                    storyItemUpdate(story)
                    if (story.downLoadPercent.get() == 100) {
                        binding.ovStory.loadRoundUri(story.getFile())
                    }
                }
            })
            story.downLoadFailed.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)
                }
            })
            story.uploaded.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    showUploadSuccessAnimation(binding.idEndAnim, story.uploaded.get(), story)
                }
            })


            binding.ivDelete.visible()

            //预览
            binding.root.onClick {
                clickPic(story, binding.root)
                if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                    reportPoint(mViewModel.chooserType.toPointAction()) {
                        this.type = "查看"
                        this.status = story.certStatus.get().toCertStatusName()
                        this.count = mViewModel.mList.getPicCount()
                    }
                }
            }

            //删除
            binding.ivDelete.onClick {
                mViewModel.clickDeletePic(story)
                if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                    reportPoint(mViewModel.chooserType.toPointAction()) {
                        this.type = "删除"
                        this.status = story.certStatus.get().toCertStatusName()
                        this.count = mViewModel.mList.getPicCount()
                    }
                }
            }

            //点击重试
            binding.tvRetry.onClick {
                mViewModel.clickRetryStory(story)
            }

            //点击重新加载
            binding.tvRetryDown.onClick {
                mViewModel.clickRetryDown(story)
            }

            storyItemUpdate(story)
        }

        /**
         * 视频
         */
        layout(MeItemPicVideoShowBinding::inflate) { _, story: VideoStoryItem ->
            if (StringUtil.trimEAndN(story.storyText).isNullOrEmpty()) {
                binding.idTxtIcon.gone()
            } else {
                binding.idTxtIcon.visible()
            }
            if (story.certStatus.get() == ProfileMetaModel.STATUS_REJECTED) {
                binding.idIdentify.visible()
            } else {
                binding.idIdentify.gone()
            }
            binding.ivDelete.visible()
            if (story.file != null) {
                binding.ovStory.loadRoundUri(story.file)
            } else {
                if (!TextUtils.isEmpty(story.thumbnail)) {
                    binding.ovStory.load(story.thumbnail)
                }
            }
            if (story.file == null) {
                if (!TextUtils.isEmpty(story.url)) {
                    mViewModel.downLoadItemFile(story)
                }
            }

            story.upLoadPercent.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)

                }
            })
            story.downLoadPercent.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    if (activity.isDestroyed) return
                    storyItemUpdate(story)
                }
            })
            story.downLoadFailed.addOnPropertyChangedCallback(object :
                Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)
                }
            })
            story.uploaded.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    showUploadSuccessAnimation(binding.idEndAnim, story.uploaded.get(), story)
                }
            })
            story.addFailed.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
                override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                    storyItemUpdate(story)
                }
            })


            //预览
            binding.root.onClick {
                clickPic(story, binding.root)
                if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                    reportPoint(mViewModel.chooserType.toPointAction()) {
                        this.type = "查看"
                        this.status = story.certStatus.get().toCertStatusName()
                        this.count = mViewModel.mList.getPicCount()
                    }
                }
            }
            //删除
            binding.ivDelete.onClick {
                mViewModel.clickDeletePic(story)
                if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                    reportPoint(mViewModel.chooserType.toPointAction()) {
                        this.type = "删除"
                        this.status = story.certStatus.get().toCertStatusName()
                        this.count = mViewModel.mList.getPicCount()
                    }
                }
            }
            //点击重试
            binding.tvRetry.onClick {
                mViewModel.clickRetryStory(story)
            }
            //点击重新加载
            binding.tvRetryDown.onClick {
                mViewModel.clickRetryDown(story)
            }

            storyItemUpdate(story)
        }

        /**
         * 添加按钮
         */
        layout(MeItemStoryAddNewBinding::inflate) { _, story: EmptyStoryItem ->
            binding.root.onClick {
                clickPicAdd(mViewModel.mList.getEmptyCount())
                if (!TextUtils.isEmpty(mViewModel.chooserType.toPointAction())) {
                    reportPoint(mViewModel.chooserType.toPointAction()) {
                        this.type = "添加"
                        this.count = mViewModel.mList.getPicCount()
                    }
                }
            }
        }
    }

    /**
     * 预览图片or视频
     */
    private fun clickPic(data: BaseStoryShowItem, root: View) {

        if (TextUtils.isEmpty(data.id)) {
            return
        }

        if (data is VideoStoryItem) {
            if (data.thumbnail.isNullOrEmpty()) {
//            T.ss("tinyUrl = null")
                return
            }
            if (data.url.isNullOrEmpty()) {
//            T.ss("url = null")
                return
            }
        } else {
            if (data.tinyUrl.isNullOrEmpty()) {
//            T.ss("tinyUrl = null")
                return
            }
            if (data.url.isNullOrEmpty()) {
//            T.ss("url = null")
                return
            }
        }

        if (!data.addFailed.get() && data.upLoadPercent.get() < 100) { //故事上传中不可点击
            return
        }
        //预览图片
        root.storyViewer(listOf(data).toImageViewList(root), 0)
    }


    /**
     * 点击添加图片，调起相册
     */
    private fun clickPicAdd(count: Int) {
        PhotoSelectManager.jumpForGalleryMultipleCropResult(
            activity as FragmentActivity,
            count,
            object : AvoidOnResult.Callback {
                override fun onActivityResult(
                    requestCode: Int,
                    resultCode: Int,
                    data: Intent?
                ) {
                    if (resultCode == Activity.RESULT_OK) {
                        val files = Matisse.obtainResult(data)

                        if (LList.isEmpty(files)) {
                            return
                        }

                        loadStoryFiles(files)
                    }
                }
            })
    }


    /**
     * 1、相册图片列表转换为列表item
     * 2、执行上传
     */
    private fun loadStoryFiles(files: List<Uri>) {
        ExecutorFactory.execLocalTask(Runnable {
            if (LList.isEmpty<Uri>(files)) {
                return@Runnable
            }
            val storyFiles: MutableList<BaseStoryShowItem> = mutableListOf()
            val fileIterator = files.iterator()
            while (fileIterator.hasNext()) {
                val file = fileIterator.next()
                val length = FileUtils.getFileSizeFromUri(activity, file)
                val mimeType = PhotoSelectManager.checkVideoOrPic(FileUtils.getUriSuffix(activity, file))
                when (mimeType) {
                    PhotoSelectManager.IMAGE -> storyFiles.add(getPicStoryItem(file, length))
                }
            }
            ExecutorFactory.execMainTask {
                mViewModel.uploadAddStoryFile(storyFiles)
            }
        })
    }

    fun getPicStoryItem(file: Uri, length: Long): PicStoryItem {
        val picStoryItem = PicStoryItem()
//        if (!TextUtils.isEmpty(s.photo)) {
//            val filePath = HttpExecutor.getDefaultDownLoadPath()
//            val file = FileUtils.getFile(s.photo, filePath)
//            if (file.exists()) {
//                picStoryItem.file = Uri.fromFile(file)
//                picStoryItem.setDownLoadPercent(100)
//                TLog.print("zl_log", "MyAlbumBean: setDownLoadPercent(100) line=%s", 59);
//            }
//        }
//        picStoryItem.id = s.id
//        picStoryItem.url = s.photo
//        picStoryItem.tinyUrl = s.tinyPhoto
//        picStoryItem.token = s.photo
//        picStoryItem.storyText = s.text
        picStoryItem.file = file
        picStoryItem.size = length
//        picStoryItem.setCertStatus(s.certStatus)
//        picStoryItem.textReject = s.textReject
//        picStoryItem.setPhotoReject(s.mediaReject)
        picStoryItem.setUpLoadPercent(100)
        picStoryItem.setAddFailed(false)
        return picStoryItem
    }

//    /**
//     * 相册选择，上传
//     */
//    fun upLoadAddStoryFile(selectList: List<BaseStoryShowItem>) {
//        mViewModel.uploadAddStoryFile(selectList) {
//            meInfoEditFragmentCallback?.updateProfileList(it, mViewModel.mList.getPicCount(), this)
//        }
//    }


    private fun BindingViewHolder<MeItemPicStoryShowBinding>.storyItemUpdate(
        story: PicStoryItem
    ) {
        if (!story.addFailed.get() && story.upLoadPercent.get() < 100) {
            binding.idBG.visible()
            binding.pbProgress.visible()
            binding.pbProgress.progress = story.upLoadPercent.get().toFloat()
        } else {
            binding.idBG.gone()
            binding.pbProgress.gone()
        }
        if (story.addFailed.get()) {
            binding.tvRetry.visible()
        } else {
            binding.tvRetry.gone()
        }
        if (story.downLoadFailed.get()) {
            binding.tvRetryDown.visible()
        } else {
            binding.tvRetryDown.gone()
        }
        if (!story.downLoadFailed.get() && story.downLoadPercent.get() < 100) {
            binding.idBG.visible()
            binding.downProgress.visible()
            binding.downProgress.progress = story.downLoadPercent.get().toFloat()
        } else {
            binding.idBG.gone()
            binding.downProgress.gone()
        }
    }

    private fun BindingViewHolder<MeItemPicVideoShowBinding>.storyItemUpdate(
        story: VideoStoryItem
    ) {
        if (!story.addFailed.get() && story.upLoadPercent.get() < 100) {
            binding.idBG.visible()
            binding.pbProgress.visible()
            binding.pbProgress.progress = story.upLoadPercent.get().toFloat()
        } else {
            binding.idBG.gone()
            binding.pbProgress.gone()
        }
        if (story.addFailed.get()) {
            binding.tvRetry.visible()
        } else {
            binding.tvRetry.gone()
        }
        if (story.downLoadFailed.get()) {
            binding.tvRetryDown.visible()
        } else {
            binding.tvRetryDown.gone()
        }
        if (!story.downLoadFailed.get() && story.downLoadPercent.get() < 100) {
            binding.downProgress.visible()
            binding.downProgress.progress = story.downLoadPercent.get().toFloat()
        } else {
            binding.downProgress.gone()
        }
        if ((!story.addFailed.get() && story.upLoadPercent.get() < 100)
            || (!story.downLoadFailed.get() && story.downLoadPercent.get() < 100)
        ) {
            binding.idPlay.gone()
        } else {
            binding.idPlay.visible()
        }
    }

    fun showUploadSuccessAnimation(
        view: View,
        showAnimation: Boolean,
        storyShowItem: BaseStoryShowItem
    ) {
        if (showAnimation) {
            view.visibility = View.VISIBLE
            val animator = ValueAnimator.ofFloat(0.2f, 1.0f, 1.0f).setDuration(500)
            animator.addUpdateListener { animation ->
                val value = animation.animatedValue as Float
                view.scaleY = value
                view.scaleX = value
                view.alpha = value
            }
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {}
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    storyShowItem.setUploaded(false)
                }

                override fun onAnimationCancel(animation: Animator) {
                    view.visibility = View.GONE
                    storyShowItem.setUploaded(false)
                }

                override fun onAnimationRepeat(animation: Animator) {}
            })
            animator.start()
        }
    }


}


class ImageChooserConstant {
    companion object {
        const val MAX_COUNT = 3
    }
}


enum class ChooserType {
    NO,
    SELF_INTRODUCTION, //自我介绍
    HOBBY,//兴趣爱好
    FAMILY_INTRODUCTION//家庭介绍
}




