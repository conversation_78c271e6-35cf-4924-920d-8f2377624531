package com.kanzhun.marry.me.info.fragment;

import static android.app.Activity.RESULT_OK;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import androidx.fragment.app.FragmentActivity;
import androidx.navigation.Navigation;

import com.chad.library.BR;
import com.common.AvoidOnResult;
import com.kanzhun.common.kotlin.ui.dialog.loading.LoadingDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentMeAvatarUploadCropBinding;
import com.kanzhun.marry.me.info.activity.MeAvatarUploadCropActivity;
import com.kanzhun.marry.me.info.callback.MeAvatarUploadCropCallback;
import com.kanzhun.marry.me.info.viewmodel.MeAvatarUploadCropViewModel;
import com.kanzhun.marry.me.views.MeAvatarUCropView;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.yalantis.ucrop.UCrop;
import com.zhihu.matisse.Matisse;

import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/12
 */
public class MeAvatarUploadCropFragment extends FoundationVMFragment<MeFragmentMeAvatarUploadCropBinding, MeAvatarUploadCropViewModel>
        implements MeAvatarUploadCropCallback, MeAvatarUCropView.OnCropListener {

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().flCrop.setCallback(this);
        Uri cameraFileUri = ((MeAvatarUploadCropActivity) activity).cameraFileUri;
        Bundle mCropOptionsBundle = activity.getIntent().getExtras();
        getViewModel().setGallery(mCropOptionsBundle.getBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN));
        if (cameraFileUri != null) {
            mCropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, cameraFileUri);
        }
        mCropOptionsBundle.putBoolean(UCrop.Options.EXTRA_CIRCLE_DIMMED_LAYER, true);
        getDataBinding().flCrop.initData(mCropOptionsBundle);
//        getViewModel().getSuccessBeanMutableLiveData().observe(this, new Observer<AvatarUploadSuccessBean>() {
//            @Override
//            public void onChanged(AvatarUploadSuccessBean avatarUploadSuccessBean) {
//                Intent intent = new Intent();
//                intent.putExtra(BundleConstants.BUNDLE_AVATAR_UPLOAD_DATA, avatarUploadSuccessBean);
//                activity.setResult(RESULT_OK, intent);
//                clickLeft(null);
//            }
//        });
    }

    protected void cropAndSaveImage() {
        getDataBinding().flCrop.onCropListener();
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_me_avatar_upload_crop;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(activity);
    }

    private LoadingDialog progressBar;

    @Override
    public void loadingProgress(boolean showLoader) {

        if (progressBar == null) {
            progressBar = new LoadingDialog(activity);
            progressBar.setShowTitle("裁剪中…");
        }

        if (showLoader) {
            progressBar.show();
        } else {
            progressBar.dismiss();
        }
    }

    @Override
    public void onCropFinish(MeAvatarUCropView.MeCropResult result) {
        if (result.resultCode == RESULT_OK) {
            Uri uri = result.outUri;
//            getViewModel().uploadAvatar(uri);
            Bundle bundle = new Bundle();
            bundle.putParcelable(UCrop.EXTRA_INPUT_URI, uri);
            bundle.putParcelable(UCrop.EXTRA_OUTPUT_URI, uri);
            Navigation.findNavController(getDataBinding().tvNext).navigate(R.id.action_avatarCropFragment_to_avatarCircleCropFragment, bundle);
        } else if (result.resultCode == UCrop.RESULT_ERROR) {
            //region 上报裁剪异常原因
            T.ss("裁剪失败，请选择其他图片");
//            clickLeft(null);
        }
        if (progressBar != null && progressBar.isShowing()) {
            progressBar.dismiss();
        }
    }

    @Override
    public void clickComplete() {
        cropAndSaveImage();
    }

    @Override
    public void clickReselect() {
        if (getViewModel().isGallery()) {
            jumpGallery();
        } else {
            jumpCamera();
        }
    }

    private void jumpCamera() {
        PhotoSelectManager.jumpForOnlyCameraResult((FragmentActivity) activity, new PhotoSelectManager.OnCameraUriCallBack() {
            @Override
            public void onCameraCallback(Uri cameraFileUri) {
                if (cameraFileUri != null) {
                    Bundle mCropOptionsBundle = activity.getIntent().getExtras();
                    mCropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, cameraFileUri);
                    ((MeAvatarUploadCropActivity) activity).cameraFileUri = cameraFileUri;
                    getDataBinding().flCrop.initData(mCropOptionsBundle);
                }
            }
        });
    }

    private void jumpGallery() {
        PhotoSelectManager.jumpForGalleryOnlyImageToCropResult((FragmentActivity) activity, 1, new AvoidOnResult.Callback() {
            @Override
            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                if (resultCode == RESULT_OK && data != null) {
                    List<Uri> result = Matisse.obtainResult(data);
                    if (LList.getCount(result) == 1) {
                        Uri source = result.get(0);
                        if (source != null) {
                            Bundle mCropOptionsBundle = activity.getIntent().getExtras();
                            mCropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, source);
                            ((MeAvatarUploadCropActivity) activity).cameraFileUri = source;
                            getDataBinding().flCrop.initData(mCropOptionsBundle);
                        }
                    }
                }
            }
        });
    }
}
