package com.kanzhun.marry.me.mood.item

import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItemBean

data class MoodEditSpaceBean(
    @DividerType val dividerType: Int,
    var spaceHeight: Int = 0,//间隔的高度
    var marginLeft: Int = 20,//灰线
    var marginTop: Int = 0,//灰线
    var marginRight: Int = 20,//灰线
    var marginBottom: Int = 0//灰线
) : BaseListItemBean()

@kotlin.annotation.Retention(AnnotationRetention.SOURCE)
annotation class DividerType() {
    companion object {
        const val TYPE_SPACE = 1 //间隔
        const val TYPE_GRAY_LINE = 2//灰线
    }
}

