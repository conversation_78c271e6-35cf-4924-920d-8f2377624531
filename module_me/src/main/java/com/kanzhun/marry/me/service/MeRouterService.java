package com.kanzhun.marry.me.service;

import android.content.Context;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.api.bean.ProfileStatusBean;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.UserGuideBlockInfoBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.router.service.IMeRouterService;
import com.kanzhun.marry.me.dialog.UserAuthBlockDialog;
import com.kanzhun.marry.me.dialog.UserLevelTaskDialog;
import com.kanzhun.marry.me.dialog.UserXueXinWebDialog;
import com.kanzhun.marry.me.identify.gray.AvatarAdviceGray;
import com.kanzhun.marry.me.util.UserInfoPrefectDialogUtil;
import com.sankuai.waimai.router.annotation.RouterService;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/8
 */
@RouterService(interfaces = IMeRouterService.class, key = MePageRouter.ME_SERVICE)
public class MeRouterService implements IMeRouterService {
    @Override
    public void jumpUserInfoPerfectDialog(FragmentActivity activity, List<ProfileStatusBean> profileStatusBeans) {
        UserInfoPrefectDialogUtil userInfoPrefectDialogUtil = new UserInfoPrefectDialogUtil(activity, profileStatusBeans);
        userInfoPrefectDialogUtil.show();
    }

    @Override
    public void showUserGuideBlockDialogWhenSendLike(Context context, UserGuideBlockInfoBean blockInfoBean, PageSource currentPage,String peerId) {
        UserAuthBlockDialog.Companion.showSendLikeBlockInfoWithInfo(context, blockInfoBean, currentPage,peerId);
    }

    @Override
    public void showUserGuideBlockDialogWhenChat(FragmentActivity activity, PageSource currentPage,String peerId) {
        UserAuthBlockDialog.Companion.showBlockInfoWithInfoWhenChat(activity, currentPage,peerId);
    }

    @Override
    public void showUserInviteBlockDialog(FragmentActivity activity, Function0<Unit> callback) {
        UserAuthBlockDialog.Companion.showChildInviteParentBlockDialog(activity, callback);
    }

    @Override
    public void showNewUserTaskDialog(FragmentActivity activity) {
        UserLevelTaskDialog.Companion.showDialog(activity);
    }

    @Override
    public void getAdviceGray() {
        AvatarAdviceGray.getAdviceGray(false, adviceGrayResponse -> null);
    }

    @Override
    public void showXueXinHelpDialog(FragmentActivity activity, int type) {
        UserXueXinWebDialog.Companion.showDialog(activity, type);
    }

    @Override
    public void showMeBlockInfoWithInfo(FragmentActivity activity, UserGuideBlockInfoBean blockInfo,PageSource pageSource,String p3,String peerId) {
        UserAuthBlockDialog.Companion.showMeBlockInfoWithInfo(activity, blockInfo,
                pageSource,
                p3,
               peerId);
    }
}
