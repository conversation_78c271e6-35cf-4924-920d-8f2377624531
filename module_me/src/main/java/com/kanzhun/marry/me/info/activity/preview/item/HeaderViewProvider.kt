package com.kanzhun.marry.me.info.activity.preview.item

import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statusbar.statusBarHeight
import com.kanzhun.foundation.callback.IPreviewPlayer
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.player.OPlayerHelper
import com.kanzhun.foundation.player.OViewRenderer
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityUserInfoPreviewBinding
import org.alita.webrtc.SurfaceViewRenderer

/**
 * 头部信息
 */
class HeaderViewProvider(private val binding: MeActivityUserInfoPreviewBinding,val iPlayerHelper: IPreviewPlayer?) {

    fun setData(profileMetaModel: ProfileMetaModel,isNew:Boolean) {
        binding.run {
            if (profileMetaModel.baseInfo?.liveVideo.isNullOrBlank()) {
                svVideo.gone()
            } else {//表示是视频封面
                svVideo.gone()
                val liveVideo = profileMetaModel.baseInfo?.liveVideo
                val playVideoUrl = StringUtil.getPlayVideoUrl(liveVideo)
                videoHandler(playVideoUrl,svVideo)
            }

            if(profileMetaModel.baseInfo?.tinyAvatar?.isNotEmpty() == true){
                ivAvatarBg.load(profileMetaModel.baseInfo?.tinyAvatar)
            }else{
                if(profileMetaModel.baseInfo.gender == 2)ivAvatarBg.setImageResource(R.drawable.image_preview_top_women) else ivAvatarBg.setImageResource(R.drawable.image_preview_top_man)

            }

            if (isNew){
                val lp = idShadowNew.layoutParams
                lp.height = 44.dpI + this.idShadowNew.context.statusBarHeight
                idShadowNew.layoutParams = lp
                idShadowNew.visible()
            }
        }

    }

    fun getPlayerHelper():OPlayerHelper?{
        return iPlayerHelper?.getHeaderPlayer()
    }

    private fun videoHandler(playVideoUrl:String,svVideo: OViewRenderer) {
        if(getPlayerHelper()?.viewRender == null){
            getPlayerHelper()?.setPlayView(svVideo)
            getPlayerHelper()?.setFrameScaleType(SurfaceViewRenderer.RENDER_MODE_FULL_FILL_SCREEN)
            getPlayerHelper()?.setPlayUrl(playVideoUrl)
            getPlayerHelper()?.start()
        }
    }

    fun startPlay(){
        getPlayerHelper()?.start()
    }

    fun pausePlay(){
        getPlayerHelper()?.pause()
    }

    fun release(){
        getPlayerHelper()?.onDestroy()
    }

    fun onFirstFrame() {
        binding.ivAvatarBg.gone()
        binding.svVideo.visible()
    }
}