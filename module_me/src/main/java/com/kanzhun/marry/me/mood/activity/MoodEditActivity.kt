package com.kanzhun.marry.me.mood.activity

import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.entity.StatePageBean
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.showSoftInput
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.statelayout.Status
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.common.util.ActivityAnimType
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.api.response.MoodBean
import com.kanzhun.foundation.api.response.MoodPermissionType
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.MePageRouter.jumpForResultMoodGridActivity
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityMoodEditBinding
import com.kanzhun.marry.me.mood.dialog.SetMoodViewPermissionDialog
import com.kanzhun.marry.me.mood.util.toMoodViewPermissionText
import com.kanzhun.marry.me.mood.viewmodel.MoodEditViewModel
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIKeyboardHelper
import com.sankuai.waimai.router.annotation.RouterUri
import com.techwolf.lib.tlog.TLog

/**
 * 编辑我的心情
 * 1、列表页，跳转编辑页
 * 2、我的表情详情页，跳转编辑页
 * 3、onActivityResult，返回编辑页
 * 4、我的页面，表情驳回，跳转编辑页
 */
@RouterUri(path = [MePageRouter.ME_MOOD_EDIT_ACTIVITY])
class MoodEditActivity : BaseBindingActivity<MeActivityMoodEditBinding, MoodEditViewModel>() {


    override fun preInit(intent: Intent) {
        mViewModel.pageSource = intent.getPageSource()
        val moodBean = intent.getSerializableExtra(BundleConstants.BUNDLE_DATA)
        if (moodBean is MoodBean) {
            mViewModel.moodEditBean.apply {
                this.moodItemId = moodBean.moodItemId
                this.moodIcon = moodBean.moodIcon
                this.moodTitle = moodBean.moodTitle
            }

        }

    }


    override var setStatusBar = {
        //默认使用白底黑字状态栏
        fullScreenAndBlackText()
    }

    override fun initView() {
        mBinding.appTitleView.setBackButtonRes(R.mipmap.common_ic_down_back) {
            onBackPressed()
            reportPoint("my-mood-edit-page-click") {
                type = "返回"
            }
        }



//        QMUIKeyboardHelper.setVisibilityEventListener(this) { isOpen, heightDiff ->
//            TLog.print("zl_log", "MoodEditActivity: Keyboard isOpen=%s，heightDiff=%s", isOpen, heightDiff);
//            false
//        }


        mBinding.etInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable) {
                if (s.toString().isNotEmpty() && s.toString().length > 50) {
                    T.ss("最多50个字哦")
                    mBinding.etInput.setText(s.substring(0, 50))
                    mBinding.etInput.setSelection(mBinding.etInput.text.length)
                }
            }
        })


        mBinding.llMoodName.clickWithTrigger {
            jumpForResultMoodGridActivity(
                this, RequestCodeConstants.REQUEST_CODE_MOOD_EDIT, PageSource.MOOD_EDIT_FOR_RESULT,
                MoodBean(
                    mViewModel.moodEditBean.moodItemId,
                    mViewModel.moodEditBean.moodIcon,
                    mViewModel.moodEditBean.moodTitle
                )
            )

            reportPoint("my-mood-edit-page-click") {
                type = "切换心情状态"
            }
        }

        mBinding.tvViewPermission.clickWithTrigger {
            mViewModel.getEachLikeList()
            reportPoint("my-mood-edit-page-click") {
                type = "可见范围"
            }
        }

        mBinding.rbSave.clickWithTrigger {
            mViewModel.submit(mBinding.etInput.text.toString()) {
                AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE)
            }
            reportPoint("my-mood-edit-page-click") {
                type = "就这样"
            }
        }

        reportPoint("my-mood-edit-page-expo")
    }

    override fun onBackPressed() {
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE)
    }

    override fun initData() {
        mViewModel.eachLikeLiveData.observe(this) {
            SetMoodViewPermissionDialog(this, "谁可以看", mViewModel.moodEditBean.permission, it) { permission ->
                mViewModel.moodEditBean.permission = permission
                setData()
            }.show()
        }

        mViewModel.moodEditDetailLiveData.observe(this) {
            mViewModel.moodEditBean.apply {
                this.detailResponse = it
                this.permission = it?.permission ?: MoodPermissionType.PUBLIC
                this.moodItemId = it?.moodItemId
                this.moodIcon = it?.moodIcon
                this.moodTitle = it?.moodTitle
                this.status = it?.status
                this.rejectReason = it?.rejectReason
                this.moodContent = it?.moodContent
            }
            mBinding.tvAudit.visible(!mViewModel.moodEditBean.rejectReason.isNullOrEmpty())
            mBinding.tvAudit.text = mViewModel.moodEditBean.rejectReason ?: ""
            mBinding.etInput.setText(mViewModel.moodEditBean.moodContent ?: "")
            mBinding.etInput.setSelection(mViewModel.moodEditBean.moodContent?.length ?: 0)
            setData()
            showSoftInput(mBinding.etInput, true)
        }


        if (mViewModel.pageSource != PageSource.MOOD_GRID_NEW_MOOD) {
            mViewModel.getMoodEditDetail()
        } else {
            setData()
        }

    }

    fun setData() {
        mBinding.ivMood.load(mViewModel.moodEditBean.moodIcon)
        mBinding.tvMoodName.text = mViewModel.moodEditBean.moodTitle ?: ""
        mBinding.tvViewPermission.text = mViewModel.moodEditBean.permission.toMoodViewPermissionText()
    }


    override fun onRetry() {
        val statePageBean = mViewModel.pageState.value
        if (statePageBean?.errCode == ErrorReason.QR_MOOD_TIME_OUT) {
            MePageRouter.jumpToMoodGridActivity(this, PageSource.MY_MOOD_DETAIL)
        } else {
            mViewModel.getMoodEditDetail()
        }
    }

    override fun getStateLayout() = mBinding.idStateLayout

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                RequestCodeConstants.REQUEST_CODE_MOOD_EDIT,//编辑表情
                -> {
                    val moodBean = data?.getSerializableExtra(BundleConstants.BUNDLE_DATA)
                    if (moodBean is MoodBean) {
//                        mViewModel.setMoodBean(moodBean)
                        mViewModel.moodEditBean.apply {
                            this.moodItemId = moodBean.moodItemId
                            this.moodIcon = moodBean.moodIcon
                            this.moodTitle = moodBean.moodTitle
                        }
                        setData()
                    }

                }
            }
        }
    }


}