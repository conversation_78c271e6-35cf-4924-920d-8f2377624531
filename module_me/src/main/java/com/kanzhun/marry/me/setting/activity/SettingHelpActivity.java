package com.kanzhun.marry.me.setting.activity;

import static com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt.fullScreenAndBlackText;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.common.AvoidOnResult;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.views.LinearLayoutManagerWrapper;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.imageviewer.ImageViewer;
import com.kanzhun.imageviewer.ImageViewerBuilder;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivitySettingHelpBinding;
import com.kanzhun.marry.me.databinding.MeItemPicHelperAddBinding;
import com.kanzhun.marry.me.databinding.MeItemReportPicBinding;
import com.kanzhun.marry.me.setting.bean.ReportPicBean;
import com.kanzhun.marry.me.setting.callback.SettingHelpCallback;
import com.kanzhun.marry.me.setting.model.AddBean;
import com.kanzhun.marry.me.setting.viewmodel.SettingHelpViewModel;
import com.kanzhun.marry.me.util.FeedbackReport;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.zhihu.matisse.Matisse;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

@RouterUri(path = MePageRouter.SETTING_HELP_ACTIVITY)
public class SettingHelpActivity extends FoundationVMActivity<MeActivitySettingHelpBinding, SettingHelpViewModel> implements SettingHelpCallback {
    BaseBinderAdapter adapter;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_setting_help;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    protected boolean shouldUseGrayStatusBar() {
        return true;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fullScreenAndBlackText(this, getDataBinding().activityMain, true);

        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(500);
        inputFilters[0] = filter;
        getDataBinding().etText.setFilters(inputFilters);
        adapter = new BaseBinderAdapter();
        LinearLayoutManagerWrapper linearLayoutManager = new LinearLayoutManagerWrapper(this);
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        getDataBinding().rvPic.setLayoutManager(linearLayoutManager);
        getDataBinding().rvPic.setAdapter(adapter);
        adapter.addItemBinder(ReportPicBean.class, new BaseDataBindingItemBinder<ReportPicBean, MeItemReportPicBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_report_pic;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemReportPicBinding> holder, MeItemReportPicBinding binding, ReportPicBean item) {
                binding.setItem(item);
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_delete, R.id.iv_pic);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemReportPicBinding> holder, @NonNull View view, ReportPicBean data, int position) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                int id = view.getId();
                if (id == R.id.iv_delete) {
                    setPicDelete(data.uri);
                } else if (id == R.id.iv_pic) {
                    ImageViewer.Companion.start(SettingHelpActivity.this, new Function1<ImageViewerBuilder, Unit>() {
                        @Override
                        public Unit invoke(ImageViewerBuilder imageViewerBuilder) {
                            imageViewerBuilder.views(getDataBinding().rvPic, R.id.iv_pic, getShowPicData(adapter.getData()));
                            imageViewerBuilder.urls(getViewModel().getImageUrisString());
                            imageViewerBuilder.position(position);
                            return null;
                        }
                    });
                }
            }
        });
        adapter.addItemBinder(AddBean.class, new BaseDataBindingItemBinder<AddBean, MeItemPicHelperAddBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_pic_helper_add;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemPicHelperAddBinding> holder, MeItemPicHelperAddBinding binding, AddBean item) {

            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemPicHelperAddBinding> holder, @NonNull View view, AddBean data, int position) {
                int count = 6 - getViewModel().getImageUris().size();
                PhotoSelectManager.jumpForGalleryOnlyImageResult((FragmentActivity) SettingHelpActivity.this, false, count, new AvoidOnResult.Callback() {
                    @Override
                    public void onActivityResult(int requestCode, int resultCode, Intent data) {
                        if (resultCode == RESULT_OK && data != null) {
                            List<Uri> result = Matisse.obtainResult(data);
                            setPicData(result);
                        }
                    }
                });
            }
        });
        getViewModel().getAddSuccess().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null && aBoolean) {
                    T.makeDefaultToast(BaseApplication.getApplication(), getResources().getString(R.string.me_feed_added), Toast.LENGTH_LONG).show();
                    AppUtil.finishActivity(SettingHelpActivity.this);
                }
            }
        });
        List<Object> data = new ArrayList<>();
        for (Uri uri : getViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add(new AddBean());
        }
        adapter.setList(data);

        new FeedbackReport().report();
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        String content = getViewModel().getEditContent().get();
        if (!TextUtils.isEmpty(content)) {
            if (TextUtils.isEmpty(content.trim())) {
                T.ss("内容不能全为空字符");
                return;
            }
        }

        getViewModel().requestSubmit();

    }

    private void setPicData(List<Uri> result) {
        if (LList.isEmpty(result)) {
            return;
        }
        getViewModel().getImageUris().addAll(result);
        List<Object> data = new ArrayList<>();
        for (Uri uri : getViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add(new AddBean());
        }
        adapter.setList(data);
    }

    private void setPicDelete(Uri delUri) {
        getViewModel().getImageUris().remove(delUri);
        List<Object> data = new ArrayList<>();
        for (Uri uri : getViewModel().getImageUris()) {
            data.add(new ReportPicBean(uri));
        }
        if (data.size() < 6) {
            data.add(new AddBean());
        }
        adapter.setList(data);
    }

    private List<? extends Object> getShowPicData(List<Object> data) {
        List<Object> pics = new ArrayList<>(data.size());
        for (Object o : data) {
            if (o instanceof ReportPicBean) {
                pics.add(o);
            } else {
                pics.add(null);
            }
        }
        return pics;
    }
}