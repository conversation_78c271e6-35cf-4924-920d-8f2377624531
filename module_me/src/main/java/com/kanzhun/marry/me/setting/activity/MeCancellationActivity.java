package com.kanzhun.marry.me.setting.activity;

import static com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt.fullScreenAndBlackText;

import android.os.Bundle;
import android.view.View;

import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityMeCancellationBinding;
import com.kanzhun.marry.me.setting.callback.MeCancellationCallback;
import com.kanzhun.marry.me.setting.fragment.MeCancellationFirstStepFragment;
import com.kanzhun.marry.me.setting.viewmodel.MeCancellationViewModel;

public class MeCancellationActivity extends FoundationVMActivity<MeActivityMeCancellationBinding, MeCancellationViewModel> implements MeCancellationCallback {

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_me_cancellation;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        fullScreenAndBlackText(this, getDataBinding().activityMain, true);
        getSupportFragmentManager().beginTransaction()
                .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                        0,
                        0,
                        R.anim.common_activity_slide_from_right_to_left_exit)
                .replace(R.id.ll_container, MeCancellationFirstStepFragment.getInstance())
                .commitAllowingStateLoss();
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }
}