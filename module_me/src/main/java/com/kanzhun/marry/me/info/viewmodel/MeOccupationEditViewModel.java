package com.kanzhun.marry.me.info.viewmodel;

import android.app.Application;
import android.content.Intent;
import android.text.TextUtils;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.CustomIndustryBean;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.foundation.model.profile.GuideItemBean;
import com.kanzhun.foundation.model.profile.IndustryBean;
import com.kanzhun.foundation.model.profile.IndustryCodeResponse;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;

public class MeOccupationEditViewModel extends FoundationViewModel {

    public MeOccupationEditViewModel(Application application) {
        super(application);
        requestIndustry();
    }

    public void initData(Intent intent) {
        if(intent.getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN, false)){
            initCareerCode = "";
            initCareer = intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1);
            initIndustryStr = intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_3);
            initIndustryCode = intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_2);
            editContentObservable.set(initCareer);
            initErrorDesc = "";
//            intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1);
        }else{
            ProfileMetaModel profileMetaModel = ProfileHelper.getInstance().getProfileMetaModel();
            if (profileMetaModel != null && profileMetaModel.baseInfo != null) {
                IndustryBean tmpIndustryBean = ProfileHelper.getInstance().getTmpIndustryBean();
                if (tmpIndustryBean != null && !TextUtils.equals(tmpIndustryBean.getCode(), profileMetaModel.baseInfo.industryCode)) {
                    initIndustryCode = tmpIndustryBean.getCode();
                    initCareer = "";
                    initCareerCode = "";
                    initErrorDesc = "";
                    initIndustryStr = "";
                } else {
                    initIndustryCode = profileMetaModel.baseInfo.industryCode;
                    initCareer = profileMetaModel.baseInfo.career;
                    initCareerCode = profileMetaModel.baseInfo.careerCode;
                    initErrorDesc = profileMetaModel.baseInfo.careerCertInfo;
                    initIndustryStr = profileMetaModel.baseInfo.industry;
                }

                industryCode = initIndustryCode;
                career = initCareer;
                careerCode = initCareerCode;
                occupationErrorDescObservable.set(initErrorDesc);
            }
        }

        GuideItemBean targetGuideItem = ProfileHelper.getInstance().getTargetGuideItem();
        if (targetGuideItem != null && indexObservable.get() > 0) {
            skipObservable.set(targetGuideItem.isCanSkip());
        }
    }

    public ObservableInt indexObservable = new ObservableInt();
    public ObservableInt countObservable = new ObservableInt();
    public ObservableBoolean skipObservable = new ObservableBoolean();
    // 获取所有地级市
    private MutableLiveData<List<IndustryBean>> industryLivaData = new MutableLiveData<>();
    // 搜索到的职业列表
    private MutableLiveData<List<IndustryBean>> occupationLiveData = new MutableLiveData<>();
    // 更新职业成功
    private MutableLiveData<String> updateSuccessLivaData = new MutableLiveData<>();
    // 输入框内容
    public ObservableField<String> editContentObservable = new ObservableField<>();
    // 错误提示
    public ObservableField<String> occupationErrorDescObservable = new ObservableField<>();
    public String initErrorDesc;
    // 搜索结果列表
    public ObservableField<List<IndustryBean>> searchResultObservable = new ObservableField<>();

    public String initIndustryCode;// 行业码
    public String initIndustryStr;// 行业名字
    public String initCareerCode;// 职业码，空为自定义输入内容
    public String initCareer;// 职业

    public String industryCode;
    public String career;
    public String careerCode;

    /**
     * 请求行业数据
     */
    private void requestIndustry() {
        setShowProgressBar();
        Observable<BaseResponse<IndustryCodeResponse>> commonAddressObservable
                = RetrofitManager.getInstance().createApi(MeApi.class).getIndustryCode();
        HttpExecutor.execute(commonAddressObservable, new BaseRequestCallback<IndustryCodeResponse>() {
            @Override
            public void onSuccess(IndustryCodeResponse data) {
            }

            @Override
            public void handleInChildThread(IndustryCodeResponse data) {
                super.handleInChildThread(data);
                if (LList.isEmpty(data.industry)) {
                    return;
                }
                industryLivaData.postValue(data.industry);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
    }

    /**
     * 根据名称查找职业，本地过滤
     */
    public void searchOccupation(String content) {
        List<IndustryBean> careers = new ArrayList<>();
        HashSet<String> hashSet = new HashSet<>();
        if (!TextUtils.isEmpty(content)) {
            List<IndustryBean> industries = industryLivaData.getValue();
            if (!LList.isEmpty(industries)) {
                for (IndustryBean industry : industries) {
                    if (industry != null
                            && !LList.isEmpty(industry.getCareer())) {
                        for (IndustryBean career : industry.getCareer()) {
                            if (career != null && career.getName() != null && career.getName().contains(content)) {
                                if(!hashSet.contains(career.getCode())){
                                    hashSet.add(career.getCode());
                                    careers.add(career);
                                }
                            }
                        }
                    }
                }
            }
        }

        if(!TextUtils.isEmpty(content) && content.equals(career)){

        }else {
            careerCode = "";// 搜索时，careerCode重置
        }
        TLog.print("careerCode","careerCode:"+careerCode);

        if(!TextUtils.isEmpty(content)){
            careers.add(new CustomIndustryBean());
        }
        searchResultObservable.set(careers);
        occupationLiveData.setValue(careers);
    }

    /**
     * 更新职业
     */
    public void updateOccupation() {

        Map<String, Object> params = new HashMap<>();
        params.put("industryCode", industryCode);
        params.put("careerCode", careerCode == null?"":careerCode);
//        if(TextUtils.isEmpty(careerCode)){
            params.put("career", career);
//        }

        setShowProgressBar();
        HttpExecutor.requestSimplePost(URLConfig.URL_ME_OCCUPATION_UPDATE, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                ExecutorFactory.execLocalTask(new Runnable() {
                    @Override
                    public void run() {
                        updateSuccessLivaData.postValue(career);
                    }
                });
            }

            @Override
            public void dealFail(ErrorReason reason) {
                occupationErrorDescObservable.set(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<List<IndustryBean>> getIndustryLivaData() {
        return industryLivaData;
    }

    public MutableLiveData<List<IndustryBean>> getOccupationLiveData() {
        return occupationLiveData;
    }

    public MutableLiveData<String> getUpdateSuccessLivaData() {
        return updateSuccessLivaData;
    }

}