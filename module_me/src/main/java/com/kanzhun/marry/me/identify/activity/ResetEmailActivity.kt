package com.kanzhun.marry.me.identify.activity

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.theme.O2Theme
import com.kanzhun.common.base.compose.ui.O2Toolbar
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.identify.viewmodel.ResetEmailViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [MePageRouter.ME_RESET_EMAIL_ACTIVITY])
class ResetEmailActivity : BaseComposeActivity() {

    val viewModel: ResetEmailViewModel by lazy {
        ViewModelProvider(this)[ResetEmailViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        immersionBar {
            statusBarDarkFont(true)
        }

        viewModel.getMailResult()
        reportPoint("certify-company-email-submit-expo"){
            source = getSourceStr()
        }
    }

    @Composable
    override fun OnSetContent() {
        RootView(viewModel)
    }

    private fun getSourceStr(): String? {
        when(intent.getPageSource()){
            PageSource.AUTH_CENTER_ACTIVITY ->{
                return "认证中心"
            }
            PageSource.EDU_EMAIL_SEND ->{
                return "邮件认证提交后"
            }
            else ->{
                return ""
            }
        }
    }

    private @Composable
    fun RootView(viewModel: ResetEmailViewModel) {
        val emailSuccessBean by viewModel.emailSuccessBean.observeAsState()
        val emailText = emailSuccessBean?.email ?: ""
        val name = emailSuccessBean?.companyName + emailSuccessBean?.career

        Column(modifier = Modifier.verticalScroll(state = rememberScrollState())) {
            O2Toolbar(
                onBackClick = {
                    onBackPressed()
                    reportPoint("certify-company-email-submit-click"){
                        type = "返回"
                        source = getSourceStr()
                } },
            )

            Column(
                modifier = Modifier.padding(
                    start = 20.dp,
                    top = 30.dp,
                    end = 20.dp,
                    bottom = 30.dp
                )
            ) {

                Text(
                    text = "您的邮件已发送至：",
                    style = TextStyle(
                        color = colorResource(R.color.common_color_191919),
                        fontSize = 28.sp,
                        fontWeight = FontWeight(weight = 700)
                    ),

                    )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = emailText,
                    style = TextStyle(
                        color = colorResource(R.color.common_color_191919),
                        fontSize = 16.sp,
                        fontWeight = FontWeight(weight = 400)
                    ),

                    )

                Spacer(modifier = Modifier.height(24.dp))


                ConstraintLayout {
                    val (step1, line, step2) = createRefs()
                    PreviewStep("1", "第一步：打开企业邮箱", modifier = Modifier.constrainAs(step1) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top)
                    })

                    Box(
                        modifier = Modifier
                            .height(40.dp)
                            .width(4.dp)
                            .background(
                                color = R.color.common_color_F5F5F5.colorResource(),
                                shape = RoundedCornerShape(74.dp)
                            )
                            .constrainAs(line) {
                                start.linkTo(step1.start, margin = 8.dp)
                                top.linkTo(step1.bottom, margin = 7.dp)
                            }
                    ) {

                    }
                    PreviewStep(
                        "2",
                        "第二步：点击邮件中【确认邮箱地址】即可完成邮件确认",
                        modifier = Modifier.constrainAs(step2) {
                            start.linkTo(step1.start)
                            top.linkTo(line.bottom, margin = 7.dp)
                        })


                }

                Spacer(modifier = Modifier.height(12.dp))

                SendEmailSureCard(name)


                Spacer(modifier = Modifier.height(28.dp))

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        painter = R.mipmap.me_icon_reset_eamil_warn.painterResource(),
                        contentDescription = null,
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(20.dp)
                    )

                    Spacer(modifier = Modifier.width(6.dp))
                    Text(
                        text = "没有收到邮件？",
                        style = TextStyle(
                            color = colorResource(R.color.common_color_292929),
                            fontSize = 16.sp,
                            fontWeight = FontWeight(weight = 500)
                        ),

                        )
                }
                Spacer(modifier = Modifier.height(13.dp))

                Text(
                    text = "1：请打开您的邮箱，并在“垃圾邮件箱”中检查",
                    style = TextStyle(
                        color = colorResource(R.color.common_color_5E5E5E),
                        fontSize = 14.sp,
                        fontWeight = FontWeight(weight = 400)
                    ),

                    )
                Spacer(modifier = Modifier.height(12.dp))

                val styleText0 = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            color = colorResource(R.color.common_color_5E5E5E)
                        )
                    ) {
                        append("2：若仍未找到，请检查您的邮件地址是否有误")
                    }
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            color = colorResource(R.color.common_color_0046BD)
                        )
                    ) {
                        append("${emailText}")
                    }
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            color = colorResource(R.color.common_color_5E5E5E)
                        )
                    ) {
                        append("，若您的邮件地址填写错误，")
                    }
                    withStyle(
                        style = SpanStyle(
                            color = colorResource(R.color.common_color_0046BD),
                            fontSize = 14.sp,
                            textDecoration = TextDecoration.Underline
                        )
                    ) {
                        pushStringAnnotation("onClickReset", annotation = "重新发送")
                        append("可点击这里重新发送")
                    }

                }

                ClickableText(
                    text = styleText0,
                    onClick = {
                        //重新发送事件
                        styleText0.getStringAnnotations(tag = "onClickReset", start = it, end = it)
                            .firstOrNull()?.let {
                                reportPoint("certify-company-email-submit-click"){
                                    type = "可点击这里重新发送"
                                    source = getSourceStr()
                                }
                                MePageRouter.jumpToMeCompanyAuthActivity(
                                    this@ResetEmailActivity,
                                    enterEditEmail = true
                                )
                                AppUtil.finishActivityDelay(this@ResetEmailActivity)
                            }
                    }
                )

                Spacer(modifier = Modifier.height(12.dp))

                val styleText = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            color = colorResource(R.color.common_color_5E5E5E)
                        )
                    ) {
                        append("3：若仍未找到，您可以使用工卡或社保截图等进行公司认证，")
                    }
                    withStyle(
                        style = SpanStyle(
                            color = colorResource(R.color.common_color_0046BD),
                            fontSize = 14.sp,
                            textDecoration = TextDecoration.Underline
                        )
                    ) {
                        pushStringAnnotation("onClickOne", annotation = "其他方式认证")
                        append("可点击这里使用其他方式认证")
                    }

                }
                ClickableText(
                    text = styleText,
                    onClick = {
                        //其他方式事件
                        styleText.getStringAnnotations(tag = "onClickOne", start = it, end = it)
                            .firstOrNull()?.let {
                                reportPoint("certify-company-email-submit-click"){
                                    type = "可点击这里使用其他方式认证"
                                    source = getSourceStr()
                                }
                                showTwoButtonDialog(
                                    "温馨提示",
                                    "点击“确定继续“后，您的邮箱认证链接将会失效，但您可以修改公司名称或者使用其他方式认证，是否继续",
                                    false,
                                    false,
                                    "确定继续",
                                    {

                                        reportPoint("certify-company-email-submit-click"){
                                            type = "确定继续"
                                            source = getSourceStr()
                                        }
                                        viewModel.expireEmail {
                                            MePageRouter.jumpToMeCompanyAuthActivity(this@ResetEmailActivity)
                                            AppUtil.finishActivityDelay(this@ResetEmailActivity)
                                        }
                                    },
                                    "取消",
                                    {
                                        reportPoint("certify-company-email-submit-click"){
                                            type = "取消"
                                            source = getSourceStr()
                                        }
                                    },
                                )

                            }
                    }
                )
            }
        }
    }

    @Preview(showBackground = true)
    @Composable
    private fun PreviewResetEmailActivity() {
        O2Theme {
            RootView(viewModel())
        }
    }

    @Composable
    private fun PreviewStep(num: String, text: String, modifier: Modifier = Modifier) {
        Row(modifier = modifier) {
            Box(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .size(20.dp)
                    .background(
                        color = colorResource(R.color.common_color_292929),
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = num,
                    style = TextStyle(
                        color = colorResource(R.color.color_white),
                        fontSize = 13.sp,
                        fontWeight = FontWeight(weight = 500)
                    )
                )
            }


            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = text,
                style = TextStyle(
                    color = colorResource(R.color.common_color_191919),
                    fontSize = 14.sp,
                    fontWeight = FontWeight(weight = 500)
                )
            )
        }
    }

    @Composable
    private fun SendEmailSureCard(str: String) {
        ConstraintLayout(
            modifier = Modifier.padding(start = 28.dp)
                .clip(shape = RoundedCornerShape(16.dp))

        ) {
            val (idText1, idText2, idBtn,image,imagePoint) = createRefs()
            Image(
                painter = R.mipmap.common_bg_theme_green_and_pink.painterResource(),
                contentScale = ContentScale.FillWidth,
                contentDescription = null,
            )
            Text(
                text = "亲爱的看准用户",
                style = TextStyle(
                    color = colorResource(R.color.common_color_292929),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(weight = 700),
                ),
                modifier = Modifier.constrainAs(idText1) {
                    start.linkTo(parent.start, 16.dp)
                    top.linkTo(parent.top, 24.dp)
                }
            )


            Text(
                text = "你正在认证当前职业：${str}，请确认邮箱地址以完成认证。",
                style = TextStyle(
                    color = colorResource(R.color.common_color_292929),
                    fontSize = 13.sp,
                    fontWeight = FontWeight(weight = 400)
                ),
                modifier = Modifier.padding(horizontal = 16.dp).constrainAs(idText2) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(idText1.bottom, 12.dp)
                }

            )

            Box(modifier = Modifier.padding(horizontal = 16.dp)
                .constrainAs(idBtn) {
                    start.linkTo(parent.start)
                    top.linkTo(idText2.bottom, 24.dp)
                    end.linkTo(parent.end)
                }) {

                Box(
                    modifier = Modifier.fillMaxWidth()
                        .height(46.dp)
                        .background(R.color.common_color_191919.colorResource(), shape = RoundedCornerShape(24.dp)),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "点击确认邮箱地址",
                        style = TextStyle(
                            fontSize = 16.sp,
                            color = colorResource(R.color.color_white),
                            fontWeight = FontWeight(500)
                        ),
                    )
                }
            }

            Image(
                painter = R.mipmap.me_image_reset_email_hand_point.painterResource(),
                contentDescription = null,
                contentScale = ContentScale.Fit,
                modifier = Modifier.width(24.dp).height(24.dp).constrainAs(imagePoint) {
                    end.linkTo(idBtn.end,29.dp)
                    top.linkTo(idBtn.top, 16.dp)
                }
            )

            Image(
                painter = R.mipmap.me_image_reset_email_hand.painterResource(),
                contentDescription = null,
                contentScale = ContentScale.Fit,
                modifier = Modifier.width(28.dp).height(37.dp).constrainAs(image) {
                    end.linkTo(idBtn.end,25.dp)
                    top.linkTo(idBtn.top, 25.dp)
                }
            )


        }
    }
}


