package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.parsePageSourceFromBundle;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import android.view.View;

import androidx.lifecycle.Observer;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.bean.UserSchoolGetBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.identify.EducationIdentifyActivityHandler;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.marry.me.util.EduSubTitleHandler;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityGraduationIdentifyBinding;
import com.kanzhun.marry.me.identify.callback.GraduationIdentifyCallback;
import com.kanzhun.marry.me.identify.viewmodel.GraduationIdentifyViewModel;
import com.sankuai.waimai.router.annotation.RouterUri;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR>
 * @date 2022/3/22.
 */
@RouterUri(path = MePageRouter.ME_Identify_CODE_ACTIVITY)
public class GraduationIdentifyActivity extends FoundationVMActivity<MeActivityGraduationIdentifyBinding, GraduationIdentifyViewModel> implements GraduationIdentifyCallback {
    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_graduation_identify;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initEditText();
        getViewModel().getSuccessLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                EducationIdentifyActivityHandler.Companion.reset();
                AppUtil.startActivity(GraduationIdentifyActivity.this, new Intent(GraduationIdentifyActivity.this, IdentifyResultActivity.class));
                setResult(Activity.RESULT_OK);
                AppUtil.finishActivityDelay(GraduationIdentifyActivity.this);
            }
        });

        getDataBinding().layoutOptionCode.editContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADNUM_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(getViewModel().getEduLevelName());
                        pointBean.setType("证书编号");
                        return null;
                    }
                });
            }
        });

        startTime = System.currentTimeMillis();
        getDataBinding().layoutOptionCode.editContent.post(new Runnable() {
            @Override
            public void run() {
                QMUIKeyboardHelper.showKeyboard(getDataBinding().layoutOptionCode.editContent,true);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADNUM_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(getViewModel().getEduLevelName());
                        pointBean.setType("证书编号");
                        return null;
                    }
                });
            }
        });

        getViewModel().userSchoolGetBeanLiveData.observe(this, new Observer<UserSchoolGetBean>() {
            @Override
            public void onChanged(UserSchoolGetBean userSchoolGetBean) {
                EduSubTitleHandler.Companion.handlerUserSchoolGetBean(GraduationIdentifyActivity.this,getDataBinding().tvAssist,userSchoolGetBean,"certify-education-gradnum-click", getPageSource());
            }
        });

    }

    private PageSource getPageSource() {
        return parsePageSourceFromBundle(getIntent());
    }

    @Override
    protected void onResume() {
        super.onResume();
        getViewModel().getData(this);
    }

    private long startTime;

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADNUM_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().getEduLevelName());
                pointBean.setDuration(System.currentTimeMillis() - startTime);
                return null;
            }
        });
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().layoutOptionCode.editContent);
    }

    private void initEditText() {
        QMUIKeyboardHelper.showKeyboard(getDataBinding().layoutOptionCode.editContent, true);
        getDataBinding().layoutOptionCode.editContent.setFilters(new InputFilter[]{new InputFilter.LengthFilter(18)});
        getDataBinding().layoutOptionCode.editContent.setKeyListener(DigitsKeyListener.getInstance(getResources().getString(R.string.common_input_number)));
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADNUM_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().getEduLevelName());
                pointBean.setType("返回");
                return null;
            }
        });
    }

    @Override
    public void clickSubmit(View view) {
        String code = getViewModel().getCode().getEditContent().get();
        if (TextUtils.isEmpty(code)) {
            return;
        }
        int length = code.length();
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                .setTitle(getResources().getString(R.string.me_identify_type_title, length > 16 ? getResources().getString(R.string.me_diploma) : getResources().getString(R.string.me_degree_diploma)))
                .setContent(getResources().getString(R.string.me_identify_num_input_num, length))
                .setPositiveText(getResources().getString(R.string.common_sure_to_sub))
                .setNegativeText(getResources().getString(R.string.me_re_edit_num))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                        getViewModel().requestSubmit(getViewModel().getEduLevelName());
                        LiveEventBus.post(LivedataKeyMe.AUTH_CENTER_ACTIVITY_REFRESH, true);
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {

                    }
                });
        builder.create().show();
    }
}
