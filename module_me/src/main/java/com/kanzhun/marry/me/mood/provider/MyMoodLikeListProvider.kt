package com.kanzhun.marry.me.mood.provider

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.api.response.MoodThumbBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.marry.me.databinding.MeItemMyMoodLikeListBinding
import com.kanzhun.marry.me.mood.item.MyMoodLikeListItemBean

class MyMoodLikeListProvider(val activity: FragmentActivity, val itemClick: (MoodThumbBean) -> Unit = {}) : BaseItemProvider<BaseListItem, MeItemMyMoodLikeListBinding>() {
    override fun onBindItem(binding: MeItemMyMoodLikeListBinding, item: BaseListItem) {
        if (item is MyMoodLikeListItemBean) {
            binding.run {
                val thumbBean = item.moodThumbBean
                ivAvatar.load(thumbBean.tinyAvatar)
                tvUserName.text = thumbBean.nickName
                tvTime.text = thumbBean.createTime
                root.clickWithTrigger {
                    itemClick(thumbBean)
                }
            }
        }

    }
}

