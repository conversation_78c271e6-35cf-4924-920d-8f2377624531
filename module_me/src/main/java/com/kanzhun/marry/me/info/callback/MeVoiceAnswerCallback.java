package com.kanzhun.marry.me.info.callback;

import com.kanzhun.common.callback.TitleBarCallback;

import java.io.File;

public interface MeVoiceAnswerCallback extends TitleBarCallback {
    void clickReSelect();

    void voiceRecordIng();

    void voiceRecordStart();

    void voiceRecordSuccess(boolean haveSuccess);

    void voiceSave(File file,int duration,int[] wave);

    void voiceRecordDownload();
}