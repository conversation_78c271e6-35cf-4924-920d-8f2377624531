package com.kanzhun.marry.me.entrance.views

import android.text.TextUtils
import android.widget.LinearLayout
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.rememberAsyncImagePainter
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.compose.ext.attachOwners
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.base.compose.ui.RoundedSquareProgress
import com.kanzhun.foundation.kernel.account.Account
import com.kanzhun.foundation.model.profile.UserTabModel
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.entrance.callback.MeCallback
import com.kanzhun.marry.me.entrance.viewmodel.MeViewModel
import com.kanzhun.marry.me.info.activity.PersonEditActivity.Companion.PAGE_EDIT
import com.kanzhun.marry.me.info.activity.PersonEditActivity.Companion.PAGE_PREVIEW
import kotlinx.coroutines.delay

private fun getMeHeader(idTopBar: LinearLayout): ComposeView? {
    return idTopBar.findViewById<ComposeView>(R.id.id_me_header_new)
}

fun shouldRemoveMeHeader(idTopBar: LinearLayout) {
    idTopBar.removeView(getMeHeader(idTopBar))
}

fun shouldAddMeHeader(idTopBar: LinearLayout, viewModel: MeViewModel, callback: MeCallback) {
    if (getMeHeader(idTopBar) == null) {
        val ctx = idTopBar.context
        idTopBar.addView(
            ComposeView(ctx)
                .apply {
                    id = R.id.id_me_header_new

                    onSetViewTreeContent {
                        val userTabModel by viewModel.userTabModelMutableLiveData.observeAsState()

                        MeHeader(
                            userTabModel = userTabModel,
                            onClickAvatar = {
                                callback.clickEditInfo(userTabModel?.apply {
                                    index = PAGE_PREVIEW
                                    isNewPage = true
                                })

                                reportPoint("F4-avatar-click")
                            },
                            onClickEditor = {
                                callback.clickEditInfo(userTabModel?.apply {
                                    index = PAGE_EDIT
                                    isNewPage = true
                                })
                            },
                            onClickMood = { callback.clickEditMood(userTabModel) }
                        )
                    }

                    (ctx as? ComponentActivity)?.let { owner ->
                        attachOwners(
                            owner = owner,
                            targetView = this
                        )
                    }
                },
            0,
            LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        )
    }
}

@Composable
fun MeHeader(
    userTabModel: UserTabModel? = null,
    onClickAvatar: () -> Unit = {},
    onClickEditor: () -> Unit = {},
    onClickMood: () -> Unit = {},
    inPreview: Boolean = false
) {
    ConstraintLayout(
        modifier = Modifier
            .padding(bottom = 24.dp)
            .noRippleClickable {
                onClickEditor()
            }
    ) {
        val (avatar, rectProgress, txtProgress, name, editor, mood) = createRefs()

        var progress by remember { mutableIntStateOf(0) }
        val targetProgress = userTabModel?.baseInfo?.userInfoProcess ?: 0
        LaunchedEffect(targetProgress) {
            while (progress < targetProgress) {
                progress += 1
                delay(10)
            }
        }

        RoundedSquareProgress(
            modifier = Modifier
                .size(width = 100.dp, height = 120.dp)
                .constrainAs(rectProgress) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                },
            progress = progress,
            cornerRadius = 22,
            trackBrush = Brush.linearGradient(
                colors = listOf(
                    Color(0xFFD7D7D7),
                    Color(0xFFD7D7D7)
                )
            ),
            progressBrush = Brush.linearGradient(
                0.0f to Color(0xFFEB66FF),
                1.0f to Color(0xFF4281FF),
            )
        )

        if (!TextUtils.isEmpty(userTabModel?.baseInfo?.tinyAvatar)) {
            Image(
                painter = rememberAsyncImagePainter(model = userTabModel?.baseInfo?.tinyAvatar),
                contentDescription = "image description",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .padding(8.dp)
                    .constrainAs(avatar) {
                        top.linkTo(rectProgress.top)
                        start.linkTo(rectProgress.start)
                        end.linkTo(rectProgress.end)
                        bottom.linkTo(rectProgress.bottom)

                        width = Dimension.fillToConstraints
                        height = Dimension.fillToConstraints
                    }
                    .clip(shape = RoundedCornerShape(16.dp))
                    .noRippleClickable {
                        onClickAvatar()
                    }
            )
        } else {
            val composition by rememberLottieComposition(
                LottieCompositionSpec.Asset(
                    if (userTabModel?.baseInfo?.gender == Account.ACCOUNT_GENDER_MEN || inPreview)
                        "me_avatar_male/male2.json" else "me_avatar_female/femaleavatar2.json"
                ),
            )
            LottieAnimation(
                composition = composition,
                iterations = LottieConstants.IterateForever,
                modifier = Modifier
                    .padding(8.dp)
                    .constrainAs(avatar) {
                        top.linkTo(rectProgress.top)
                        start.linkTo(rectProgress.start)
                        end.linkTo(rectProgress.end)
                        bottom.linkTo(rectProgress.bottom)

                        width = Dimension.fillToConstraints
                        height = Dimension.fillToConstraints
                    }
                    .clip(shape = RoundedCornerShape(16.dp))
                    .noRippleClickable {
                        onClickAvatar()
                    }
            )
        }

        Box(
            modifier = Modifier
                .padding(8.dp)
                .size(width = 52.dp, height = 32.dp)
                .constrainAs(txtProgress) {
                    end.linkTo(avatar.end)
                    bottom.linkTo(avatar.bottom)
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.mipmap.me_ic_profile_progress),
                contentDescription = null,
                modifier = Modifier.size(width = 52.dp, height = 32.dp)
            )

            Text(
                text = "${userTabModel?.baseInfo?.userInfoProcess ?: 0}%",
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFFFFFFFF),
                ),
                modifier = Modifier.padding(top = 8.dp, start = 8.dp)
            )
        }

        Text(
            text = if (!TextUtils.isEmpty(userTabModel?.baseInfo?.nickName)) userTabModel?.baseInfo?.nickName
                ?: "昵称未填写" else "昵称未填写",
            style = TextStyle(
                fontSize = 28.sp,
                fontFamily = boldFontFamily(),
                fontWeight = FontWeight(900),
                color = Color(0xFF191919),
            ),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(name) {
                top.linkTo(avatar.top, 14.dp)
                start.linkTo(avatar.end, 16.dp)
                end.linkTo(parent.end)

                width = Dimension.fillToConstraints
            }
        )

        Row(
            modifier = Modifier
                .background(color = Color(0xFF292929), shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 10.dp, vertical = 6.dp)
                .constrainAs(editor) {
                    start.linkTo(name.start)
                    top.linkTo(name.bottom, 10.dp)
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Image(
                painter = painterResource(id = R.mipmap.me_ic_editor),
                contentDescription = "image description",
                modifier = Modifier.size(14.dp)
            )

            Text(
                text = "编辑资料",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFFFFFFFF),
                )
            )
        }

        /*
            if (userTabModel.moodInfo == null || TextUtils.isEmpty(userTabModel.moodInfo.getEncMoodId())) {
                getDataBinding().headerContainer.idIvMood.setImageResource(R.mipmap.common_ic_add_mood);
                getDataBinding().headerContainer.idTvMood.setText("心情状态");
            } else {
                getDataBinding().headerContainer.idIvMood.load(userTabModel.moodInfo.getMoodIcon());
                getDataBinding().headerContainer.idTvMood.setText(userTabModel.moodInfo.getMoodTitle());
                getDataBinding().headerContainer.ivAudit.setVisibility(!TextUtils.isEmpty(userTabModel.moodInfo.getRejectReason()) ? View.VISIBLE : View.GONE);
            }
         */

        val hasNotSetMood =
            userTabModel?.moodInfo == null || TextUtils.isEmpty(userTabModel.moodInfo?.encMoodId)

        Row(
            modifier = Modifier
                .background(color = Color(0xFFEBEBEB), shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 10.dp, vertical = 6.dp)
                .constrainAs(mood) {
                    start.linkTo(editor.end, 8.dp)
                    top.linkTo(editor.top)
                    bottom.linkTo(editor.bottom)
                    end.linkTo(parent.end)
                    width = Dimension.preferredWrapContent
                    horizontalBias = 0f // Align to start when space is limited
                }
                .noRippleClickable {
                    onClickMood()
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            if (!hasNotSetMood // 有设置过心情
                && !TextUtils.isEmpty(
                    userTabModel?.moodInfo?.rejectReason // 异步机审驳回的时候，会返回该字段
                )
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.common_ic_warning),
                    contentDescription = "image description",
                    modifier = Modifier.size(20.dp)
                )
            }

            if (hasNotSetMood) {
                Image(
                    painter = painterResource(id = R.mipmap.common_ic_add_mood),
                    contentDescription = "image description",
                    modifier = Modifier.size(20.dp)
                )
            } else {
                Image(
                    painter = rememberAsyncImagePainter(model = userTabModel?.moodInfo?.moodIcon),
                    contentDescription = "image description",
                    modifier = Modifier.size(20.dp)
                )
            }

            Text(
                text = if (hasNotSetMood) "心情状态" else userTabModel?.moodInfo?.moodTitle
                    ?: "心情状态",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f, fill = false)
            )
        }

    }
}

@Preview(showBackground = true, widthDp = 480)
@Composable
private fun PreviewMeHeader() {
    MeHeader(userTabModel = UserTabModel().apply {
        baseInfo = UserTabModel.BaseInfo().apply {
            nickName =
                "小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明"
            avatar =
                "https://img.alicdn.com/imgextra/i4/O1CN01QY2Y1d1OQ1Y1d1OQ1_!!6000000002637-2-tps-200-200.jpg"
        }
    }, inPreview = true)
}
@Preview(showBackground = true, widthDp = 320)
@Composable
private fun PreviewMeHeader2() {
    MeHeader(userTabModel = UserTabModel().apply {
        baseInfo = UserTabModel.BaseInfo().apply {
            nickName =
                "小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明小明"
            avatar =
                "https://img.alicdn.com/imgextra/i4/O1CN01QY2Y1d1OQ1Y1d1OQ1_!!6000000002637-2-tps-200-200.jpg"
        }
    }, inPreview = true)
}