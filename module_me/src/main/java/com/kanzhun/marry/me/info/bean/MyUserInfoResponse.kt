package com.kanzhun.marry.me.info.bean

import com.kanzhun.foundation.model.profile.UserTipsBean
import com.kanzhun.foundation.model.profile.UserTipsBeanV2
import com.kanzhun.marry.me.info.bean.adapterbean.MyInfoBaseBean

class MyUserInfoResponse {
    lateinit var list:MutableList<MyInfoBaseBean>
    var userInfoProcess:Int = 0
    var canPreview:Int = 0
    var canNotPreviewReason:String? = null
    var userTipsBean:UserTipsBean? = null
    var userTipsBeanV2: UserTipsBeanV2? = null
}