package com.kanzhun.marry.me.info.activity

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.entity.StatePageBean
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.kotlin.ui.statelayout.Status
import com.kanzhun.common.kotlin.ui.statusbar.useBlackTextStatusBar
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.model.profile.QuestionTemplateModel
import com.kanzhun.foundation.model.profile.QuestionTemplateModel.QuestionInfoBean
import com.kanzhun.foundation.model.profile.QuestionTemplateModel.QuestionTemplateBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.views.ViewPager2Helper
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityMeQuestionSelectBinding
import com.kanzhun.marry.me.databinding.MeItemQustionSelectTabBinding
import com.kanzhun.marry.me.info.viewmodel.MeSelectQuestionViewModel
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.sankuai.waimai.router.annotation.RouterUri
import net.lucode.hackware.magicindicator.buildins.ArgbEvaluatorHolder
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView.OnPagerTitleChangeListener

/**
 * 选择问题页面
 */
@RouterUri(path = [MePageRouter.ME_QUESTION_AND_ANSWER_ACTIVITY])
class MeQuestionSelectActivity : BaseBindingActivity<MeActivityMeQuestionSelectBinding, MeSelectQuestionViewModel>() {

    override var setStatusBar = {
        useBlackTextStatusBar(R.color.common_color_F5F5F5.toResourceColor())
    }
    private lateinit var adapter: BaseQuickAdapter<QuestionTemplateBean,BaseViewHolder>


    override fun preInit(intent: Intent) {
        mViewModel.answerType = intent.getIntExtra(BundleConstants.BUNDLE_TYPE, MePageRouter.TYPE_ANSWER_TEXT)
        mViewModel.isSelectMode = intent.getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN,false)
    }

    override fun initView() {
        mBinding.titleBar.asBackButton()
        mBinding.titleBar.setTitle(R.string.me_please_select_question)
    }

    private fun initViewPager(questionTemplateModel: QuestionTemplateModel) {
        adapter = object:BaseQuickAdapter<QuestionTemplateBean,BaseViewHolder>(R.layout.me_view_question_select_pager_item){
            override fun convert(holder: BaseViewHolder, item: QuestionTemplateBean) {
                val stateLayout = holder.getView<StateLayout>(R.id.state_layout)

                if(item.questionInfos.isNullOrEmpty()){
                    stateLayout.showEmpty(StatePageBean(Status.EMPTY,"全部问题都答完了~"))
                }else{
                    stateLayout.showContent()
                    val recyclerView = holder.getView<RecyclerView>(R.id.recyclerview)
                    val itemAdapter = object :BaseQuickAdapter<QuestionInfoBean,BaseViewHolder>(R.layout.me_item_question_select){
                        override fun convert(h: BaseViewHolder, question: QuestionInfoBean) {
                            h.setText(R.id.tv_title,question.name)
                            h.itemView.clickWithTrigger {
                                if(mViewModel.isSelectMode){
                                    Intent().apply {
                                        this.putExtra(BundleConstants.BUNDLE_DATA,question)
                                        setResult(RESULT_OK,this)
                                        finish()
                                    }
                                }else{
                                    if(mViewModel.isEditTextAnswer()){
                                        MePageRouter.jumpToEditTextAnswerActivity(null,question)
                                    } else{
                                        MePageRouter.jumpToEditVoiceAnswerActivity(null,question)
                                    }
                                }

                            }
                        }

                    }
                    itemAdapter.setList(item.questionInfos)
                    recyclerView.clipToPadding = false
                    recyclerView.adapter = itemAdapter
                }

            }

        }

        adapter.setNewInstance(questionTemplateModel.questionGroup)
        mBinding.viewPager.setAdapter(adapter)
    }

    private fun initMagicIndicator(questionTemplateModel: QuestionTemplateModel) {
        val questionGroup = questionTemplateModel.questionGroup
        val commonNavigator = CommonNavigator(this)
        val padding = QMUIDisplayHelper.dp2px(this, 11)
        commonNavigator.rightPadding = padding
        commonNavigator.leftPadding = padding
        commonNavigator.adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return questionGroup?.size ?: 0
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                val questionTemplateBean = questionGroup!![index]
                val commonPagerTitleView = CommonPagerTitleView(context)
                val tabBinding: MeItemQustionSelectTabBinding = MeItemQustionSelectTabBinding.inflate(LayoutInflater.from(context))
                val textSelectedColor = R.color.common_color_191919.toResourceColor()
                val textUnSelectedColor = R.color.common_color_B2B2B2.toResourceColor()

                tabBinding.tvName.setText(questionTemplateBean.name)
                tabBinding.tvEnName.setText(resources.getString(R.string.me_question_select_en_name, questionTemplateBean.enName))
                commonPagerTitleView.setContentView(tabBinding.getRoot())
                commonPagerTitleView.onPagerTitleChangeListener = object : OnPagerTitleChangeListener {
                    override fun onSelected(index: Int, totalCount: Int) {
                        tabBinding.tvName.setTextColor(textSelectedColor)
                        tabBinding.tvEnName.setTextColor(textSelectedColor)
                    }

                    override fun onDeselected(index: Int, totalCount: Int) {
                        tabBinding.tvName.setTextColor(textUnSelectedColor)
                        tabBinding.tvEnName.setTextColor(textUnSelectedColor)
                    }

                    override fun onLeave(index: Int, totalCount: Int, leavePercent: Float, leftToRight: Boolean) {
                        val color = ArgbEvaluatorHolder.eval(leavePercent, R.color.common_color_191919.toResourceColor(), R.color.common_color_B2B2B2.toResourceColor())
                        tabBinding.tvName.setTextColor(color)
                        tabBinding.tvEnName.setTextColor(color)
                    }

                    override fun onEnter(index: Int, totalCount: Int, enterPercent: Float, leftToRight: Boolean) {
                        val color = ArgbEvaluatorHolder.eval(enterPercent, R.color.common_color_B2B2B2.toResourceColor(), R.color.common_color_191919.toResourceColor())
                        tabBinding.tvName.setTextColor(color)
                        tabBinding.tvEnName.setTextColor(color)
                    }
                }
                commonPagerTitleView.setOnClickListener { mBinding.viewPager.setCurrentItem(index, false) }
                return commonPagerTitleView
            }

            override fun getIndicator(context: Context): IPagerIndicator? {
                return null
            }
        }
        mBinding.magicIndicator.setNavigator(commonNavigator)
        ViewPager2Helper.bind(mBinding.magicIndicator, mBinding.viewPager)
    }

    override fun initData() {
        mViewModel.questionTemplateLiveData.observe(this){
            initViewPager(it)
            initMagicIndicator(it)
        }
        liveEventBusObserve(LivedataKeyMe.USER_UPDATE_QUESTION_ANSWER){_:Boolean->
            finish()
        }
        mViewModel.queryQuestionTemplate()
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = mBinding.stateLayout
}
