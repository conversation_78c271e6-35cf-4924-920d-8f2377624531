package com.kanzhun.marry.me.identify.activity

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.constract.BundleConstants.BUNDLE_AVATAR_ADVICE_ID
import com.kanzhun.common.constract.BundleConstants.BUNDLE_AVATAR_PHOTO_ID
import com.kanzhun.common.constract.BundleConstants.BUNDLE_AVATAR_PHOTO_URI
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.model.CerBean
import com.kanzhun.marry.me.api.model.PhotoCertStatus
import com.kanzhun.marry.me.databinding.MeActivityAvatarAuthBinding
import com.kanzhun.marry.me.identify.fragment.avatar.AvatarAuthEditFragment2
import com.kanzhun.marry.me.identify.fragment.avatar.AvatarAuthExamineFragment
import com.kanzhun.marry.me.identify.fragment.avatar.AvatarAuthSummitSuccessFragment
import com.kanzhun.marry.me.identify.viewmodel.AvatarAuthViewModel
import com.kanzhun.marry.me.util.IBackPressFragmentDelegate
import com.sankuai.waimai.router.annotation.RouterUri

/**
 * 头像认证页面
 */
@RouterUri(path = [MePageRouter.ME_AVATAR_AUTH_ACTIVITY])
class AvatarAuthActivity : BaseBindingActivity<MeActivityAvatarAuthBinding, AvatarAuthViewModel>() {
    var fragment: Fragment? = null
    override fun preInit(intent: Intent) {
        @Suppress("DEPRECATION") val data = intent.getSerializableExtra(BundleConstants.BUNDLE_DATA)
        if (data is CerBean) {
            mViewModel.cerBean = data
            mViewModel.avatarAuthStatusLiveData.value = data.status
        } else {
            mViewModel.avatarAuthStatusLiveData.value = PhotoCertStatus.TYPE_APPROVE_PHOTO_NO
        }
        mViewModel.protocolFrom = intent.getStringExtra(BundleConstants.BUNDLE_DATA_2) ?: ""
        mViewModel.mSource = intent.getPageSource()
    }

    override fun initView() {
    }

    override fun initData() {
        mViewModel.avatarAuthStatusLiveData.observe(this) {
            //重新加载fragment
            loadFragment(it)
        }
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null

    private fun loadFragment(avatarAuthStatus: Int) {
        //移除原来的fragment
        fragment?.run {
            supportFragmentManager.beginTransaction().remove(this).commitNow()
        }

        //加载新的fragment
        fragment = getCurrentNeedFragment(avatarAuthStatus).also {
            supportFragmentManager.beginTransaction().add(R.id.fragmentContainerView, it)
                .commitNow()
        }
    }

    private fun getCurrentNeedFragment(avatarAuthStatus: Int): Fragment {
        return when (avatarAuthStatus) {
            PhotoCertStatus.TYPE_APPROVE_PHOTO_SUBMIT -> {
                reportPoint("certify-submit-page-expo") {
                    actionp2 = "头像"
                }
                AvatarAuthSummitSuccessFragment()
            }

            PhotoCertStatus.TYPE_APPROVE_PHOTO_ING_ROBOT, PhotoCertStatus.TYPE_APPROVE_PHOTO_ING_PEOPLE -> {
                reportPoint("certify-inreview-page-expo") {
                    actionp2 = "头像"
                }
                //审核中
                AvatarAuthExamineFragment()
            }

            else -> {
//                // 未认证、认证失败
//                val response = AvatarAdviceGray.getAdviceGray()
//                if (response?.uploadGray == 1) { // 灰度流程
//                    @Suppress("DEPRECATION")
//                    AvatarAuthEditFragment2(
//                        adviceId = intent.getStringExtra(BUNDLE_AVATAR_ADVICE_ID),
//                        photoId = intent.getStringExtra(BUNDLE_AVATAR_PHOTO_ID),
//                        imageFileUri = intent.getParcelableExtra(BUNDLE_AVATAR_PHOTO_URI)
//                    )
//                } else { // 原流程
//                    @Suppress("DEPRECATION")
//                    AvatarAuthEditFragment(
//                        adviceId = intent.getStringExtra(BUNDLE_AVATAR_ADVICE_ID),
//                        photoId = intent.getStringExtra(BUNDLE_AVATAR_PHOTO_ID),
//                        imageFileUri = intent.getParcelableExtra(BUNDLE_AVATAR_PHOTO_URI)
//                    )
//                }

                @Suppress("DEPRECATION")
                AvatarAuthEditFragment2(
                    adviceId = intent.getStringExtra(BUNDLE_AVATAR_ADVICE_ID),
                    photoId = intent.getStringExtra(BUNDLE_AVATAR_PHOTO_ID),
                    imageFileUri = intent.getParcelableExtra(BUNDLE_AVATAR_PHOTO_URI)
                )
            }
        }
    }

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onBackPressed() {
        val currentFragment = fragment
        if (currentFragment is IBackPressFragmentDelegate) {
            currentFragment.onBackPress(this)
        } else {
            @Suppress("DEPRECATION")
            super.onBackPressed()
        }
    }

    @Suppress("unused")
    companion object {
        const val PROTOCOL_F4_NEW_TASK = 1
        const val PROTOCOL_F4_FORMAL_GUIDE = 2

        fun intent(
            context: Context,
            cerBean: CerBean?,
            pageSource: PageSource,
            blockWhenAuthSuccess: Boolean = true,
            protocolFrom: String = ""
        ) {
            if (blockWhenAuthSuccess && cerBean?.status == PhotoCertStatus.TYPE_APPROVE_PHOTO_SUCCESS) {
                return
            }
            val bundle = getPageSourceBundle(pageSource)
            Intent(context, AvatarAuthActivity::class.java).apply {
                putExtra(BundleConstants.BUNDLE_DATA, cerBean)
                putExtra(BundleConstants.BUNDLE_DATA_2, protocolFrom)
                this.putExtras(bundle)
            }.also {
                context.startActivity(it)
            }
        }
    }
}