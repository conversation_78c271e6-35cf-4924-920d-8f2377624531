package com.kanzhun.marry.me.identify.view

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import com.kanzhun.common.kotlin.ext.dp
import kotlin.math.cos
import kotlin.math.sin

class SpiderDiagramView(context: Context?, attrs: AttributeSet?) : View(context, attrs) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    private val path = Path()
    private val rect = Rect()

    var txtAndGoal = arrayListOf<Pair<String, Float>>()
    var data2Point = arrayListOf<Pair<Float,Float>>()

    private val nPoint: Int
        get() {
            return txtAndGoal.size
        }

    private val mWeb: Int
        get() {
           return 5
        }

    private var currentFraction = 0f

    private val startAnimator by lazy {
        val animator = ObjectAnimator.ofFloat(0f, 1f)
        animator.duration = 1000

        animator.addUpdateListener {
            currentFraction = it.animatedFraction


            invalidate()
        }

        animator
    }

    init {
        startAnimator.start()
    }


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        val width = resolveSize(measuredWidth,widthMeasureSpec)
        val height = resolveSize(width,heightMeasureSpec)

        setMeasuredDimension(width,height)

        val eachAngle = 360.00 / nPoint
        val webSpace = INTERVAL
        val x0 = measuredWidth / 2
        val y0 = measuredHeight / 2
        data2Point.clear()
        for (i in 0 until mWeb){
            for (j in 0 until nPoint){
                val angle = eachAngle * j - 90
                val r = webSpace * (i + 1)

                val x = x0 + r * cos(Math.toRadians( angle))
                val y = y0 + r * sin(Math.toRadians( angle))

                val pair = Pair(x.toFloat(),y.toFloat());
                data2Point.add(i * nPoint + j,pair)
            }
        }

    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        drawSpiderWeb(canvas)

        drawSpider(canvas)

        drawTxt(canvas)
    }

    private fun drawTxt(canvas: Canvas?) {
        path.reset()
        paint.reset()
        paint.apply {
            color = Color.RED
            strokeWidth = 2.dp
            textSize = 14.dp
            style = Paint.Style.FILL
        }
        for (i in 0 until nPoint){
            val txt = txtAndGoal[i].first
            paint.getTextBounds(txt , 0 , txt.length,rect)

            val txtWidth = rect.width()

            val angle = (360.00 / nPoint) * i - 90
            val r = INTERVAL * (mWeb + 1)

            var txtX = measuredWidth / 2 + r * cos(Math.toRadians( angle)) - txtWidth / 2f
            val txtY = let {
                if(i == 0){
                    measuredHeight / 2 + r * sin(Math.toRadians( angle)) +INTERVAL - 2.dp
                }else{
                    measuredHeight / 2 + r * sin(Math.toRadians( angle))
                }
            }

            canvas?.drawText(txt,txtX.toFloat(),txtY.toFloat(),paint)
        }
    }

    private fun drawSpider(canvas: Canvas?) {
        val eachAngle = 360.00 / nPoint
        val webSpace = INTERVAL
        val x0 = measuredWidth / 2
        val y0 = measuredHeight / 2
        path.reset()
        txtAndGoal.forEachIndexed { index, pair ->

            val angle = eachAngle * index - 90
            val r = webSpace * mWeb * pair.second * currentFraction

            val x = x0 + r * cos(Math.toRadians( angle))
            val y = y0 + r * sin(Math.toRadians( angle))

            if (index == 0) {
                path.moveTo(x.toFloat(), y.toFloat())
            } else {
                path.lineTo(x.toFloat(), y.toFloat())
            }
        }
        path.close()

        paint.style = Paint.Style.STROKE
        paint.color = Color.BLUE
        canvas?.drawPath(path, paint) // 绘制边

        paint.style = Paint.Style.FILL
        paint.alpha = (255 * 0.2).toInt()
        canvas?.drawPath(path, paint) // 绘制内边
        path.reset()
    }



    //  1、 初始化 需要多少个网的条 和条之间间距
    //  2、 确定最内侧n(几边型)个点x y 坐标 并连接
    //  3、 循环2 绘制所有m个网
    private fun drawSpiderWeb(canvas: Canvas?) {
        path.reset()
        paint.reset()
        paint.apply{
            color = Color.RED
            style = Paint.Style.STROKE
            strokeWidth = 2.dp
        }
        canvas?.drawPoint(measuredWidth / 2.0f , measuredHeight / 2.0f , paint)
        data2Point.forEachIndexed { index, pair ->
            if(index % nPoint == 0){
                path.moveTo(pair.first,pair.second)
            }else{
                path.lineTo(pair.first,pair.second)
                if(index % nPoint == nPoint - 1){
                    path.close()
                }
            }
            canvas?.drawLine(measuredWidth / 2.0f , measuredHeight / 2.0f,pair.first,pair.second,paint)
        }
        canvas?.drawPath(path,paint)
    }

    private fun drawSmallWeb(canvas: Canvas?) {


    }

    companion object {
        val WEB_COLOR = Color.parseColor("#88888888");

        val TXT_COLOR = Color.parseColor("#333333")

        // 最小多边形 (中间最小多边形大小)
        val MIN_RADIUS = 20.dp

        // 多边形间隔
        val INTERVAL = 20.dp
    }
}