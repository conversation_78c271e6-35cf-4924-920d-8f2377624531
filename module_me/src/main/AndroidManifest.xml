<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.VIBRATE" /><!-- 振动权限 -->
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application>
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeLikeMeInfoPreviewActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".identify.activity.RevenueAuthActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".personality.activity.MyQRCodeActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.UnfitReasonActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".identify.activity.ResetEmailActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".info.activity.CompanyNameHelpActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".info.activity.JobNameHelpActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.OtherProtocolActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.MeNotifySettingActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.MeAboutActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.SettingHelpActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.FaceVerifyPolicyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.MeCancellationActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeBasicInfoOverEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeIndustryEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeUpdateIntroActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeUpdateNickNameActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeOccupationEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeMarryLoveStatusActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeBaseInfoEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MyInfoEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.kanzhun.marry.me.info.activity.PersonEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.kanzhun.marry.me.mood.activity.MoodGridActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.kanzhun.marry.me.mood.activity.MyMoodDetailActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.kanzhun.marry.me.mood.activity.OtherMoodDetailActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.kanzhun.marry.me.mood.activity.MoodEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".setting.activity.TestProtocolActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeLivingPlaceEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeHometownEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeHouseCarEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeResidenceEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeRevenueEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeStatureEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeWeightEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.ABImpressionActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:hardwareAccelerated="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.personality.activity.PersonalityTestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.CertificationActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.IdentifyResultActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.EducationIdentifyChooseActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.MainLandEducationIdentifyChooseActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.EducationIdentifyActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.XueXinIdentifyActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.XueXinWebViewActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.RetentionServiceNumIdentifyActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.GraduationIdentifyActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.GraduationPicIdentifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.MeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.AccountAndSecurityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.PrivacySettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.FaceVerifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.EducationSchoolInputActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeAvatarUploadActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeAvatarUploadCropActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.StoryUploadActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeQuestionAndAnswerActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.MeQuestionSelectActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.fragment.ABImpressionContentEditActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.StoryTextEditActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.AuthCenterActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name="com.kanzhun.marry.me.setting.activity.UserReportActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".identify.activity.MarryCertActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.CompanyAuthActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.HouseAuthActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.CarAuthActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.AvatarAuthActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.AvatarBeautifyActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.CertingActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.identify.activity.AvatarCropActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.me.info.activity.preview.MeUserInfoPreviewActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.kanzhun.marry.me.identify.activity.AvatarAuthPreviewActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".setting.activity.ModifyPhoneActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".setting.activity.DeviceManagerActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".info.activity.MeTextAnswerActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />

        <activity
            android:name=".info.activity.MeVoiceAnswerActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".setting.activity.VerifyCodeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity android:name=".entrance.fragment.TestComposeActivity" />

        <activity android:name=".info.fragment.ActivityPhotoActivity" />

        <activity android:name="com.kanzhun.foundation.activity.ReCertActivity"
            android:screenOrientation="portrait"/>
    </application>

</manifest>