<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
   style="@style/style_edit_me_info_card"
    tools:visibility="visible">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <TextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            android:textStyle="bold"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="我的基本信息"
            style="@style/text_c7f_s15"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/idWaitToWrite"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_width="wrap_content"
            android:textStyle="bold"
            android:layout_height="wrap_content"
            android:text="4项待完善"
            android:drawableEnd="@drawable/icon_persion_edit_red_point"
            android:drawablePadding="4dp"
            android:textColor="@color/common_color_292929"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toLeftOf="@+id/idReject"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <ImageView
            android:id="@+id/idReject"
            android:layout_width="16dp"
            android:visibility="gone"
            android:layout_height="16dp"
            android:src="@drawable/me_ic_fail_small"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <ImageView
            android:id="@+id/idIconRight"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/me_my_info_ic_right_new"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />


        <com.kanzhun.foundation.utils.NoTouchRecyclerview
            android:id="@+id/idRecyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            app:spanCount="2"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle">


        </com.kanzhun.foundation.utils.NoTouchRecyclerview>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>