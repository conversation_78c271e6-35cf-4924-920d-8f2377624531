<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.activity.MeStatureEditActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeStatureEditViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeStatureEditCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:right='@{viewModel.skipObservable?@string/me_skip:""}'/>

        <ImageView
            android:id="@+id/iv_stature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/app_layout_page_title_margin"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:src="@drawable/common_ic_stature_title"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintTop_toTopOf="@id/iv_stature"
            app:layout_constraintBottom_toBottomOf="@id/iv_stature"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="18dp"
            app:index="@{viewModel.indexObservable - 1}"
            app:count="@{viewModel.countObservable}"
            app:visibleGone="@{viewModel.indexObservable > 0}"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_stature_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginTop="@dimen/app_layout_page_sub_title_margin"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            android:text="@string/me_stature_question_title"
            app:layout_constraintTop_toBottomOf="@+id/iv_stature"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <com.kanzhun.common.views.wheel.WheelView
            android:id="@+id/wv_stature"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/app_layout_page_wheel_margin"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            app:common_wheel_view_loop="false"
            app:common_wheel_view_textSize="@dimen/common_text_sp_24"
            app:common_wheel_view_visible_item="7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_stature_title" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_vertical_50"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toRightOf="@+id/guideline_vertical_50"
            app:layout_constraintTop_toTopOf="@+id/wv_stature"
            app:layout_constraintBottom_toBottomOf="@+id/wv_stature"
            app:layout_constraintVertical_bias="0.51"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="2dp"
            android:text="@string/me_stature_cm"
            android:textSize="@dimen/common_text_sp_18"
            android:textColor="@color/common_black"/>

        <com.kanzhun.common.views.RoundAlphaButton
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text='@{viewModel.indexObservable>0&amp;&amp;viewModel.indexObservable&lt;viewModel.countObservable ? @string/me_next : @string/me_save}'
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>