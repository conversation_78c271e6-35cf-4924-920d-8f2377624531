<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/common_white">

        <FrameLayout
            android:id="@+id/fl_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/ic_title_bar"
                layout="@layout/common_layout_only_left_title_bar"
                app:callback="@{callback}" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="32dp"
            android:fontFamily="sans-serif-medium"
            android:text="反馈"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_title" />

        <TextView
            android:id="@+id/tv_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="20dp"
            android:text="请输公司全称及邮箱后缀，审核通过后，可使用新的邮箱后缀进行认证"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />


        <TextView
            android:id="@+id/idEmailTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="公司全称"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/tv_assist"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist" />

        <EditText
            android:id="@+id/idEditText"
            android:layout_width="0dp"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="20dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@null"
            android:hint="请输入公司全称"
            android:singleLine="true"
            android:textColorHint="@color/common_color_CCCCCC"
            android:textSize="16dp"
            app:layout_constraintLeft_toLeftOf="@+id/idEmailTitle"
            app:layout_constraintTop_toBottomOf="@+id/idEmailTitle" />

        <View
            android:id="@+id/idLine"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="@color/common_color_CCCCCC"
            app:layout_constraintLeft_toLeftOf="@+id/idEditText"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="20dp"
            app:layout_constraintTop_toBottomOf="@+id/idEditText" />

        <TextView
            android:id="@+id/idError"
            android:textSize="13dp"
            app:layout_constraintTop_toBottomOf="@+id/idLine"
            app:layout_constraintLeft_toLeftOf="@+id/idLine"
            tools:text="123"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_marginTop="4dp"
            android:textColor="@color/common_color_E03641"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/idEmailTitleSub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="邮箱后缀"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/idEmailTitle"
            app:layout_constraintTop_toBottomOf="@+id/idError" />


        <TextView
            android:id="@+id/idEditTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="\@"
            android:textColor="@color/common_color_292929"
            android:textSize="16dp"
            app:layout_constraintLeft_toLeftOf="@+id/idEmailTitle"
            app:layout_constraintTop_toBottomOf="@+id/idEmailTitleSub" />

        <EditText
            android:id="@+id/idEditTextSub"
            android:layout_width="0dp"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="20dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@null"
            android:inputType="textEmailAddress"
            android:hint="请输入邮箱后缀"
            android:singleLine="true"
            android:textColorHint="@color/common_color_CCCCCC"
            android:textSize="16dp"
            android:layout_marginLeft="10dp"
            app:layout_constraintLeft_toRightOf="@+id/idEditTitle"
            app:layout_constraintTop_toBottomOf="@+id/idEmailTitleSub" />

        <View
            android:id="@+id/idLine2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="@color/common_color_CCCCCC"
            app:layout_constraintLeft_toLeftOf="@+id/idEditText"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="20dp"
            app:layout_constraintTop_toBottomOf="@+id/idEditTextSub" />

        <TextView
            android:id="@+id/idError2"
            android:textSize="13dp"
            app:layout_constraintTop_toBottomOf="@+id/idLine2"
            app:layout_constraintLeft_toLeftOf="@+id/idLine2"
            tools:text="123"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_marginTop="4dp"
            android:textColor="@color/common_color_E03641"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_submit"
            style="@style/button_large_next_page_style"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:enabled="false"
            android:onClick="@{()->callback.clickSubmit()}"
            android:text="提交"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.CompanyAuthSocialCallback" />


        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CompanyAuthViewModel" />
    </data>

</layout>