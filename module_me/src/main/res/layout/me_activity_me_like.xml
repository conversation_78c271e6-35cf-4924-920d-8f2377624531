<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_color_F0F0F0">

    <com.kanzhun.foundation.views.CommonPageTitleView
        android:id="@+id/idTopLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title_bottom_padding="24dp"
        app:title_icon="@drawable/common_ic_icon_title_avatar"
        app:title_text="我的兴趣爱好" />

    <TextView
        android:id="@+id/idWarn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_layout_page_left_padding"
        android:layout_marginEnd="@dimen/app_layout_page_right_padding"
        android:drawableLeft="@drawable/me_ic_fail_small"
        android:drawablePadding="6dp"
        android:textColor="@color/common_color_FF3F4B"
        android:textSize="@dimen/common_text_sp_12"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/idTopLayout"
        tools:text="个人简介内容敏感，请修改个人简介内容敏感，请修改个人简介内容敏感，请修改个人简介内容敏感，"
        tools:visibility="visible" />


    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:layout_marginLeft="@dimen/app_layout_page_left_padding"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        android:background="@color/common_white"
        android:padding="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/idWarn"
        app:qmui_radius="12dp">

        <TextView
            android:id="@+id/tv_content_total_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="/200"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/idCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_content_total_count" />

        <EditText
            android:id="@+id/edit_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="12dp"
            android:background="@null"
            android:gravity="start"
            android:hint="介绍一下自己的热爱的事吧"
            android:textColor="@color/common_color_191919"
            android:textColorHint="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintBottom_toTopOf="@+id/tv_content_total_count"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
             />
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


    <fragment
        android:id="@+id/idFragmentLoginKeySelect"
        android:name="com.kanzhun.marry.login.fragment.LoginLabelViewPagerSelectFragment"
        android:layout_width="match_parent"
        android:layout_height="310dp"
        android:layout_marginBottom="16dp"
        android:tag="idFragmentLoginKeySelect"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout="@layout/me_fragment_like_key_select" />

</androidx.constraintlayout.widget.ConstraintLayout>
