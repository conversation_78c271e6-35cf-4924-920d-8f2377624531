<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_white"
    android:orientation="vertical">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_title_height"
         />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="never"
        android:scrollbars="none"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="true"
                android:text="@string/me_upload_car_auth_information"
                android:textColor="@color/common_color_1D1D1F"
                android:textSize="28sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/me_upload_car_auth_information_desc"
                android:textColor="@color/common_color_7F7F7F"
                android:textSize="@dimen/common_text_sp_14" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/me_upload_car_auth_owner"
                android:textColor="@color/common_color_858585"
                android:textSize="@dimen/common_text_sp_14" />

            <EditText
                android:id="@+id/etInputOwner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/common_white"
                android:hint="@string/me_upload_car_auth_owner_hint"
                android:maxLength="25"
                android:singleLine="true"
                android:textColor="@color/common_color_292929"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                tools:text="" />

            <View
                android:id="@+id/idLine1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="20dp"
                android:background="@color/common_color_F0F0F0" />

            <TextView
                android:id="@+id/idError1"
                tools:visibility="visible"
                android:visibility="gone"
                android:layout_marginTop="12dp"
                tools:text="1232"
                android:textSize="12dp"
                android:textColor="@color/common_color_FF0000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/me_upload_car_auth_car_number"
                android:textColor="@color/common_color_858585"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/etInputCertificateNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/common_white"
                android:hint="@string/me_upload_car_auth_driving_permit_hint"
                android:maxLength="10"
                android:singleLine="true"
                android:textColor="@color/common_color_292929"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16" />

            <View
                android:id="@+id/idLine2"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="20dp"
                android:background="@color/common_color_F0F0F0" />


            <TextView
                android:id="@+id/idError2"
                tools:visibility="visible"
                android:visibility="gone"
                android:layout_marginTop="12dp"
                tools:text="1232"
                android:textSize="12dp"
                android:textColor="@color/common_color_FF0000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="28dp">

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/ivCarAuthPositive"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/me_car_auth_example1"
                    app:common_radius="8dp"
                    app:layout_constraintDimensionRatio="h,16:9"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/vCarAuthPositive"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/me_upload_image_cover"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthPositive"
                    app:layout_constraintEnd_toEndOf="@id/ivCarAuthPositive"
                    app:layout_constraintStart_toStartOf="@id/ivCarAuthPositive"
                    app:layout_constraintTop_toTopOf="@id/ivCarAuthPositive"
                    tools:visibility="visible" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btUpload1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginVertical="10dp"
                    android:drawableStart="@drawable/me_ic_icon_graduation_upload"
                    android:drawablePadding="6dp"
                    android:paddingLeft="20dp"
                    android:paddingTop="8dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/me_click_and_upload"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthPositive"
                    app:layout_constraintEnd_toEndOf="@id/ivCarAuthPositive"
                    app:layout_constraintStart_toStartOf="@id/ivCarAuthPositive"
                    app:layout_constraintTop_toTopOf="@id/ivCarAuthPositive"
                    app:qmui_backgroundColor="@color/common_color_141414"
                    app:qmui_radius="25dp" />


                <TextView
                    android:id="@+id/tvReUploadPositive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/me_ic_restart_upload_picture"
                    android:drawablePadding="6dp"
                    android:gravity="center_vertical"
                    android:text="@string/common_re_upload"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthPositive"
                    app:layout_constraintEnd_toEndOf="@id/ivCarAuthPositive"
                    app:layout_constraintStart_toStartOf="@id/ivCarAuthPositive"
                    tools:visibility="visible" />

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/ivCarAuthNegative"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="12dp"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/me_car_auth_example2"
                    app:common_radius="8dp"
                    app:layout_constraintDimensionRatio="h,16:9"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ivCarAuthPositive" />

                <View
                    android:id="@+id/vCarAuthNegative"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/me_upload_image_cover"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthNegative"
                    app:layout_constraintEnd_toEndOf="@id/ivCarAuthNegative"
                    app:layout_constraintStart_toStartOf="@id/ivCarAuthNegative"
                    app:layout_constraintTop_toTopOf="@id/ivCarAuthNegative"
                    tools:visibility="visible" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/btUpload2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginVertical="10dp"
                    android:drawableStart="@drawable/me_ic_icon_graduation_upload"
                    android:drawablePadding="6dp"
                    android:paddingLeft="20dp"
                    android:paddingTop="8dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/me_click_and_upload"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthNegative"
                    app:layout_constraintLeft_toLeftOf="@id/ivCarAuthNegative"
                    app:layout_constraintRight_toRightOf="@id/ivCarAuthNegative"
                    app:layout_constraintTop_toTopOf="@id/ivCarAuthNegative"
                    app:qmui_backgroundColor="@color/common_color_141414"
                    app:qmui_radius="25dp" />

                <TextView
                    android:id="@+id/tvReUploadNegative"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/me_ic_restart_upload_picture"
                    android:drawablePadding="6dp"
                    android:gravity="center_vertical"
                    android:text="@string/common_re_upload"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/ivCarAuthNegative"
                    app:layout_constraintEnd_toEndOf="@id/ivCarAuthNegative"
                    app:layout_constraintStart_toStartOf="@id/ivCarAuthNegative" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/tvBottomNote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:lineSpacingMultiplier="1.1"
                android:text="@string/me_car_auth_note"
                android:textColor="@color/common_color_858585"
                android:textSize="@dimen/common_text_sp_12" />
        </LinearLayout>
    </ScrollView>

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/btn_next"
        style="@style/common_black_button_style"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:enabled="false"
        android:text="@string/common_submit"
         />

</LinearLayout>