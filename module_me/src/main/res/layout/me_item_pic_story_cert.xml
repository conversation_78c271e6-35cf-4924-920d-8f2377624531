<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="canShowStoryDelete"
            type="androidx.databinding.ObservableBoolean" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.bean.PicStoryItem" />
    </data>

    <com.kanzhun.common.views.UniformHWConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="6dp">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ov_story"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:common_cache_source="true"
            app:common_radius="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            layout="@layout/me_item_story_cert_down"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:bean="@{bean}" />

        <include
            layout="@layout/me_item_story_cert_option"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:bean="@{bean}"
            app:canShowStoryDelete="@{canShowStoryDelete}" />

        <include
            layout="@layout/me_item_story_cert_upload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:bean="@{bean}" />
    </com.kanzhun.common.views.UniformHWConstraintLayout>
</layout>