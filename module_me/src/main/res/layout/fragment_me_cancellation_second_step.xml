<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".setting.MeCancellationSecondStepFragment">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.MeCancellationSecondStepViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.MeCancellationSecondStepCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fragmentMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_F5F5F5"
        android:orientation="vertical">

        <include
            android:id="@+id/ll_title"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title="@{@string/me_cancellation_account}" />

        <TextView
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="16dp"
            android:id="@+id/tv_top"
            android:text="@string/me_cancellation_account_top_tips"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_7F7F7F"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintTop_toBottomOf="@+id/ll_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_cancellation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="16dp"
            android:onClick="@{()->callback.cancellation()}"
            android:enabled="@{viewModel.isEnable}"
            android:background="@{viewModel.isEnable ? @drawable/common_bg_corner_25_color_191919 : @drawable/common_bg_corner_25_color_cccccc}"
            android:gravity="center"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/me_cancellation_account"
            android:textColor="@color/common_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ScrollView
            android:layout_marginBottom="12dp"
            android:layout_marginTop="16dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_top"
            app:layout_constraintBottom_toTopOf="@+id/tv_cancellation"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    app:layout_constraintEnd_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:id="@+id/et_reason"
                        visibleGone="@{viewModel.isOther}"
                        android:layout_width="match_parent"
                        android:layout_height="146dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="20dp"
                        android:background="@drawable/common_bg_corner_12_color_ffffff"
                        android:gravity="start"
                        android:hint="@string/me_cancellation_reason_hint"
                        android:maxLength="500"
                        android:padding="14dp"
                        android:text="@={viewModel.otherReason}"
                        android:textColor="@color/common_color_191919"
                        android:textColorHint="@color/common_color_B2B2B2"
                        android:textSize="@dimen/common_text_sp_16"></EditText>

                    <TextView
                        android:id="@+id/tv_reason_length"
                        visibleGone="@{viewModel.isOther}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="39dp"
                        android:text="0/500"
                        android:textColor="@color/common_color_B2B2B2"
                        android:textSize="@dimen/common_text_sp_12"
                        app:layout_constraintBottom_toBottomOf="@+id/et_reason"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:text="adnjaadnjaksdn" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>