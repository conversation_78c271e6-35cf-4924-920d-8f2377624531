<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white"
        tools:ignore="SpUsage,ContentDescription,HardcodedText,UseCompatTextViewDrawableXml">

        <FrameLayout
            android:id="@+id/fl_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/ic_title_bar"
                layout="@layout/common_layout_only_left_title_bar"
                app:callback="@{callback}" />

        </FrameLayout>

    <ScrollView
        android:id="@+id/idScrollView"
        app:layout_constraintTop_toBottomOf="@+id/fl_title"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_marginBottom="20dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_submit"
        android:layout_height="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:paddingBottom="20dp"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="32dp"
            android:fontFamily="sans-serif-medium"
            android:text="@string/me_company_auth_choose_wechat_ding_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="20dp"
            android:text="@string/me_company_auth_choose_wechat_ding_sub_desc"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <com.qmuiteam.qmui.layout.QMUILinearLayout
            android:id="@+id/idCardInfo"
            android:layout_width="match_parent"
            android:layout_marginTop="12dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist"
            android:background="@color/common_color_F5F5F5"
            android:layout_marginHorizontal="20dp"
            android:padding="16dp"
            app:qmui_radius="20dp"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/idCardInfoTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="认证公司：看准科技"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold"
                />

            <TextView
                android:layout_marginTop="8dp"
                android:id="@+id/idCardInfoContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请保持认证材料与公司名称一致"
                android:textColor="@color/common_color_858585"
                android:textSize="@dimen/common_text_sp_14"
                />


        </com.qmuiteam.qmui.layout.QMUILinearLayout>


        <TextView
            android:id="@+id/tv_assist_tip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:text="@string/me_company_auth_choose_wechat_ding_sub_desc_tip"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idCardInfo" />

        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:drawableStart="@mipmap/me_icon_graduation_tip"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="@string/me_company_auth_choose_wechat_ding_company_name"
            android:textColor="@color/common_color_292929"
            android:textSize="14dp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="@+id/tv_assist_tip"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist_tip" />

        <TextView
            android:id="@+id/tv_staff_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:drawableStart="@mipmap/me_icon_graduation_tip"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="@string/me_company_auth_choose_wechat_ding_staff_name"
            android:textColor="@color/common_color_292929"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_company_name"
            app:layout_constraintLeft_toRightOf="@+id/tv_company_name"
            app:layout_constraintTop_toTopOf="@+id/tv_company_name" />

        <TextView
            android:id="@+id/tv_clear_complete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:drawableStart="@mipmap/me_icon_graduation_tip"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="@string/me_company_auth_choose_wechat_ding_qrcode"
            android:textColor="@color/common_color_292929"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_staff_name"
            app:layout_constraintLeft_toRightOf="@+id/tv_staff_name"
            app:layout_constraintTop_toTopOf="@+id/tv_staff_name" />

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/cl_error"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="12dp"
            android:background="@color/common_color_FFD209"
            app:qmui_radius="12dp"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:layout_marginTop="12dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_clear_complete"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/idIcon"
                android:src="@mipmap/me_icon_error_red"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="24dp"
                android:layout_height="24dp"/>

            <TextView
                android:layout_marginLeft="8dp"
                android:id="@+id/tv_error_content"
                android:layout_width="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textColor="@color/common_color_292929"
                android:textSize="13dp"
                app:layout_constraintLeft_toRightOf="@+id/idIcon"
                android:layout_marginRight="16dp"
                android:textStyle="bold"
                app:layout_constraintRight_toRightOf="parent"
                tools:text="1哈哈哈哈哈哈哈哈哈" />

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/fl_upload"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="20dp"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_error"
            app:layout_goneMarginTop="24dp"
            app:qmui_radius="12dp">

            <ImageView
                android:id="@+id/iv_social_upload"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@mipmap/me_icon_wechat_ding_upload"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/iv_social"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="@+id/iv_social_upload"
                app:layout_constraintLeft_toLeftOf="@+id/iv_social_upload"
                app:layout_constraintRight_toRightOf="@+id/iv_social_upload"
                app:layout_constraintTop_toTopOf="@+id/iv_social_upload" />

            <View
                android:id="@+id/view_bg"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/common_color_E3F6FF_50"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/iv_social_upload"
                app:layout_constraintLeft_toLeftOf="@+id/iv_social_upload"
                app:layout_constraintRight_toRightOf="@+id/iv_social_upload"
                app:layout_constraintTop_toTopOf="@+id/iv_social_upload" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableStart="@drawable/me_ic_icon_graduation_upload"
                android:drawablePadding="6dp"
                android:onClick="@{()->callback.uploadSocialPic()}"
                android:paddingLeft="20dp"
                android:paddingTop="8dp"
                android:paddingRight="20dp"
                android:paddingBottom="8dp"
                android:text="@{TextUtils.isEmpty(viewModel.imageUrl) ? @string/me_company_auth_upload_dd : @string/me_re_upload}"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_backgroundColor="@color/common_color_292929"
                app:qmui_radius="25dp"
                tools:text="@string/me_company_auth_upload_wechat_ding" />

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_submit"
            style="@style/common_blue_button_style"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:enabled="@{!TextUtils.isEmpty(viewModel.imageUrl)}"
            android:onClick="@{()->callback.clickSubmit()}"
            android:text="@string/me_commit_identify"
            app:layout_constraintBottom_toBottomOf="parent"
            app:greyDisabledStyle="@{!TextUtils.isEmpty(viewModel.imageUrl)}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.CompanyAuthWechatDingCallback" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CompanyAuthWechatDingViewModel" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CompanyAuthViewModel" />
    </data>

</layout>