<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:visibility="visible"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

        <TextView
            android:id="@+id/tvLoveAttitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_love_attitude"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivLovePatternInclinationArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/me_preview_arrow"
            app:layout_constraintBottom_toBottomOf="@id/tvLoveAttitude"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvLoveAttitude" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/loveAttitudeBg"
            android:layout_width="0dp"
            android:layout_height="8dp"
            app:layout_constraintBottom_toBottomOf="@id/tvLoveAttitudeContent"
            app:layout_constraintEnd_toEndOf="@id/tvLoveAttitudeContent"
            app:layout_constraintStart_toStartOf="@id/tvLoveAttitudeContent"
            app:stv_shaderEnable="true"
            app:stv_shaderEndColor="@color/common_color_FFFFFF_30"
            app:stv_shaderMode="leftToRight"
            app:stv_shaderStartColor="#FF648F" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvLoveAttitudeContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="5dp"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvLoveAttitude"
            tools:text="复合型恋人" />

        <TextView
            android:id="@+id/tvLoveAttitudeDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:maxLines="2"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvLoveAttitudeContent"
            tools:text="@string/common_long_placeholder" />


</androidx.constraintlayout.widget.ConstraintLayout>