<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.marry.me.views.GradientBorderConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idScrollToThisView"
    style="@style/style_edit_me_info_card_noPadding"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:visibility="visible">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="29dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:src="@drawable/me_ic_ab"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idTitleLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:layout_marginTop="4dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            app:layout_constraintTop_toBottomOf="@+id/idIcon">

            <TextView
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:id="@+id/idTitle"
                style="@style/text_c7f_s15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的AB面"
                android:textStyle="bold"
                app:layout_goneMarginTop="0dp" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/idCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text="0/3"
                android:textColor="@color/common_color_858585"
                android:textSize="18dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/idTitle"
                app:layout_constraintLeft_toRightOf="@+id/idTitle"
                app:layout_constraintTop_toTopOf="@+id/idTitle" />

            <TextView
                android:id="@+id/idSecondTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="16dp"
                android:text="每个人都是多面的，反差人设也很有魅力"
                android:textColor="@color/common_color_D4D4D4"
                android:textSize="18dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/idTitle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idTitle" />

            <ImageView
                android:id="@+id/idIconRight"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/me_my_info_ic_right_new"
                app:layout_constraintBottom_toBottomOf="@+id/idTitle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idTitle" />

            <TextView
                android:id="@+id/idHide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="2dp"
                android:drawableEnd="@drawable/icon_persion_edit_red_point"
                android:drawablePadding="4dp"
                android:text="填写"
                android:textColor="@color/common_color_7F7F7F"
                android:textSize="14dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/idIconRight"
                app:layout_constraintRight_toLeftOf="@id/idIconRight"
                app:layout_constraintTop_toTopOf="@+id/idIconRight" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.kanzhun.marry.me.info.views.NestRecyclerView
            android:id="@+id/idRecyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitleLayout"
            tools:visibility="gone" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.kanzhun.marry.me.views.GradientBorderConstraintLayout>