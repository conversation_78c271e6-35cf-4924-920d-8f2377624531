<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    tools:background="@color/common_black_60"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDialogContent"
        android:layout_width="match_parent"
        android:layout_height="482dp"
        android:background="@mipmap/preview_love_attitude_dialog_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            android:text="@string/me_love_attitude_matching"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_26"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.lihang.ShadowLayout
            android:id="@+id/clPercent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:hl_cornerRadius="23dp"
            app:hl_endColor="#FFFCE8FF"
            app:hl_startColor="#FFD6E4FF"
            app:hl_strokeColor="@color/common_color_191919"
            app:hl_strokeWith="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="17dp"
                >

                <com.kanzhun.common.views.textview.BoldTextView
                    android:id="@+id/tvPercentValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_34"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="85" />

                <com.kanzhun.common.views.textview.BoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="%"
                    android:textColor="@color/common_color_191919"
                    android:textSize="16sp"
                    app:layout_constraintBaseline_toBaselineOf="@id/tvPercentValue"
                    app:layout_constraintStart_toEndOf="@id/tvPercentValue" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.lihang.ShadowLayout>


        <com.kanzhun.marry.me.views.raderview.RadarView
            android:id="@+id/raderView"
            android:layout_width="match_parent"
            android:layout_height="280dp"
            android:layout_marginTop="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clPercent" />

        <ImageView
            android:id="@+id/iconYou"
            android:layout_width="12dp"
            android:layout_height="14dp"
            android:layout_marginStart="120dp"
            android:layout_marginBottom="33dp"
            android:src="@mipmap/me_icon_radar_man"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tvYou"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/common_you"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iconYou"
            app:layout_constraintStart_toEndOf="@id/iconYou"
            app:layout_constraintTop_toTopOf="@id/iconYou" />


        <ImageView
            android:id="@+id/iconTA"
            android:layout_width="12dp"
            android:layout_height="14dp"
            android:layout_marginStart="32dp"
            android:src="@mipmap/me_icon_radar_woman"
            app:layout_constraintBottom_toBottomOf="@id/iconYou"
            app:layout_constraintStart_toEndOf="@id/iconYou"
            app:layout_constraintTop_toTopOf="@id/iconYou" />

        <TextView
            android:id="@+id/tvTa"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/common_ta"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iconYou"
            app:layout_constraintStart_toEndOf="@id/iconTA"
            app:layout_constraintTop_toTopOf="@id/iconYou" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:src="@drawable/common_white_circle_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clDialogContent" />

</androidx.constraintlayout.widget.ConstraintLayout>