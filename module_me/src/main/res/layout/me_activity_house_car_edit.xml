<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.MeHouseCarEditActivity">

    <data>
        <import type="android.view.View" />
        <import type="com.kanzhun.foundation.Constants" />
        <import type="com.kanzhun.marry.me.R" />
        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeHouseCarEditViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeHouseCarEditCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:right='@{viewModel.skipObservable?@string/me_skip:""}'/>

        <ImageView
            android:id="@+id/iv_house_car"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="32dp"
            android:src="@drawable/me_ic_house_car_page"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintTop_toTopOf="@id/iv_house_car"
            app:layout_constraintBottom_toBottomOf="@id/iv_house_car"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="18dp"
            app:index="@{viewModel.indexObservable - 1}"
            app:count="@{viewModel.countObservable}"
            app:visibleGone="@{viewModel.indexObservable > 0}"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_house_car_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="12dp"
            android:text="@string/me_house_car_question_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_house_car" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_house_car_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:layout_marginTop="4dp"
            android:text="@string/me_info_preview_private_title"
            android:textColor="@color/common_color_797979"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_house_car_title" />


        <TextView
            android:id="@+id/tv_has_house"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginTop="40dp"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            app:layout_constraintTop_toBottomOf="@+id/tv_house_car_sub_title"
            android:text="房产情况"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/fl_house"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_has_house"
            >

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/idBtnHasHouse"
                android:text="有房"
                android:textColor="@color/common_color_292929"
                android:layout_marginRight="5dp"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:minHeight="46dp"
                app:layout_constraintRight_toRightOf="@id/guideline"
                android:layout_gravity="center"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                android:gravity="center"
                android:layout_width="0dp"
                android:layout_height="46dp"/>


            <TextView
                android:id="@+id/idBtnNoHouse"
                android:text="无房"
                android:textColor="@color/common_color_292929"
                android:layout_marginLeft="5dp"
                android:minHeight="46dp"
                android:textStyle="bold"
                android:layout_gravity="center"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:gravity="center"
                app:layout_constraintLeft_toLeftOf="@+id/guideline"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_width="0dp"
                android:layout_height="46dp"/>

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <TextView
            android:id="@+id/tv_has_car"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginTop="40dp"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            app:layout_constraintTop_toBottomOf="@+id/fl_house"
            android:text="车产情况"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fl_car"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_has_car"
            >

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline2"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/idBtnHasCar"
                android:text="有车"
                android:textColor="@color/common_color_292929"
                android:layout_marginRight="5dp"
                android:minHeight="46dp"
                app:layout_constraintRight_toRightOf="@+id/guideline2"
                android:layout_gravity="center"
                android:textStyle="bold"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                app:layout_constraintLeft_toLeftOf="parent"
                android:gravity="center"
                android:layout_width="0dp"
                android:layout_height="46dp"/>

            <TextView
                android:id="@+id/idBtnNoCar"
                android:text="无车"
                android:textColor="@color/common_color_292929"
                android:minHeight="46dp"
                android:layout_gravity="center"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginLeft="5dp"
                app:layout_constraintLeft_toLeftOf="@+id/guideline2"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_width="0dp"
                android:layout_height="46dp"/>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.kanzhun.common.views.RoundAlphaButton
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text='@{viewModel.indexObservable>0&amp;&amp;viewModel.indexObservable&lt;viewModel.countObservable ? @string/me_next : @string/me_save}'
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>