<?xml version="1.0" encoding="utf-8"?>
<layout>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <TextView
        android:text="@string/company_custom_string"
        android:id="@+id/tv_content"
        android:layout_height="wrap_content"
        android:paddingVertical="14dp"
        android:layout_width="wrap_content"
        android:drawableEnd="@drawable/common_ic_gray_right_arrow"
        android:textColor="@color/common_color_0046BD"
         />
</FrameLayout>
</layout>
