<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.kanzhun.foundation.api.model.ProfileInfoModel.Story" />

        <variable
            name="bean"
            type="com.kanzhun.marry.me.info.bean.VideoStoryEditItem" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@color/common_translate"
        android:keepScreenOn="true">

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="left|bottom"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/me_selector_story_video_voice"
            app:viewSelected="@{bean.silence}"
            app:visibleGone="@{bean.type == Story.TYPE_VIDEO}" />

        <TextView
            android:id="@+id/tv_reselect"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_gravity="right|bottom"
            android:layout_marginBottom="13dp"
            android:layout_marginRight="13dp"
            android:background="@drawable/common_bg_corner_16_color_66000000"
            android:drawableLeft="@drawable/me_ic_story_reselect"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/me_reselect"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12" />
    </FrameLayout>
</layout>