<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coorchice.library.SuperTextView
        android:id="@+id/tvReportOrFeedback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="32dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="9dp"
        android:drawablePadding="7dp"
        android:text="@string/me_report_or_feedback"
        android:drawableStart="@drawable/me_icon_preview_report"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:stv_corner="20dp"
        app:stv_stroke_color="@color/common_color_CCCCCC"
        app:stv_stroke_width="1dp" />

</androidx.constraintlayout.widget.ConstraintLayout>