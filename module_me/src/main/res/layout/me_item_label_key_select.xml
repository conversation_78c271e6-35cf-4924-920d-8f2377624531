<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingRight="10dp">

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        android:id="@+id/idUnSelectLabel"
        app:qmui_borderColor="@color/common_color_FFE0E0E0"
        app:qmui_borderWidth="1dp"
        app:qmui_radius="19dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/idImageView"
            android:layout_gravity="center_vertical"
            tools:src="@drawable/common_tip_bar_warn"
            android:layout_marginRight="3dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idLabel"
            app:layout_constraintBottom_toBottomOf="@+id/idLabel"
            android:layout_width="20dp"
            android:layout_height="20dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="20dp"
            android:visibility="visible"
            android:gravity="center_vertical"
            android:textColor="@color/common_color_5E5E5E"
            android:textSize="14dp"
            tools:text="123213" />


    </com.qmuiteam.qmui.layout.QMUILinearLayout>


    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="wrap_content"
        android:id="@+id/idSelectLabel"
        android:visibility="gone"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        android:background="@color/common_color_E9F1FF"
        app:qmui_radius="19dp"
        android:paddingLeft="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/idImageView2"
            android:layout_gravity="center_vertical"
            tools:src="@drawable/common_tip_bar_warn"
            android:layout_marginRight="3dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idLabel2"
            app:layout_constraintBottom_toBottomOf="@+id/idLabel2"
            android:layout_width="20dp"
            android:layout_height="20dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idLabel2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_0046BD"
            android:textSize="14dp"
            app:layout_constraintLeft_toRightOf="@+id/idImageView2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="123213" />


        <ImageView
            android:layout_marginRight="10dp"
            android:id="@+id/idImgClose"
            android:paddingLeft="8dp"
            android:paddingRight="6dp"
            android:paddingTop="11dp"
            android:paddingBottom="11dp"
            android:layout_width="28dp"
            android:layout_height="36dp"
            android:src="@drawable/login_select_key_img_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/idLabel2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>