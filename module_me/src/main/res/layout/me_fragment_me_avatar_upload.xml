<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_title_avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:src="@drawable/common_ic_icon_avatar_page"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:visibleTurnInvisible="@{viewModel.avatarPassObservable}" />

        <ImageView
            android:id="@+id/iv_upload"
            android:layout_width="108dp"
            android:layout_height="108dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/common_bg_conor_12_color_f5f5f5"
            android:onClick="@{v->callback.clickSelectPhoto()}"
            android:scaleType="center"
            android:src="@mipmap/me_ic_story_upload_add"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_desc" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:text="@string/me_upload_avatar_desc"
            android:textColor="@color/common_color_797979"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:visibleTurnInvisible="@{viewModel.avatarPassObservable}" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_20"
            app:count="@{viewModel.countObservable}"
            app:index="@{viewModel.indexObservable - 1}"
            app:layout_constraintBottom_toBottomOf="@id/iv_title_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_title_avatar"
            app:visibleGone="@{viewModel.indexObservable > 0}"
            app:visibleTurnInvisible="@{viewModel.avatarPassObservable}" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="12dp"
            android:text="@string/me_upload_avatar_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_title_avatar"
            app:visibleTurnInvisible="@{viewModel.avatarPassObservable}" />

        <TextView
            android:id="@+id/tv_success_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="39dp"
            android:text="@string/me_upload_avatar_success_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:visibleInvisible="@{viewModel.avatarPassObservable}"
            tools:visibility="invisible" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/common_blue_button_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:enabled="@{viewModel.enableNextObservable}"
            android:onClick="@{v->callback.clickNext()}"
            android:text='@{viewModel.indexObservable>0&amp;&amp;viewModel.indexObservable&lt;viewModel.countObservable ? @string/me_next : @string/me_save}'
            app:greyDisabledStyle="@{viewModel.enableNextObservable}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <ScrollView
            android:id="@+id/scorll_avatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_next"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/tv_desc">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.qmuiteam.qmui.layout.QMUIFrameLayout
                        android:id="@+id/fl_avatar"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="40dp"
                        android:layout_marginTop="30dp"
                        android:layout_marginEnd="40dp"
                        android:onClick="@{v->callback.clickSelectPhoto()}"
                        app:layout_constraintDimensionRatio="h,3:4"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:qmui_radius="10dp">

                        <ImageView
                            android:id="@+id/iv_avatar_success"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="fitXY"
                            android:src="@mipmap/me_icon_avatar_success"
                            app:visibleInvisible="@{viewModel.avatarPassObservable}" />

                        <com.qmuiteam.qmui.layout.QMUIFrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="6dp"
                            app:qmui_radius="10dp">

                            <com.kanzhun.common.views.image.OImageView
                                android:id="@+id/iv_avatar"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="fitXY" />

                            <FrameLayout
                                android:id="@+id/fl_error"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="bottom"
                                android:background="@color/common_color_000000_40"
                                app:visibleTurnInvisible="@{viewModel.enableNextObservable}">

                                <TextView
                                    android:id="@+id/tv_error"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:drawableRight="@drawable/me_ic_fail_small"
                                    android:drawablePadding="6dp"
                                    android:gravity="center_vertical"
                                    android:padding="10dp"
                                    android:textColor="@color/common_white"
                                    android:textSize="@dimen/common_text_sp_14"
                                    tools:text="看不清楚你的长相，换一张吧" />
                            </FrameLayout>
                        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

                    </com.qmuiteam.qmui.layout.QMUIFrameLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </ScrollView>

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:right='@{viewModel.skipObservable?@string/me_skip:""}'/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeAvatarUploadCallback" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeAvatarUploadFragmentViewModel" />
    </data>
</layout>