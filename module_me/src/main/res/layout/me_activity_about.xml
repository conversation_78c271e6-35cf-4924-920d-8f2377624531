<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".setting.MeAboutActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.MeAboutViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.MeAboutCallback" />
    </data>

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:title="@{@string/me_about_title}" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <com.qmuiteam.qmui.layout.QMUIFrameLayout
                    android:layout_width="90dp"
                    android:layout_height="90dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="43dp"
                    >

                    <ImageView
                        android:layout_width="88dp"
                        android:layout_height="88dp"
                        android:src="@mipmap/login_entry_bg" />
                </com.qmuiteam.qmui.layout.QMUIFrameLayout>




                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="48dp"
                    android:onClick="@{v->callback.gotoScore(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_goto_score"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.versionUpdate(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:id="@+id/tv_update_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_version_update"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{@string/me_current_version(viewModel.versionName)}"
                        android:textColor="@color/common_color_7F7F7F"
                        android:textSize="@dimen/common_text_sp_14"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintLeft_toRightOf="@+id/tv_update_title"
                        app:layout_constraintRight_toLeftOf="@+id/iv_version_new"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/iv_version_new"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="8dp"
                        android:src="@mipmap/me_ic_version_update_new"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/iv_arrow_version"
                        app:layout_constraintTop_toTopOf="parent"
                        app:visibleGone="@{viewModel.versionObservable.type > 0}"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_arrow_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="10dp"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.userProtocol(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_user_protocol"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.privacyProtocol(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_privacy_protocol"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.userActionProtocol(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_user_action_protocol"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:onClick="@{v->callback.thirdProtocol(v)}">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:text="@string/me_third_protocol"
                        android:textSize="@dimen/common_text_sp_16"
                        android:textColor="@color/common_color_191919"/>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:onClick="@{v->callback.permissionProtocol(v)}">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:text="@string/me_permission_list"
                        android:textSize="@dimen/common_text_sp_16"
                        android:textColor="@color/common_color_191919"/>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.otherProtocol(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_other_protocol"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.bindProtocol(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_about_bind_protocol"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickCustomerService(v)}"
                    android:paddingLeft="20dp"
                    android:paddingTop="16dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="16dp"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="联系客服"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/common_ic_icon_common_setting_arrow_go"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>
        </ScrollView>

        <LinearLayout
            android:paddingHorizontal="20dp"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:paddingVertical="16dp"
                android:id="@+id/idLeft"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:onClick="@{v->callback.clickJingICP(v)}"
                android:text="@string/me_icp_num"
                android:textColor="@color/common_color_FFB8B8B8"
                android:textSize="@dimen/common_text_sp_12" />

            <TextView
                android:id="@+id/idRight"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="left"
                android:text="@string/me_icp_num2"
                android:textColor="@color/common_color_FFB8B8B8"
                android:textSize="@dimen/common_text_sp_12" />
        </LinearLayout>


    </LinearLayout>

</layout>