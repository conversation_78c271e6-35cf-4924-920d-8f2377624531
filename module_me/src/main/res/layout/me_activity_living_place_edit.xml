<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.MeLivingPlaceEditActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeLivingPlaceEditViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeLivingPlaceEditCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:right='@{viewModel.skipObservable?@string/me_skip:""}' />

        <ImageView
            android:id="@+id/iv_living_place"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="32dp"
            android:layout_marginLeft="18dp"
            android:src="@drawable/me_ic_living_place_page"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintTop_toTopOf="@id/iv_living_place"
            app:layout_constraintBottom_toBottomOf="@id/iv_living_place"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="18dp"
            app:index="@{viewModel.indexObservable - 1}"
            app:count="@{viewModel.countObservable}"
            app:visibleGone="@{viewModel.indexObservable > 0}"/>

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_living_place_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="12dp"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            android:text="@string/me_living_place_question_title"
            app:layout_constraintTop_toBottomOf="@+id/iv_living_place"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <com.kanzhun.common.views.wheel.pick.LinkageView
            android:id="@+id/pick_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="18dp"
            android:layout_marginEnd="18dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_living_place_title"
            android:orientation="horizontal" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:enabled="@{viewModel.enableSubmit}"
            app:greyDisabledStyle="@{viewModel.enableSubmit}"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text='@{viewModel.indexObservable>0&amp;&amp;viewModel.indexObservable&lt;viewModel.countObservable ? @string/me_next : @string/me_save}'
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>