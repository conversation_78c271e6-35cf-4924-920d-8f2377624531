<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="12dp"
    android:background="@color/common_white"
    android:paddingBottom="20dp"
    app:qmui_radius="12dp">

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/idTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:gravity="top|start"
        android:includeFontPadding="false"
        android:paddingRight="10dp"
        android:textColor="@color/common_color_191919"
        android:textSize="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="我的问答"
         />


    <ImageView
        android:layout_width="68dp"
        android:layout_height="84dp"
        android:background="@mipmap/me_qa_section_bg"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="5dp"
        android:src="@drawable/me_icon_preview_qa"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_question" />

    <TextView
        android:id="@+id/tv_question"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        android:gravity="top|start"
        android:includeFontPadding="false"
        android:paddingRight="10dp"
        android:textColor="@color/common_color_292929"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toBottomOf="@+id/idTitle"
        tools:text="十年后我的理想生活十年后我的理想生活十年后我的理想生活十年后我的理想生活十年后我的理想生活十年后我的理想生活" />

    <com.kanzhun.common.kotlin.ui.BlurryedLayout
        android:id="@+id/idBlurryLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_question">

        <LinearLayout
            android:id="@+id/idContentLinearLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_options"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/common_color_191919"
                android:textSize="20dp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="相似性、互补性" />

            <TextView
                android:id="@+id/tv_answer"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/common_color_191919"
                android:textSize="20dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_options"
                app:layout_goneMarginTop="12dp"
                tools:text="时的他们面对镜头采访难免会有些紧张，于是动作拘谨、表情略显僵" />

        </LinearLayout>



    </com.kanzhun.common.kotlin.ui.BlurryedLayout>

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/idSimpleDraweeView0"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/idBlurryLayout"
        tools:background="@color/common_color_7F7F7F"
        tools:layout_height="100dp"
        tools:visibility="visible" />

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/idSimpleDraweeView1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/idSimpleDraweeView0"
        tools:background="@color/common_color_0046BD"
        tools:layout_height="100dp"
        tools:visibility="visible" />

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/idSimpleDraweeView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/idSimpleDraweeView1"
        tools:background="@color/common_color_7F7F7F"
        tools:layout_height="100dp"
        tools:visibility="visible" />

    <com.kanzhun.foundation.views.PreviewLikeView
        android:id="@+id/idLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_gravity="right"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@id/idSimpleDraweeView2" />

</com.qmuiteam.qmui.layout.QMUIConstraintLayout>