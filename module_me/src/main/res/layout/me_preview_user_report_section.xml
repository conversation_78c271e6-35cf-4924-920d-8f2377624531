<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="12dp"
    android:background="@color/common_white"
    app:qmui_radius="12dp">

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvAboutMe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="@string/me_about_me"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_20"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.kanzhun.common.views.span.ZPUISpanTextView
        android:id="@+id/stv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="12dp"
        android:lineHeight="24dp"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_15"
        app:layout_constraintBottom_toTopOf="@id/clDynamic"
        app:layout_constraintTop_toBottomOf="@id/tvAboutMe"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_goneMarginBottom="24dp"
        app:zpui_stv_expand_color="@color/common_color_003580"
        app:zpui_stv_expand_text="@string/common_all"
        app:zpui_stv_max_line="6"
        app:zpui_stv_support_collapse="false"
        app:zpui_stv_support_custom_expand="false"
        app:zpui_stv_support_expand="true"
        app:zpui_stv_support_real_expand_or_collapse="true"
        tools:text="@string/common_long_placeholder" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDynamic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/stv_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/common_color_EEEEEE"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvDynamic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="23dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/me_newest_dynamic"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivAboutMeArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/me_preview_arrow"
            app:layout_constraintBottom_toBottomOf="@id/tvDynamic"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvDynamic" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatar1"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="11dp"
            app:common_radius="8dp"
            app:layout_constraintBottom_toBottomOf="@id/tvDynamic"
            app:layout_constraintEnd_toStartOf="@id/ivAboutMeArrow"
            app:layout_constraintTop_toTopOf="@id/tvDynamic"
            tools:background="@color/common_color_5E5E5E" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatar2"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            app:common_radius="8dp"
            app:layout_constraintBottom_toBottomOf="@id/tvDynamic"
            app:layout_constraintEnd_toStartOf="@id/ivAvatar1"
            app:layout_constraintTop_toTopOf="@id/tvDynamic"
            tools:background="@color/common_color_5E5E5E" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatar3"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            app:common_radius="8dp"
            app:layout_constraintBottom_toBottomOf="@id/tvDynamic"
            app:layout_constraintEnd_toStartOf="@id/ivAvatar2"
            app:layout_constraintTop_toTopOf="@id/tvDynamic"
            tools:background="@color/common_color_5E5E5E" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>