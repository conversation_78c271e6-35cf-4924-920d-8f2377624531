<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.kanzhun.marry.me.info.bean.ABImpression" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/common_bg_conor_12_color_white"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <ImageView
            android:id="@+id/iv_default_a_b"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:layout_marginLeft="12dp"
            android:src="@drawable/me_ic_a_b"
            app:layout_constraintBottom_toBottomOf="@+id/tv_default_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_default_title"
            app:visibleGone="@{bean.ABNum &lt;= 0}" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_default_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:text="@string/me_my_ab_impression"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@+id/iv_default_a_b"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginLeft="12dp" />

        <com.kanzhun.common.views.OTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:gravity="center"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintBottom_toBottomOf="@+id/tv_default_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_default_title"
            app:selectCount="@{bean.ABNum}"
            app:totalCount="@{3}"
            tools:text="0/3" />

        <TextView
            android:id="@+id/tv_a_b_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/me_a_b_assist"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="@id/tv_default_title"
            app:layout_constraintRight_toLeftOf="@+id/iv_add"
            app:layout_constraintTop_toBottomOf="@+id/tv_default_title"
            app:visibleGone="@{bean.ABNum &lt;= 0}" />


        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:onClick="@{()->callback.clickAddAB()}"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:scaleType="fitStart"
            android:src="@drawable/me_info_add"
            app:layout_constraintBottom_toBottomOf="@id/tv_a_b_assist"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_default_title"
            app:visibleGone="@{bean.ABNum &lt; 3}" />

        <com.kanzhun.marry.me.info.views.NestRecyclerView
            android:id="@+id/rv_a_b"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_default_title"
            app:visibleGone="@{bean.ABNum > 0}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>