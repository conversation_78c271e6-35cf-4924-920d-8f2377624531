<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="12dp"
    android:background="@color/common_white"
    app:qmui_radius="12dp"
    tools:layout_margin="16dp">
    <!--我的照片-->
    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvPictureInformation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="@string/me_my_pictures"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_20"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</com.qmuiteam.qmui.layout.QMUIConstraintLayout>