<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/me_user_report_navigation"
    app:startDestination="@+id/reasonSelectFragment">

    <fragment
        android:id="@+id/reasonSelectFragment"
        android:name="com.kanzhun.marry.me.setting.fragment.UserReportListFragment"
        android:label="fragment_reason_select"
        tools:layout="@layout/me_fragment_user_report_list">
        <action
            android:id="@+id/action_reasonSelectFragment_to_reportSubmitFragment"
            app:destination="@id/reportSubmitFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

    </fragment>

    <fragment
        android:id="@+id/reportSubmitFragment"
        android:name="com.kanzhun.marry.me.setting.fragment.UserReportSubmitFragment"
        android:label="fragment_report_submit"
        tools:layout="@layout/me_fragment_user_report_submit" >
        <action
            android:id="@+id/action_reportSubmitFragment_to_successFragment"
            app:destination="@id/successFragment"
            app:popUpTo="@id/reasonSelectFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/successFragment"
        android:name="com.kanzhun.marry.me.setting.fragment.UserReportSuccessFragment"
        android:label="fragment_success"
        tools:layout="@layout/me_fragment_user_report_success" />



</navigation>