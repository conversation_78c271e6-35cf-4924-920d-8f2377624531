apply plugin: 'com.android.library'
apply from: "$rootDir/gradle/common_library.gradle"

android {
    defaultConfig {
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.twl.startup'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly deps.androidx.app_compat
    testImplementation 'junit:junit:4.12'
}