package com.kanzhun.marry.module_new_task.net

import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.response.RecommendTagContentGenerateResponse
import com.kanzhun.http.response.BaseResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface KApiTask {
    @GET(URLConfig.URL_GET_GUIDE_ITEMS)
    suspend fun getGuideItems2(): BaseResponse<GuideItemsResponse2>

    @GET(URLConfig.URL_RECOMMEND_TAG_CONTENT_GENERATE)
    suspend fun getRecommendTagContentGenerate(
        @Query("tagIds") tagIds: String, // 标签id
        @Query("scene") scene: Int // 场景 0:我的标签 1:爱好 2:理想型
    ): BaseResponse<RecommendTagContentGenerateResponse>
}