package com.kanzhun.marry.module_new_task.service;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance;
import com.kanzhun.foundation.router.TaskPageRouter;
import com.kanzhun.foundation.router.service.INewTaskService;
import com.kanzhun.marry.module_new_task.NoviceTaskApprovePerformance;
import com.sankuai.waimai.router.annotation.RouterService;

@RouterService(interfaces = INewTaskService.class, key = TaskPageRouter.NEW_TASK_SERVICE)
public class NewTaskService implements INewTaskService {
    @Override
    public AbsPerformance getNoviceTaskApproveFinishPerformance(FragmentActivity activity) {
        return new NoviceTaskApprovePerformance();
    }
}
