package com.kanzhun.marry.module_new_task.fragment.abtest

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.createApi
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.module_new_task.fragment.abtest.interest.Bean
import com.kanzhun.marry.module_new_task.fragment.abtest.interest.OrangeDicProfileTagResponse
import com.kanzhun.marry.module_new_task.net.ApiTask
import com.kanzhun.marry.module_new_task.net.KApiTask
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private const val TAG = "IntroSuggestViewModel"

class IntroSuggestViewModel : BaseViewModel() {
    var currentStep by mutableIntStateOf(-1) // 0/1/2/3
    var allSteps by mutableIntStateOf(4)
    var keywords = mutableStateListOf<Bean>()
    private var stepSelectedKeywords = mutableStateMapOf<Int, SnapshotStateList<Bean>>()
    var tagToContent by mutableStateOf<String?>(null)

    var isLoadingContent by mutableStateOf(false)

    fun initData(profileTagList: List<Bean>) {
        HttpExecutor.execute(
            RetrofitManager.getInstance().createApi(ApiTask::class.java).dicProfileTag("0"),
            object : BaseRequestCallback<OrangeDicProfileTagResponse?>() {
                override fun onSuccess(data: OrangeDicProfileTagResponse?) {
                    data?.result?.let { result ->
                        //region 若此前首善流程有选择个性标签，此处回显选中状态
                        val mutableProfileTagList = profileTagList.toMutableList()
                        if (isQaDebugUser()) {
                            if (result.size >= 2) {
                                result[0].subTag?.get(0)?.let {
                                    mutableProfileTagList.add(it)
                                }

                                result[1].subTag?.get(0)?.let {
                                    mutableProfileTagList.add(it)
                                }
                            }
                        }

                        if (mutableProfileTagList.isNotEmpty()) {
                            result.forEachIndexed { index, bean ->
                                bean.subTag?.forEach { subBean ->
                                    val selectedInterestTag =
                                        mutableProfileTagList.find { it.id == subBean.id }
                                    if (selectedInterestTag != null) {
                                        subBean.isSelected.value = true
                                        putStepSelectedKeywords(index, subBean)
                                    }
                                }
                            }
                        }
                        //endregion

                        keywords.clear()
                        keywords.addAll(result)
                        allSteps = result.size

                        currentStep = 0
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                }
            })
    }

    fun previousStep() {
        currentStep = if (currentStep > 0) currentStep - 1 else currentStep
    }

    fun nextStep() {
        currentStep = if (currentStep < allSteps - 1) currentStep + 1 else currentStep
    }

    fun confirm() {
        val tagIds = stepSelectedKeywords.values.flatten().joinToString(",") { it.id.toString() }

        //region 保存用户选择的标签
        HttpExecutor.requestSimplePost(
            URLConfig.URL_ORANGE_USER_UPDATE_USER_TAG, mutableMapOf<String, Any?>().apply {
                put("tagIds", tagIds)
            },
            object : SimpleRequestCallback() {
                override fun onSuccess() {
                }

                override fun dealFail(reason: ErrorReason?) {
                }
            })
        //endregion

        isLoadingContent = true
        viewModelScope.launch {
            try {
                createApi(KApiTask::class.java).getRecommendTagContentGenerate(
                    tagIds = tagIds,
                    scene = 0
                ).data?.content?.let {
                    tagToContent = it
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "getRecommendTagContentGenerate: $e")
            } finally {
                if (isQaDebugUser()) {
                    delay(1000)
                    tagToContent = "平日里我还喜欢XX、XX、XX\n" +
                            "在业余时间，我喜欢XX、XX、XX\n" +
                            "有空的时候，我通常喜欢XX、XX、XX\n" +
                            "我平时的兴趣爱好有XX、XX、XX"
                }

                isLoadingContent = false
            }
        }
    }

    fun putStepSelectedKeywords(step: Int, bean: Bean): String {
        val beans = stepSelectedKeywords[step]
        var addFailedReason = ""
        if (beans == null) {
            stepSelectedKeywords[step] = mutableStateListOf<Bean>().apply {
                add(bean)
            }
        } else {
            if (beans.size >= 8) {
                addFailedReason =
                    "不能超过8个标签哦" // 点选标签少于3个时toast“请选择至少3个标签哦”拦截，超过8个时toast“不能超过8个标签哦”拦截
            } else {
                beans.add(bean)
            }
        }

        return addFailedReason
    }

    fun removeStepSelectedKeywords(step: Int, bean: Bean) {
        val beans = stepSelectedKeywords[step]
        beans?.remove(bean)
    }

    fun getStepSelectedKeywords(step: Int): SnapshotStateList<Bean>? {
        return stepSelectedKeywords[step]
    }
}