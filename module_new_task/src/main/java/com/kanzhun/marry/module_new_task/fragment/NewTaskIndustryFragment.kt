package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.model.profile.IndustryBean
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentIndustryBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemIndustrySelectBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction

class NewTaskIndustryFragment : NewTaskBaseFragment<TaskFragmentIndustryBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
//            findNavController(mBinding.btnNext).navigate(R.id.action_industryFragment_to_jobFragment)
//            activityViewModel.nowPage++
            gotoNext()
            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                step = mBinding.idTitle.getTitle()
                source = activityViewModel.getPageSourceStr()
            }
        }
        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)

    }

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        if(activityViewModel.industry.isNullOrEmpty()){
            getStateLayout().showError()
            return
        }
        activityViewModel.allPage.observe(this){
            mBinding.idTitle.setAllStep("/"+activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep("${activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1)}")
        }
        mBinding.rvIndustry.layoutManager = FlexboxLayoutManager(context)
        val adapter =  buildMultiTypeAdapterByType {
            layout(TaskItemIndustrySelectBinding::inflate) { _, bean: IndustryBean ->
                binding.idText.text = bean.name
                binding.idText.tag = bean.code
                binding.idText.setBackgroundColor(if(activityViewModel.initIndustryCode.equals(bean.code)) R.color.common_color_E8EEFF.toResourceColor() else R.color.common_color_F5F5F5.toResourceColor())
                binding.idText.setTextColor(if(activityViewModel.initIndustryCode.equals(bean.code)) R.color.common_color_7171FF.toResourceColor() else R.color.common_color_545454.toResourceColor())

                binding.root.setOnClickListener {
                    mBinding.btnNext.enable(true)
                    activityViewModel.selectInitIndustryCode(bean.getCode())
                    activityViewModel.selectInitIndustryName(bean.name)

                    adapter.notifyDataSetChanged()
                }
            }
        }

        if(activityViewModel.initIndustryCode.isNotEmpty()){
            mBinding.btnNext.enable(true)
        }else{
            mBinding.btnNext.enable(false)
        }

        mBinding.rvIndustry.adapter = adapter
        adapter.replaceData(activityViewModel.industry)

    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {
        activityViewModel.requestIndustry({
            showFragmentContent()
        }, {
            showFragmentContent()
        })
    }

    override fun getStateLayout() = mBinding.idStateLayout
}