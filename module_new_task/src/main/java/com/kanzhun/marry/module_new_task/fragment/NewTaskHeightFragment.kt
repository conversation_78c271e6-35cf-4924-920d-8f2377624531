package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.views.wheel.pick.adapter.NumericWheelAdapter
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.kernel.account.Account
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentHeightEditBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction

class NewTaskHeightFragment:NewTaskBaseFragment<TaskFragmentHeightEditBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
            val realStature: Int =
                mBinding.wvStature.currentItem + Constants.MIN_STATURE_CM
            if (realStature != activityViewModel.initHeight) { // 只有身高改变时才更新
                activityViewModel.updateHeight(realStature){
                    gotoNext()
                    reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                        step = mBinding.idTitle.getTitle()
                        source = activityViewModel.getPageSourceStr()
                    }
                }
            } else {
                gotoNext()
                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                    step = mBinding.idTitle.getTitle()
                    source = activityViewModel.getPageSourceStr()
                }
            }
        }
        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)
    }

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        mBinding.wvStature.setAdapter(
            NumericWheelAdapter(
                Constants.MIN_STATURE_CM,
                Constants.MAX_STATURE_CM
            )
        )

        activityViewModel.allPage.observe(this){
            mBinding.idTitle.setAllStep("/"+activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep("${activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1)}")
        }

        context?.apply {
            mBinding.wvStature.setDividerColor(color(R.color.common_black))
            mBinding.wvStature.setTextColorOut(color(R.color.common_color_AAAAAA))
            mBinding.wvStature.setTextColorCenter(color(R.color.common_black))
            mBinding.wvStature.setPaintCenterBg(color(R.color.common_color_EEEEEE))
        }


        val currentPosition: Int =
            if (activityViewModel.initHeight >= Constants.MIN_STATURE_CM && activityViewModel.initHeight <= Constants.MAX_STATURE_CM) {
                activityViewModel.initHeight - Constants.MIN_STATURE_CM
            } else {
                val user = ServiceManager.getInstance().profileService.userLiveData.value
                val gender = user!!.gender
                if (gender == Account.ACCOUNT_GENDER_MEN) Constants.MALE_DEFAULT_STATURE_CM - Constants.MIN_STATURE_CM else Constants.FEMALE_DEFAULT_STATURE_CM - Constants.MIN_STATURE_CM
            }

        mBinding.wvStature.currentItem = currentPosition // 初始化时显示的数据
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = null
}