package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentCarHouseBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction

class NewTaskCarHouseFragment:NewTaskBaseFragment<TaskFragmentCarHouseBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
            if(activityViewModel.initCarHold == carSelect
                && houseSelect == activityViewModel.initHouseHold
                && carSelect != 0
                && houseSelect != 0){
                gotoNext()
                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                    step = mBinding.idTitle.getTitle()
                    source = activityViewModel.getPageSourceStr()
                }
            }else{
                activityViewModel.updateHouseCar(carSelect,houseSelect){
                    gotoNext()
                    reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                        step = mBinding.idTitle.getTitle()
                        source = activityViewModel.getPageSourceStr()
                    }
                }
            }
        }
        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)
    }

    var carSelect:Int = 0// 车 1 无 2 有
    var houseSelect:Int = 0

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        activityViewModel.allPage.observe(this){
            mBinding.idTitle.setAllStep("/"+activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep("${activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1)}")
        }
        carSelect = activityViewModel.initCarHold
        houseSelect = activityViewModel.initHouseHold

        mBinding.apply {
            idBtnHasHouse.setOnClickListener {
                houseSelect = 2
                initSelectUI()
            }
            idBtnNoHouse.setOnClickListener {
                houseSelect = 1
                initSelectUI()
            }

            idBtnHasCar.setOnClickListener {
                carSelect = 2
                initSelectUI()
            }
            idBtnNoCar.setOnClickListener {
                carSelect = 1
                initSelectUI()
            }
        }
        initSelectUI()
    }

    private fun initSelectUI() {
        mBinding.apply {
            if(carSelect == 2){
                idBtnHasCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929)
                idBtnNoCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5)
            }else if(carSelect == 1){
                idBtnHasCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5)
                idBtnNoCar.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929)
            }
            if(houseSelect == 2){
                idBtnHasHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929)
                idBtnNoHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5)
            }else if(houseSelect == 1){
                idBtnHasHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_f5f5f5)
                idBtnNoHouse.setBackgroundResource(R.drawable.common_bg_corner_23_color_292929)
            }
            if(carSelect == 0 || houseSelect == 0){
                mBinding.btnNext.enable(false)
            }else{
                mBinding.btnNext.enable(true)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout
}