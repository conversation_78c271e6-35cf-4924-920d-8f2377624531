package com.kanzhun.marry.module_new_task.fragment

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.databinding.Observable
import androidx.fragment.app.FragmentActivity
import androidx.navigation.Navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.viewbinding.ViewBinding
import com.common.AvoidOnResult
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.views.ItemTouchChangeListener
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.adapter.BindingViewHolder
import com.kanzhun.foundation.adapter.MultiTypeBindingAdapter
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.EmptyStoryItem
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.databinding.MeItemPicStoryShowBinding
import com.kanzhun.foundation.databinding.MeItemStoryAddNewBinding
import com.kanzhun.foundation.dialog.StoryGuideDialog
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.info.callback.ItemTouchHelperCallback
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentStoryBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.utils.base.LList
import com.kanzhun.utils.views.MultiClickUtil
import com.techwolf.lib.tlog.TLog
import com.zhihu.matisse.Matisse

class NewTaskStoryFragment : NewTaskBaseFragment<TaskFragmentStoryBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    var adapter: MultiTypeBindingAdapter<BaseStoryShowItem, ViewBinding>? = null
    var itemTouchHelperCallback: ItemTouchHelperCallback? = null
    var isDestory = false
    override fun initView() {
        mBinding.btnNext.onClick {
            gotoNext()
            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                step = mBinding.idTitle.getTitle()
                source = activityViewModel.getPageSourceStr()
            }
            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
                actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
                type = "下一步"
            }
        }

        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO) {
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    private fun updateSuggestLayout(showSuggest: Boolean) {
        if (showSuggest) {
            mBinding.tvSuggestSwitch.text = "隐藏提示"
            mBinding.tvSuggestText.text = "清晰丰富的照片类型更容易获得关注。"
//            reportPoint("newguidance-mylife-photo-click") {
//                actionp2 = "打开"
//            }
        } else {
            mBinding.tvSuggestSwitch.text = "查看提示"
            mBinding.tvSuggestText.text = "不知道什么照片更吸睛？我们来给你建议。"
        }
        SpManager.get().user().edit().putBoolean(Constants.SP_STORY_HINT_SWITCH, showSuggest)
            .apply()

    }

    private fun updateSuggestVisible() {
        mBinding.clSuggest.visibility =
            if (activityViewModel.count.get() >= 6) View.GONE else View.VISIBLE

    }

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        isDestory = false
        mBinding.rvStory.layoutManager = GridLayoutManager(context, 3)
        activityViewModel.allPage.observe(this) {
            mBinding.idTitle.setAllStep("/" + activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep(
                "${
                    activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(
                        1
                    )
                }"
            )
        }
        updateSuggestLayout(activityViewModel.showSuggest)
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_EXPO) {
            actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
        }
        updateSuggestVisible()
        activityViewModel.updateSuggestVisible.observe(this) {
            if (it) {
                updateSuggestVisible()
            }
        }
        mBinding.tvSuggestSwitch.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View) {


                activityViewModel.showSuggest = !activityViewModel.showSuggest
                updateSuggestLayout(activityViewModel.showSuggest)

                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
                    actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
                    type = if (activityViewModel.showSuggest) "查看提示" else "隐藏提示"
                }

                val list = activityViewModel.storySelectLiveData.value
                if (!LList.isEmpty(list)) {
                    val newList: MutableList<BaseStoryShowItem> = java.util.ArrayList()
                    for (i in list!!.indices) {
                        val storyItem = list[i]
                        newList.add(storyItem)
                    }
                    activityViewModel.updateThem(newList)
                }

            }
        })
        adapter = buildMultiTypeAdapterByType {
            layout(MeItemPicStoryShowBinding::inflate) { _, story: PicStoryItem ->
                if (story.storyText.isNullOrEmpty()) {
                    binding.idTxtIcon.gone()
                } else {
                    binding.idTxtIcon.visible()
                }
                if (story.certStatus.get() == ProfileMetaModel.STATUS_REJECTED) {
                    binding.idIdentify.visible()
                } else {
                    binding.idIdentify.gone()
                }
                if (story.getFile() != null) {
                    binding.ovStory.loadRoundUri(story.getFile())
                } else {
                    if (!TextUtils.isEmpty(story.getUrl())) {
                        activityViewModel.downLoadItemFile(story, binding.ovStory)
                    }
                }
                story.upLoadPercent.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        storyItemUpdate(story)
                    }
                })
                story.addFailed.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        storyItemUpdate(story)
                    }
                })
                story.downLoadPercent.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        if (activity == null) return
                        if (activity!!.isDestroyed) return
                        if (host == null) return
                        if (isDestory) return
                        storyItemUpdate(story)
                        if (story.downLoadPercent.get() == 100) {
                            binding.ovStory.loadRoundUri(story.getFile())
                            TLog.print(
                                "zl_log",
                                "NewTaskStoryFragment: story.getFile()=%s，story.hashCode() = %s",
                                story.getFile(),
                                story.hashCode()
                            );
                        }

                    }
                })
                story.downLoadFailed.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        storyItemUpdate(story)
                    }
                })
                story.uploaded.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        if (isDestory) return
                        showUploadSuccessAnimation(binding.idEndAnim, story.uploaded.get(), story)
                    }
                })

                activityViewModel.canShowStoryDelete.addOnPropertyChangedCallback(object :
                    Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                        if (isDestory) return
                        if (activityViewModel.canShowStoryDelete.get()) {
                            binding.ivDelete.visible()
                        } else {
                            binding.ivDelete.gone()
                        }
                    }
                })

                storyItemUpdate(story)

                if (activityViewModel.canShowStoryDelete.get()) binding.ivDelete.visible() else binding.ivDelete.gone()
                binding.root.onClick { clickPic(story) }
                binding.ivDelete.onClick { clickDeletePic(story) }
                binding.tvRetry.onClick { clickRetryStory(story) }
                binding.tvRetryDown.onClick {
                    clickRetryDown(
                        story,
                        binding.ovStory
                    )
                }
            }
            layout(MeItemStoryAddNewBinding::inflate) { _, story: EmptyStoryItem ->
                binding.tvAdd.text = story.addText
                if (activityViewModel.showSuggest) {
                    binding.tvAdd.visible()
                    val index = activityViewModel.storySuggestList.map { it.text }.indexOf(story.addText)
                    activityViewModel.getAndUpdateList(
                        index + 1,
                        activityViewModel.storySuggestList
                    )
                } else {
                    binding.tvAdd.gone()
                }
                binding.root.onClick {
                    reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
                        actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
                        actionp3 = story.addText
                        type = "添加"
                    }
                    clickPicAdd(6 - activityViewModel.count.get())
                }
            }
        }

        bean = activityViewModel.storyList
        nextEnable(bean)
        mBinding.rvStory.adapter = adapter
        adapter?.replaceData(bean)
        activityViewModel.storySelectLiveData.observe(
            this
        ) {
            ExecutorFactory.runOnUiThread {
                activityViewModel.resetStoryList(it)
                nextEnable(it)
                itemTouchHelperCallback?.setEnableLastDrag(activityViewModel.count.get() > 5)
                itemTouchHelperCallback?.setCount(activityViewModel.count.get())
                adapter?.replaceData(it)
            }
        }

        adapter?.apply {
            itemTouchHelperCallback =
                ItemTouchHelperCallback(
                    this,
                    activityViewModel.count.get() > 5,
                    activityViewModel.count.get()
                ) { activityViewModel.storySelectLiveData.value }
            itemTouchHelperCallback?.setLongPressDragEnabled(true)
            itemTouchHelperCallback?.setListener(object : ItemTouchChangeListener {
                override fun touchChanged() {
                    reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
                        actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
                        type = "长按拖动"
                    }
                }

                override fun onDragFinish() {
                    activityViewModel.storySelectLiveData.value?.apply {
                        activityViewModel.sortStory(this)
                    }
                }
            })
            val helper = ItemTouchHelper(itemTouchHelperCallback!!)
            helper.attachToRecyclerView(mBinding.rvStory)
        }
    }

    var bean: ArrayList<BaseStoryShowItem>? = null

    private fun nextEnable(bean: List<BaseStoryShowItem>?) {
        if (bean == null) return
        if (activityViewModel.count.get() > 0) {
//            if (activityViewModel.count.get() > 1) {
//                mBinding.clDragAssist.visible()
//            } else {
//                mBinding.clDragAssist.gone()
//            }
            bean.forEach {
                if (it !is EmptyStoryItem && (it.photoReject == 2 || it.textReject == 2 || it.upLoadPercent.get() < 100)) {
                    mBinding.btnNext.enable(false)
                    return
                }
            }
            mBinding.btnNext.enable(true)
        } else {
//            mBinding.clDragAssist.gone()
            mBinding.btnNext.enable(false)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isDestory = true
    }

    fun clickPicAdd(max: Int) {
//        val hasShowGuide = SpManager.get().user().getBoolean(Constants.HAS_SHOW_STORY_GUIDE, false)
//        if (hasShowGuide) {
//            addStory(max)
//        } else {
//            SpManager.putUserBoolean(Constants.HAS_SHOW_STORY_GUIDE, true)
        val builder = StoryGuideDialog.Builder(activity as FragmentActivity)
            .setPadding(0, 0)
            .add(R.id.btn_finish)
            .setOnItemClickListener { dialog, view ->
                addStory(max)
                dialog.dismiss()
            }
        builder.create().show()
//        }
    }

    private fun addStory(max: Int) {
        selectStoryFile(max)
    }

    private fun selectStoryFile(count: Int) {
        PhotoSelectManager.jumpForGalleryMultipleCropResult(
            activity as FragmentActivity,
            count,
            object : AvoidOnResult.Callback {
                override fun onActivityResult(
                    requestCode: Int,
                    resultCode: Int,
                    data: Intent?
                ) {
                    if (resultCode == Activity.RESULT_OK) {
                        val files = Matisse.obtainResult(data)
                        if (MultiClickUtil.isMultiClick()) {
                            return
                        }
                        if (LList.isEmpty<Uri>(files)) {
                            return
                        }
                        activityViewModel?.selectFiles = files
                        val bundle = Bundle()
                        bundle.putInt(BundleConstants.BUNDLE_TYPE, 0)
                        bundle.putInt(
                            BundleConstants.BUNDLE_TEXT_REJECT_TYPE,
                            ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
                        )
                        bundle.putInt(
                            BundleConstants.BUNDLE_PHOTO_REJECT_TYPE,
                            ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
                        )
                        bundle.putString(BundleConstants.BUNDLE_STATURE, "")
                        bundle.putBoolean("activityViewModel", true)
                        bundle.putInt("maxImageCount", 6 - activityViewModel.count.get())
                        findNavController(mBinding.btnNext).navigate(
                            R.id.action_storyFragment_to_storyFragmentAdd,
                            bundle
                        )
                    }
                }
            })
    }


    fun clickRetryDown(data: BaseStoryShowItem, imageView: OImageView) {
        data.setDownLoadFailed(false)
        data.setDownLoadPercent(0)
        activityViewModel.downLoadItemFile(data, imageView)
    }


    fun clickDeletePic(story: BaseStoryShowItem) {
        if (TextUtils.isEmpty(story.getId())) { //未生成故事
            activityViewModel.deleteStoryLocalData(story)
        } else {
            activityViewModel.deleteStory(story)
        }
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
            actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
            type = "删除"
        }
    }

    fun clickRetryStory(data: PicStoryItem) {
        data.setAddFailed(false)
        if (data.getUpLoadPercent().get() >= 100) { //文件上传成功
            activityViewModel.addPicStory(data)
        } else {
            activityViewModel.upLoadImage(data)
        }
    }


    fun clickPic(data: BaseStoryShowItem) {
        if (TextUtils.isEmpty(data.id)) {
            return
        }
        if (data.file == null) {
            return
        }
        if (!data.getAddFailed().get() && data.getUpLoadPercent().get() < 100) { //故事上传中不可点击
            return
        }
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_MY_LIFE_PHOTO_CLICK) {
            actionp2 = if (activityViewModel.showSuggest) "打开" else "关闭"
            type = "查看"
        }
        if (data.certStatus.get() == ProfileMetaModel.STATUS_REJECTED) {
            reEditRejectedStoryFiles(data)
            return
        }
        reEdit(data)
    }

    private fun reEditRejectedStoryFiles(baseStoryShowItem: BaseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.id) && !baseStoryShowItem.addFailed.get()) {
            var textReject = baseStoryShowItem.textReject
            var photoReject = baseStoryShowItem.getPhotoReject()
            if (baseStoryShowItem.certStatus.get() == ProfileMetaModel.STATUS_REJECTED && textReject != ProfileInfoModel.CONTENT_TYPE_REJECTED && photoReject != ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                textReject =
                    if (!TextUtils.isEmpty(baseStoryShowItem.storyText)) ProfileInfoModel.CONTENT_TYPE_REJECTED else ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
                photoReject = ProfileInfoModel.CONTENT_TYPE_REJECTED
            }

            activityViewModel?.reEditSelectId = baseStoryShowItem.id
            val bundle = Bundle()
            bundle.putInt(BundleConstants.BUNDLE_TYPE, 2)
            bundle.putInt(BundleConstants.BUNDLE_TEXT_REJECT_TYPE, textReject)
            bundle.putInt(BundleConstants.BUNDLE_PHOTO_REJECT_TYPE, photoReject)
            bundle.putString(BundleConstants.BUNDLE_STATURE, baseStoryShowItem.certInfo)
            bundle.putBoolean("activityViewModel", true)
            findNavController(mBinding.btnNext).navigate(
                R.id.action_storyFragment_to_storyFragmentAdd,
                bundle
            )
        }
    }

    private fun reEdit(baseStoryShowItem: BaseStoryShowItem) {
        if (!TextUtils.isEmpty(baseStoryShowItem.id) && !baseStoryShowItem.addFailed.get()) {
            activityViewModel?.reEditSelectId = baseStoryShowItem.id
            val bundle = Bundle()
            bundle.putInt(BundleConstants.BUNDLE_TYPE, 1)
            bundle.putInt(
                BundleConstants.BUNDLE_TEXT_REJECT_TYPE,
                ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
            )
            bundle.putInt(
                BundleConstants.BUNDLE_PHOTO_REJECT_TYPE,
                ProfileInfoModel.CONTENT_TYPE_UN_REJECTED
            )
            bundle.putString(BundleConstants.BUNDLE_STATURE, baseStoryShowItem.certInfo)
            bundle.putBoolean("activityViewModel", true)
            findNavController(mBinding.btnNext).navigate(
                R.id.action_storyFragment_to_storyFragmentAdd,
                bundle
            )

        }
    }


    override fun initData() {
    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout

    private fun BindingViewHolder<MeItemPicStoryShowBinding>.storyItemUpdate(
        story: PicStoryItem,
    ) {
        try {
            if (isDestory) return
            if (activity == null) return
            if (activity!!.isDestroyed) return
            if (host == null) return
            if (!story.addFailed.get() && story.upLoadPercent.get() < 100) {
                binding.idBG.visible()
                binding.pbProgress.visible()
                binding.pbProgress.progress = story.upLoadPercent.get().toFloat()
            } else {
                binding.idBG.gone()
                binding.pbProgress.gone()
                nextEnable(activityViewModel.storyList)
            }
            if (story.addFailed.get()) {
                binding.tvRetry.visible()
            } else {
                binding.tvRetry.gone()
            }
            if (story.downLoadFailed.get()) {
                binding.tvRetryDown.visible()
            } else {
                binding.tvRetryDown.gone()
            }
            if (!story.downLoadFailed.get() && story.downLoadPercent.get() < 100) {
                binding.idBG.visible()
                binding.downProgress.visible()
                binding.downProgress.progress = story.downLoadPercent.get().toFloat()
            } else {
                binding.idBG.gone()
                binding.downProgress.gone()
            }
        } catch (e: Exception) {

        }

    }

    fun showUploadSuccessAnimation(
        view: View,
        showAnimation: Boolean,
        storyShowItem: BaseStoryShowItem,
    ) {
        if (showAnimation) {
            view.visibility = View.VISIBLE
            val animator = ValueAnimator.ofFloat(0.2f, 1.0f, 1.0f).setDuration(500)
            animator.addUpdateListener { animation ->
                val value = animation.animatedValue as Float
                view.scaleY = value
                view.scaleX = value
                view.alpha = value
            }
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {}
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    storyShowItem.setUploaded(false)
                }

                override fun onAnimationCancel(animation: Animator) {
                    view.visibility = View.GONE
                    storyShowItem.setUploaded(false)
                }

                override fun onAnimationRepeat(animation: Animator) {}
            })
            animator.start()
        }
    }

}