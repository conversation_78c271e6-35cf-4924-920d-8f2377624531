package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.views.wheel.pick.LinkagePickerOption
import com.kanzhun.common.views.wheel.pick.listener.OnOptionsSelectChangeListener
import com.kanzhun.foundation.api.model.AreaBean
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentLivePlaceBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.utils.base.LList

class NewTaskLIvePlaceFragment:NewTaskBaseFragment<TaskFragmentLivePlaceBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
            if(!activityViewModel.getLocalAddressCodeCode().isNullOrEmpty() && activityViewModel.getLocalAddressCodeCode()  == activityViewModel.user?.addressCode){
                gotoNext()
                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                    step = mBinding.idTitle.getTitle()
                    source = activityViewModel.getPageSourceStr()
                }
            }else{
                activityViewModel.saveAddressCode(activityViewModel.getLocalAddressCodeCode()) {
                    gotoNext()
                    reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
                        step = mBinding.idTitle.getTitle()
                        source = activityViewModel.getPageSourceStr()
                    }
                }
            }
        }
        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)

    }

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        if (LList.isEmpty(activityViewModel.areaOptions1Items) || LList.isEmpty(
                activityViewModel.areaOptions2Items
            )
        ) {
            mBinding.btnNext.isEnabled = false
            mBinding.idStateLayout.showError()
            return
        }
        activityViewModel.allPage.observe(this){
            mBinding.idTitle.setAllStep("/"+activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep("${activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1)}")
        }
        mBinding.idStateLayout.showContent()
        mBinding.btnNext.isEnabled = true
        val linkagePickerOption = LinkagePickerOption<AreaBean>()
        context?.apply {
            linkagePickerOption.dividerColor = color(R.color.common_black)
            linkagePickerOption.textColorOut = color(R.color.common_color_AAAAAA)
            linkagePickerOption.textColorCenter = color(R.color.common_black)
            linkagePickerOption.bgColorCenter = color(R.color.common_color_EEEEEE)
        }
        linkagePickerOption.mOptions1Items = activityViewModel.areaOptions1Items
        linkagePickerOption.mOptions2Items = activityViewModel.areaOptions2Items
        linkagePickerOption.optionsSelectChangeListener =
            OnOptionsSelectChangeListener { options1, options2, options3 ->
                activityViewModel.areaOptions1position = options1
                activityViewModel.areaOptions2position = options2
            }


        linkagePickerOption.options1 = activityViewModel.areaOptions1position
        linkagePickerOption.options2 = activityViewModel.areaOptions2position

        mBinding.pickView.setPickerOption(linkagePickerOption)
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {
        activityViewModel.getAre({
            showFragmentContent()
        },{
            showFragmentContent()
        })
    }

    override fun getStateLayout() = mBinding.idStateLayout
}