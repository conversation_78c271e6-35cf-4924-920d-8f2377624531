<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="PanelSwitchLayout">
        <attr name="animationSpeed" format="integer">
            <enum name="slowest" value="300" />
            <enum name="slow" value="250" />
            <enum name="standard" value="200" />
            <enum name="fast" value="150" />
            <enum name="fastest" value="100" />
        </attr>
    </declare-styleable>

    <declare-styleable name="PanelView">
        <attr name="panel_trigger" format="reference" />
        <attr name="panel_toggle" format="boolean" />
        <attr name="panel_layout" format="reference" />
    </declare-styleable>

    <attr name="edit_view" format="reference" />
    <attr name="auto_reset_enable" format="boolean" />
    <attr name="auto_reset_area" format="reference" />

    <declare-styleable name="LinearContentContainer">
        <attr name="edit_view" />
        <attr name="auto_reset_enable" />
        <attr name="auto_reset_area" />
    </declare-styleable>

    <declare-styleable name="FrameContentContainer">
        <attr name="edit_view" />
        <attr name="auto_reset_enable" />
        <attr name="auto_reset_area" />
    </declare-styleable>

    <declare-styleable name="RelativeContentContainer">
        <attr name="edit_view" />
        <attr name="auto_reset_enable" />
        <attr name="auto_reset_area" />
    </declare-styleable>

</resources>