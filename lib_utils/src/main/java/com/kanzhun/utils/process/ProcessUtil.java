package com.kanzhun.utils.process;

import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Process;
import android.text.TextUtils;

import com.kanzhun.utils.L;
import com.kanzhun.utils.file.FileUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.lang.reflect.Method;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/7.
 */

public class ProcessUtil {

    private static String sProcessName = "";

    /**
     * 获得当前进程名字
     *
     * @param context
     * @return
     */
    public static String getCurProcessName(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            sProcessName = Application.getProcessName();
        }

        if (TextUtils.isEmpty(sProcessName)) {
            sProcessName = getProcessNameFromActivityThread();
        }

        if (TextUtils.isEmpty(sProcessName)) {
            int pid = Process.myPid();
            ActivityManager activityManager = (ActivityManager) context
                    .getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningAppProcessInfo> processInfos = activityManager
                    .getRunningAppProcesses();
            if (processInfos != null) {
                for (ActivityManager.RunningAppProcessInfo appProcess : processInfos) {
                    if (appProcess.pid == pid) {
                        sProcessName = appProcess.processName;
                        break;
                    }
                }
            }
        }

        if(TextUtils.isEmpty(sProcessName)){
            sProcessName = getProcessNameFromLinuxFile(Process.myPid());
            L.e("ProcessUtil", "getProcessNameFromLinuxFile:" + sProcessName);
        }
        return sProcessName;
    }

    /**
     * 反射ActivityThread hide方法获取进程名
     *
     * @return
     */
    private static String getProcessNameFromActivityThread() {
        String processName = null;
        try {
            final Method nameMethod = Class.forName("android.app.ActivityThread")
                    .getDeclaredMethod("currentProcessName", new Class[0]);
            nameMethod.setAccessible(true);
            processName = (String) nameMethod.invoke(null, new Object[0]);
        } catch (Throwable e) {
        }
        return processName;
    }

    /**
     * 从cmdline获取当前进程名称
     *
     * @param pid
     * @return
     */
    private static String getProcessNameFromLinuxFile(int pid) {
        String processName = "";
        BufferedReader cmdlineReader = null;
        try {
            cmdlineReader = new BufferedReader(new FileReader("/proc/" + pid + "/cmdline"));
            processName = cmdlineReader.readLine();
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            FileUtils.closeCloseable(cmdlineReader);
        }
        return processName;
    }


    /**
     * 是否是主进程
     *
     * @param context
     * @return
     */
    public static boolean isMainProcess(Context context) {
        boolean ret = false;
        if (context != null) {
            ret = context.getPackageName().equals(getCurProcessName(context));
        }
        return ret;
    }
}
