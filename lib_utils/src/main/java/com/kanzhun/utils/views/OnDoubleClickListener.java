package com.kanzhun.utils.views;

import android.os.SystemClock;
import android.view.View;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/5/13
 * 双击事件
 */
public abstract class OnDoubleClickListener implements View.OnClickListener {
    public static final int DOUBLE_TIME = 1000;
    private final int mDoubleClickTime;
    private long mLastClickTime = 0;

    public OnDoubleClickListener() {
        mDoubleClickTime = MultiClickUtil.DELAY_DEFAULT;
    }

    public OnDoubleClickListener(int time) {
        mDoubleClickTime = time;
    }

    @Override
    public void onClick(View v) {
        long currentTime = SystemClock.elapsedRealtime();
        if (currentTime - mLastClickTime < mDoubleClickTime) {
            OnDoubleClick(v);
        }
        mLastClickTime = currentTime;
    }

    /**
     * 双击返回
     */
    public abstract void OnDoubleClick(View v);
}
