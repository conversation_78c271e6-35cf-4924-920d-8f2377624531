package com.kanzhun.utils;

import android.text.TextUtils;

import com.kanzhun.utils.configuration.Configuration;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.jurisdiction.Jurisdiction;
import com.kanzhun.utils.jurisdiction.JurisdictionUtils;

import java.util.HashMap;

/**
 * create by sunyangyang
 * on 2020/3/14
 */
public class SettingBuilder {
    public static final int HOST_CONFIG_TYPE_ONLINE = 1;
    public static final int HOST_CONFIG_TYPE_PRE = 2;
    public static final int HOST_CONFIG_TYPE_QA = 3;
    public static final int HOST_CONFIG_TYPE_RD = 4;
    private static SettingBuilder instance;
    private String flavor;
    private boolean debug;
    private int versionCode;
    private String versionName;
    private int mediaMaxSize;//media服务上传下载最大MB
    private HashMap<Integer, Jurisdiction> jurisdictions = new HashMap<>();
    private HashMap<Integer, Configuration> configurations = new HashMap<>();
    private HashMap<Integer, UserSettingConfig> userSettingConfigs = new HashMap<>();
    private int configType;

    private SettingBuilder() {
    }

    public static SettingBuilder getInstance() {
        if (instance == null) {
            synchronized (SettingBuilder.class) {
                if (instance == null) {
                    instance = new SettingBuilder();
                }
            }
        }
        return instance;
    }

    public static class Builder {
        private String flavor;
        private Boolean debug = null;
        private int versionCode;
        private String versionName;
        public int mediaMaxSize;//media服务上传下载最大MB
        private HashMap<Integer, Jurisdiction> jurisdictions = new HashMap();
        private HashMap<Integer, Configuration> configurations = new HashMap();
        private HashMap<Integer, UserSettingConfig> userSettingConfigs = new HashMap();
        private int configType;

        public Builder setFlavor(String flavor) {
            this.flavor = flavor;
            return this;
        }

        public Builder setDebug(boolean debug) {
            this.debug = debug;
            return this;
        }

        public Builder setVersionCode(int versionCode) {
            this.versionCode = versionCode;
            return this;
        }

        public Builder setVersionName(String versionName) {
            this.versionName = versionName;
            return this;
        }

        public Builder setMediaMaxSize(int mediaMaxSize) {
            this.mediaMaxSize = mediaMaxSize;
            return this;
        }

        public Builder addJurisdiction(Integer key, Jurisdiction value) {
            jurisdictions.put(key, value);
            return this;
        }

        public Builder addConfiguration(Integer key, Configuration value) {
            configurations.put(key, value);
            return this;
        }

        public Builder addUserSetting(Integer key, UserSettingConfig value) {
            userSettingConfigs.put(key, value);
            return this;
        }

        public Builder setConfigType(int type) {
            this.configType = type;
            return this;
        }

        public SettingBuilder build() {
            instance = getInstance();
            if (!TextUtils.isEmpty(flavor)) {
                instance.flavor = flavor;
            }
            if (debug != null) {
                instance.debug = debug;
            }
            if (versionCode > 0) {
                instance.versionCode = versionCode;
            }
            if (!TextUtils.isEmpty(versionName)) {
                instance.versionName = versionName;
            }
            if (mediaMaxSize > 0) {
                instance.mediaMaxSize = mediaMaxSize;
            }
            if (jurisdictions.size() > 0) {
                instance.jurisdictions.putAll(jurisdictions);
            }
            if (configurations.size() > 0) {
                instance.configurations.putAll(configurations);
            }
            if (configType > 0) {
                instance.configType = configType;
            }

            if (userSettingConfigs.size() > 0) {
                instance.userSettingConfigs.putAll(userSettingConfigs);
            }
            return instance;
        }
    }

    public String getFlavor() {
        return flavor;
    }

    public boolean isDebug() {
        return debug;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public int getMediaMaxSize() {
        if (mediaMaxSize > 0) {
            return mediaMaxSize;
        }
        return 100;
    }

    public int getJurisdictionVisible(Integer key) {
        if (jurisdictions == null
                || jurisdictions.size() == 0
                || jurisdictions.get(key) == null) {
            return JurisdictionUtils.IDENTITY_INVISIBLE;
        }
        return jurisdictions.get(key).getVisible();
    }

    public Jurisdiction getJurisdiction(Integer key) {
        if (jurisdictions == null
                || jurisdictions.size() == 0
                || jurisdictions.get(key) == null) {
            return null;
        }
        return jurisdictions.get(key);
    }

    public String getConfigurationValue(Integer key) {
        if (configurations == null
                || configurations.size() == 0
                || configurations.get(key) == null) {
            return "";
        }
        return configurations.get(key).getValue();
    }

    public String getUserSettingValue(Integer key) {
        if (userSettingConfigs == null
                || userSettingConfigs.size() == 0
                || userSettingConfigs.get(key) == null) {
            return "";
        }
        return userSettingConfigs.get(key).getValue();
    }

    public int getConfigType() {
        return configType;
    }

    public boolean isOnline(){
        return configType == HOST_CONFIG_TYPE_ONLINE;
    }

    public boolean isPre(){
        return configType == HOST_CONFIG_TYPE_PRE;
    }

    public String getApmUidPassword() {
        if (configType == HOST_CONFIG_TYPE_ONLINE || configType == HOST_CONFIG_TYPE_PRE) { //线上、预发
            return "ADLIJFOI019DJIF0QWEIJDFNXKV0QWEJL1209JLQWE0912J";
        }
        return "LAKDF0WLEIOIWERLDSOFIJQWEO123OW";
    }


    public String getTemporaryUrl() {
        switch (configType) {
            case HOST_CONFIG_TYPE_ONLINE://线上
                return "wss://lengjing.weizhipin.com";
            case HOST_CONFIG_TYPE_PRE://预发
                return "wss://orange-rd.weizhipin.com";
            case HOST_CONFIG_TYPE_QA://QA
                return "wss://orange-qa.weizhipin.com";
            case HOST_CONFIG_TYPE_RD:
                return "wss://orange-rd.weizhipin.com";
            default:
                return "wss://lengjing.weizhipin.com";
        }
    }
}
