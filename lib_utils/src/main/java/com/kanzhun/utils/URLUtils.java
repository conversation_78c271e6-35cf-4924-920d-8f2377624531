package com.kanzhun.utils;

import android.text.TextUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by wa<PERSON><PERSON> on 2017/4/11.
 */

public class URLUtils {

    public static Map<String, String> parseUrlParam(String URL) {
        String strUrlParam = TruncateUrlPage(URL);
        return parseParam(strUrlParam);
    }

    public static String getTypeValueFromProtocol(String url) {
        Map<String, String> params = parseUrlParam(url);
        if (params.containsKey("type")) {
            return params.get("type");
        }
        return null;
    }

    /**
     * 解析url参数
     *
     * @param strUrlParam
     * @return
     */
    public static Map<String, String> parseParam(String strUrlParam) {
        Map<String, String> mapRequest = new HashMap<>();
        if (TextUtils.isEmpty(strUrlParam)) {
            return mapRequest;
        }
        String[] arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("[=]");
            if (arrSplitEqual.length <= 0) {
                continue;
            }
            if (arrSplitEqual.length > 1) {
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (!"".equals(arrSplitEqual[0])) {
                    //只有参数没有值，不加入
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 截取url参数
     *
     * @param strURL
     * @return
     */
    private static String TruncateUrlPage(String strURL) {
        if (strURL == null || strURL.length() == 0) {
            return "";
        }
        String strAllParam = null;
        String[] arrSplit = null;
        strURL = strURL.trim();
        arrSplit = strURL.split("[?]");
        if (arrSplit.length > 1) {
            if (arrSplit[1] != null) {
                strAllParam = arrSplit[1];
            }
        }
        return strAllParam;
    }

    public static String getHost(String url) {
        String host = "";
        if(TextUtils.isEmpty(url)){
            return host;
        }
        try {
            URL u = new URL(url);
            host = u.getHost();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return host;
    }

    public static String getMainHost(String str){
        try {
            URL url = new URL(str);
            String host = url.getHost();

            // 移除可能的www前缀
            if (host.startsWith("www.")) {
                host = host.substring(4);
            }

            // 使用点号(.)作为分隔符分割主机名
            String[] parts = host.split("\\.");

            // 假设一级域名是主机名的最后一部分
            if (parts.length > 0) {
                return parts[parts.length - 2] + "." +parts[parts.length - 1];
            } else {
                return "";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String replaceHost(String originUrl, String oldHost, String newHost) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            URL url = new URL(originUrl);
            if (TextUtils.equals(url.getHost(), oldHost)) {
                stringBuilder.append(url.getProtocol());
                stringBuilder.append("://");
                stringBuilder.append(newHost);
                int port = url.getPort();
                if (port != -1) {//port存在
                    stringBuilder.append(":");
                    stringBuilder.append(port);
                }
                stringBuilder.append(url.getPath());
                String urlParams = URLUtils.TruncateUrlPage(originUrl);
                if (!TextUtils.isEmpty(urlParams)) {
                    stringBuilder.append("?");
                    stringBuilder.append(urlParams);
                }
            } else {
                return originUrl;
            }
        } catch (Exception e) {
            return originUrl;
        }
        return stringBuilder.toString();
    }

    public static String deleteParamByKey(String originUrl, String key) {
        if (originUrl == null || originUrl.length() == 0) {
            return "";
        }
        originUrl = originUrl.trim();
        String[] arrSplit = originUrl.split("[?]");
        if (arrSplit.length > 1) {//有参数
            String strProtocol = arrSplit[0];
            String strAllParam = arrSplit[1];
            if (TextUtils.isEmpty(strAllParam) || TextUtils.isEmpty(strProtocol)) {
                return originUrl;
            }
            Map<String, String> urlParams = parseParam(strAllParam);
            if (!urlParams.containsKey(key)) {//不包含要去除的参数
                return originUrl;
            }
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(strProtocol);
            if (!urlParams.isEmpty() && urlParams.size() > 1) {
                stringBuilder.append("?");
                Iterator<Map.Entry<String, String>> it = urlParams.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, String> entry = it.next();
                    String entryKey = entry.getKey();
                    if (TextUtils.equals(entryKey, key)) {
                        continue;
                    }
                    String value = entry.getValue();
                    stringBuilder.append(entryKey);
                    stringBuilder.append("=");
                    stringBuilder.append(value);
                    stringBuilder.append("&");
                }
                int length = stringBuilder.length();
                if (TextUtils.equals(stringBuilder.subSequence(length - 1, length), "&")) {
                    stringBuilder.deleteCharAt(length - 1);
                }
            }
            return stringBuilder.toString();
        } else {//无参数
            return originUrl;
        }
    }

    /**
     * 获取没有参数的url
     *
     * @param originUrl
     * @return
     */
    public static String getNoParamsUrl(String originUrl) {
        if (originUrl == null || originUrl.length() == 0) {
            return "";
        }
        originUrl = originUrl.trim();
        String[] arrSplit = originUrl.split("[?]");
        if (arrSplit.length > 1) {//有参数
            return arrSplit[0];
        } else {
            return originUrl;
        }
    }
}
