package com.kanzhun.imageviewer.video;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.views.image.OImageView;
import com.kanzhun.foundation.player.OPlayerHelper;
import com.kanzhun.foundation.player.OViewRenderer;
import com.kanzhun.foundation.views.VideoDownloadProgressbar;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.download.callback.DownLoadListener;
import com.kanzhun.imageviewer.R;
import com.kanzhun.utils.file.FileUtils;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.techwolf.lib.tlog.TLog;

import org.alita.webrtc.SurfaceViewRenderer;

import java.io.File;

import io.reactivex.rxjava3.disposables.Disposable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/9/6
 */
public class OVideoView extends FrameLayout implements LifecycleEventObserver {
    private OViewRenderer viewRenderer;
    private OPlayerHelper oPlayerHelper;
    private Context context;
    private View coverContainer;
    private OImageView ivVideoCover;
    private ImageView ivCoverStatus;
    private VideoDownloadProgressbar downloadProgressbar;
    private ProgressBar progressBar;
    private boolean isFirstResume = true;
    private String targetUrl;
    private Disposable disposable;

    private View menuContainer;
    private TextView menuTime;
    private TextView menuEndTime;
    private SeekBar menuSeekbar;
    private ImageView menuPlayStatus;
    private ImageView menuPlayStatusSmall;
    private ImageView ivVideoClose;
    private ImageView ivVideoSave;
    private long menuDismissTime = 3000L;
    private long mLastProgress = 0L;
    private long mLastTotalTime = 0L;
    private boolean mIsProgressDragging = false;
    private OVideoViewCallback callback;
    private View viewBg;

    private boolean simpleMode = false;//简单模式，只留下播放按钮，进度等其他按钮隐藏

    private boolean downloadField; //下载失败
    private boolean isPlayed; //播放完毕

    private boolean isReady = false;//视频是否准备好了

    private boolean isResume = false;

    private boolean isAutoPlay = false;


    private Runnable menuDismiss = new Runnable() {
        @Override
        public void run() {
            if (!mIsProgressDragging) {
                menuContainer.setVisibility(View.INVISIBLE);
            } else {
                postDelayed(menuDismiss, menuDismissTime);
            }

        }
    };

    public OVideoView(@NonNull Context context) {
        this(context, null);
    }

    public OVideoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OVideoView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        initView(context);
        initListener();
    }

    public boolean isAutoPlay() {
        return isAutoPlay;
    }

    public void setAutoPlay(boolean autoPlay) {
        isAutoPlay = autoPlay;
    }

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.image_video_o_video_view, this, true);
        viewRenderer = findViewById(R.id.sv_video);
        coverContainer = findViewById(R.id.cover_container);
        ivVideoCover = findViewById(R.id.iv_video_cover);
        ivCoverStatus = findViewById(R.id.iv_cover_status);
        downloadProgressbar = findViewById(R.id.download_progress);
        progressBar = findViewById(R.id.idProgressBar);

        menuContainer = findViewById(R.id.menu_container);
        menuContainer.setVisibility(View.INVISIBLE);
        menuTime = findViewById(R.id.tv_player_times);
        menuEndTime = findViewById(R.id.tv_player_times_end);
        menuSeekbar = findViewById(R.id.seekBar);
        menuPlayStatus = findViewById(R.id.iv_play_status);
        menuPlayStatusSmall = findViewById(R.id.iv_play_status_small);
        ivVideoClose = findViewById(R.id.iv_video_back);
        ivVideoSave = findViewById(R.id.iv_video_save);

        viewBg = findViewById(R.id.view_bg);
    }

    private void initListener() {
        ivCoverStatus.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                if(!isReady){
                    return;
                }
                if (!downloadField) {
                    startPlay(targetUrl);
                } else {
                    downLoadFile();
                }

            }
        });

        menuPlayStatus.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!isReady){
                    return;
                }
                changePlay();
            }
        });

        menuPlayStatusSmall.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!isReady){
                    return;
                }
                if (!isPlayed) {
                    changePlay();
                } else {
                    startPlay(targetUrl);
                }
            }
        });

        ivVideoSave.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                saveFile();
            }
        });

        ivVideoClose.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                finish();
            }
        });

        viewRenderer.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showOrHideMenu();
            }
        });

        ivVideoCover.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isPlayed) {
                    showOrHideMenu();
                }
            }
        });

        menuSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                mLastProgress = mLastTotalTime * progress / 100;
                menuTime.setText(VideoUtils.getFormatTime(mLastProgress));
                menuEndTime.setText(VideoUtils.getFormatTime(mLastTotalTime));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                mIsProgressDragging = true;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                seek(mLastProgress);
                mIsProgressDragging = false;
            }
        });
    }

    private void tryPlay(){
        if(!isAutoPlay ){
            return;
        }
        if(!isResume && !isFirstResume){
            return;
        }
        if(!isReady){
            return;
        }
        startPlay(targetUrl);
    }

    private void saveFile() {
        if (callback != null) {
            callback.saveFile(targetUrl);
        }
    }

    private void finish() {
        if (callback != null) {
            callback.closePage();
        }
    }

    private void seek(long lastProgress) {
        if (coverContainer.getVisibility() == View.VISIBLE) {
            coverContainer.setVisibility(View.INVISIBLE);
            menuPlayStatus.setVisibility(View.VISIBLE);
        }
        oPlayerHelper.seekTo(lastProgress);
        setPlayStatusImg();
    }

    private void showOrHideMenu() {
        removeCallbacks(menuDismiss);
        if (menuContainer.getVisibility() == View.INVISIBLE) {
            menuContainer.setVisibility(View.VISIBLE);
            postDelayed(menuDismiss, menuDismissTime);
        } else {
            menuContainer.setVisibility(View.INVISIBLE);
        }
    }

    private void changePlay() {
        if (oPlayerHelper.isPlaying()) {
            pause();
        } else {
            resume();
        }
    }

    private void playEnd() {
//        menuContainer.setVisibility(View.INVISIBLE);
        coverContainer.setVisibility(View.VISIBLE);
        isPlayed = true;
        menuSeekbar.setProgress(0);
        menuPlayStatus.setVisibility(View.INVISIBLE);
        menuPlayStatusSmall.setImageResource(R.mipmap.image_video_ic_start_play_small);
    }

    private void setPlayStatusImg() {
        menuPlayStatus.setImageResource(oPlayerHelper.isPlaying() ? R.drawable.image_video_ic_stop_play : R.drawable.image_video_ic_start_play);
        menuPlayStatusSmall.setImageResource(oPlayerHelper.isPlaying() ? R.mipmap.image_video_ic_stop_play_small : R.mipmap.image_video_ic_start_play_small);
    }

    private void startPlay(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (oPlayerHelper != null && !oPlayerHelper.isSameSource(url)) {
            oPlayerHelper.onDestroyUnRemove();
            oPlayerHelper = null;
        }
        removeCallbacks(menuDismiss);
        isPlayed = false;
        menuPlayStatus.setVisibility(View.VISIBLE);
        if (oPlayerHelper == null) {
            viewBg.setVisibility(View.VISIBLE);
            oPlayerHelper = new OPlayerHelper(context, new OPlayerHelper.CallBack() {
                @Override
                public void onFirstFrame() {
                    coverContainer.setVisibility(View.INVISIBLE);
                    menuContainer.setVisibility(View.VISIBLE);
                    postDelayed(menuDismiss, menuDismissTime);
                    viewBg.setVisibility(View.GONE);
                }

                @Override
                public void onPlayEnd() {
                    playEnd();
                }

                @Override
                public void onProgress(int progress, int duration) {
                    mLastProgress = progress;
                    mLastTotalTime = duration;
                    setSeekBarProgress();
                }

                @Override
                public void loading(boolean b) {
                    if(b && !isShowLoading){
                        isShowLoading = true;
                        startLoading();
                    }else {
                        if(progressBar != null)progressBar.setVisibility(View.GONE);
                        setProgress(100);
                    }
                }
            });
            oPlayerHelper.setLooper(true);
            oPlayerHelper.setPlayView(viewRenderer);
            oPlayerHelper.setFrameScaleType(SurfaceViewRenderer.RENDER_MODE_ADJUST_RESOLUTION);
            oPlayerHelper.setPlayUrl(url);
        } else {
            coverContainer.setVisibility(View.INVISIBLE);
            menuContainer.setVisibility(View.VISIBLE);
            postDelayed(menuDismiss, menuDismissTime);
        }
        viewRenderer.setVisibility(View.VISIBLE);
        oPlayerHelper.start();
        startLoading();
        isShowLoading = false;
    }


    private void startLoading() {
        if(progressBar != null)progressBar.setVisibility(View.VISIBLE);
    }

    private void setSeekBarProgress() {
        if (!mIsProgressDragging) {
            if (mLastProgress < mLastTotalTime) {
                menuSeekbar.setProgress((int) (mLastProgress * 100 / mLastTotalTime));
            } else {
                menuSeekbar.setProgress(100);
                playEnd();
            }
        }
    }


    public void setCover(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        coverContainer.setVisibility(View.VISIBLE);
        ivVideoCover.load(url);
    }

    public void setCover() {
        coverContainer.setVisibility(View.VISIBLE);
    }


    public ImageView getVideoCover() {
        return ivVideoCover;
    }

    private void setProgress(int percent) {
        downloadProgressbar.setProgress(percent);
        if (percent == 100) {
//            downloadProgressbar.setVisibility(INVISIBLE);
            ivCoverStatus.setImageResource(R.drawable.image_video_ic_start_play);
//            ivCoverStatus.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 简单模式，只留下播放按钮，进度等其他按钮隐藏
     * @param simpleMode
     */
    public void setSimpleMode(boolean simpleMode) {
        this.simpleMode = simpleMode;
        ivVideoClose.setVisibility(simpleMode ? View.GONE : View.VISIBLE);
        ivVideoSave.setVisibility(simpleMode ? View.GONE : View.VISIBLE);
        menuSeekbar.setVisibility(simpleMode ? View.GONE : View.VISIBLE);
        menuPlayStatusSmall.setVisibility(simpleMode ? View.GONE : View.VISIBLE);
        menuEndTime.setVisibility(simpleMode ? View.GONE : View.VISIBLE);
        menuTime.setVisibility(simpleMode ? View.GONE : View.VISIBLE);

    }

    public void pause() {
        if (oPlayerHelper != null && oPlayerHelper.isPlaying()) {
            oPlayerHelper.pause();
            setPlayStatusImg();
        }
    }

    public void resume() {
        if (oPlayerHelper != null) {
            oPlayerHelper.resume();
            setPlayStatusImg();
        }
    }

    public void destroy() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        if (oPlayerHelper != null) {
            oPlayerHelper.setCallBack(null);
            oPlayerHelper.stop();
            oPlayerHelper.onDestroyUnRemove();
            oPlayerHelper = null;
        }
    }

    boolean isShowLoading = false;

    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        switch (event) {
            case ON_RESUME:
                isResume = true;
                tryPlay();
                if (isFirstResume) {
                    isFirstResume = false;
                }
                break;
            case ON_PAUSE:
                isResume = false;
                isShowLoading = false;
//                pause();
//                break;
//            case ON_DESTROY:
                destroy();
                break;
            default:
                break;
        }
    }


    public void setDataSource(String url) {
        this.targetUrl = url;

    }

    public void downLoadFile() {
        downloadField = false;
        ivCoverStatus.setVisibility(View.GONE);
        TLog.debug(OPlayerHelper.TAG, "onSuccess  + ，url = "+targetUrl);
        setProgress(100);
        isReady = true;
        if(isResume){
            tryPlay();
        }

    }

    /**
     * 动画关闭页面
     */
    public void beginBackToMin() {
        menuContainer.setVisibility(View.GONE);
        coverContainer.setVisibility(View.VISIBLE);
        ivCoverStatus.setVisibility(View.INVISIBLE);
        viewRenderer.setVisibility(View.GONE);
    }

    public void setCallback(OVideoViewCallback callback) {
        this.callback = callback;
    }

    public interface OVideoViewCallback {
        void saveFile(String path);

        void closePage();
    }
}
