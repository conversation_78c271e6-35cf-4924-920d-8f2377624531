package com.kanzhun.imageviewer.ui

import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.fragment.app.Fragment
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.imageviewer.ImageViewer
import com.kanzhun.imageviewer.ImageViewerView
import com.kanzhun.imageviewer.bean.FragmentConfig
import com.kanzhun.imageviewer.databinding.ImageFragmentImageViewerBinding
import com.kanzhun.imageviewer.interfaces.IImageViewerFragment
import com.kanzhun.imageviewer.interfaces.ImageViewLoadFactory
import com.kanzhun.imageviewer.loader.ContentLoader
import com.kanzhun.imageviewer.loader.FragmentCoverLoader
import com.kanzhun.imageviewer.tools.BitmapUtil
import com.kanzhun.imageviewer.tools.ImageViewerConstant
import com.kanzhun.imageviewer.ui.viewmodel.ImageViewerFragmentViewModel
import com.kanzhun.imageviewer.video.VideoContentLoaderImpl
import net.mikaelzero.mojito.loader.OnLongTapCallback
import net.mikaelzero.mojito.loader.OnTapCallback
import net.mikaelzero.mojito.loader.OnVideoCallback
import java.io.File

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/7
 */
class ImageViewerFragment : BaseBindingFragment<ImageFragmentImageViewerBinding, ImageViewerFragmentViewModel>(),
    IImageViewerFragment, com.kanzhun.imageviewer.interfaces.OnImageViewerViewCallback {

    private var mainHandler = Handler(Looper.getMainLooper())
    private var mImageLoader: com.kanzhun.imageviewer.loader.ImageLoader? = null
    private var mViewLoadFactory: ImageViewLoadFactory? = null
    private var fragmentCoverLoader: FragmentCoverLoader? = null
    private var iProgress: com.kanzhun.imageviewer.interfaces.IProgress? = null
    private var contentLoader: ContentLoader? = null
    private var showView: View? = null
    private var binding:ImageFragmentImageViewerBinding?=null

    companion object {
        fun newInstance(fragmentConfig: FragmentConfig): ImageViewerFragment {
            val args = Bundle()
            args.putParcelable(ImageViewerConstant.KEY_FRAGMENT_PARAMS, fragmentConfig)
            val fragment = ImageViewerFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun preInit(arguments: Bundle) {

    }

    override fun initView() {
        if (context == null || activity == null) {
            return
        }
        binding = mBinding
        if (arguments != null) {
            mViewModel.fragmentConfig = requireArguments().getParcelable(ImageViewerConstant.KEY_FRAGMENT_PARAMS)!!

        }
        mImageLoader = ImageViewer.imageLoader()
        mViewLoadFactory = if (ImageViewerActivity.multiContentLoader != null) {
            ImageViewerActivity.multiContentLoader?.providerLoader(mViewModel.fragmentConfig.position)
        } else {
            ImageViewer.imageViewFactory()
        }
        fragmentCoverLoader = ImageViewerActivity.fragmentCoverLoader?.providerInstance()
        mBinding.imageCoverLayout.removeAllViews()
        val fragmentCoverAttachView = fragmentCoverLoader?.attach(
            this,
            mViewModel.fragmentConfig.targetUrl == null || mViewModel.fragmentConfig.autoLoadTarget
        )
        if (fragmentCoverAttachView != null) {
            mBinding.imageCoverLayout.visibility = View.VISIBLE
            mBinding.imageCoverLayout.addView(fragmentCoverAttachView)
        } else {
            mBinding.imageCoverLayout.visibility = View.GONE
        }

        iProgress = ImageViewerActivity.progressLoader?.providerInstance()
        iProgress?.attach(mViewModel.fragmentConfig.position, mBinding.loadingLayout)
        contentLoader = mViewLoadFactory?.newContentLoader()
        if (contentLoader is VideoContentLoaderImpl) {
            //视频页面特殊处理
            lifecycle.addObserver(contentLoader as VideoContentLoaderImpl)
            (contentLoader as VideoContentLoaderImpl).setCallback(object : OnVideoCallback {
                override fun onSaveFile(path: String) {
                    ImageViewerActivity.onListener?.onSaveFile(getActivity(), path)
                }

                override fun onClosPage() {
                    backToMin()
                }
            })
        }
        mBinding.imageView.setBackgroundAlpha(
            if (ImageViewerActivity.hasShowedAnimMap[mViewModel.fragmentConfig.position] == true) 1f else if (mViewModel.fragmentConfig.showImmediately) 1f else 0f
        )
        mBinding.imageView.setOnImageViewerViewCallback(this)
        mBinding.imageView.setContentLoader(
            contentLoader,
            mViewModel.fragmentConfig.originUrl,
            mViewModel.fragmentConfig.targetUrl
        )
        showView = contentLoader?.providerRealView()

        contentLoader?.onTapCallback(object : OnTapCallback {
            override fun onTap(view: View, x: Float, y: Float) {
                mBinding.imageView.backToMin()
                ImageViewerActivity.onListener?.onClick(view, x, y, mViewModel.fragmentConfig.position)
            }
        })
        mBinding.loadingLayout.setOnClickListener {
            mBinding.imageView.backToMin()
            ImageViewerActivity.onListener?.onClick(mBinding.root.parent as View, 0f, 0f, mViewModel.fragmentConfig.position)
        }
        contentLoader?.onLongTapCallback(object : OnLongTapCallback {
            override fun onLongTap(view: View, x: Float, y: Float) {
                if (!mBinding.imageView.isDrag) {
                    ImageViewerActivity.onListener?.onLongClick(
                        getActivity(),
                        view,
                        x,
                        y,
                        mViewModel.fragmentConfig.position
                    )
                }
            }
        })
        val isFile: Boolean = File(mViewModel.fragmentConfig.originUrl).isFile
        val uri = if (isFile) {
            Uri.fromFile(File(mViewModel.fragmentConfig.originUrl))
        } else {
            Uri.parse(mViewModel.fragmentConfig.originUrl)
        }
        mImageLoader?.loadImage(
            showView.hashCode(),
            uri,
            !isFile,
            object : com.kanzhun.imageviewer.loader.DefaultImageCallback() {
                override fun onSuccess(image: File) {
                    mainHandler.post {
                        if (isDetached || context == null) {
                            return@post
                        }
                        mViewLoadFactory?.loadSillContent(showView!!, Uri.fromFile(image))
                        startAnim(image)
                    }
                }

                override fun onFail(error: Exception) {
                    mainHandler.post {
                        if (isDetached || context == null) {
                            return@post
                        }
                        startAnim(
                            com.kanzhun.imageviewer.tools.ScreenUtils.getScreenWidth(context),
                            com.kanzhun.imageviewer.tools.ScreenUtils.getScreenHeight(context),
                            originLoadFail = true,
                            needLoadImageUrl = mViewModel.fragmentConfig.originUrl
                        )
                    }
                }
            })
    }

    override fun initData() {
    }

    override fun onRetry() {
    }


    private fun startAnim(image: File) {
        val realSizes = getRealSizeFromFile(image)
        startAnim(realSizes[0], realSizes[1])
    }


    private fun startAnim(w: Int, h: Int, originLoadFail: Boolean = false, needLoadImageUrl: String = "") {
        val fragmentConfig = mViewModel.fragmentConfig
        if (!fragmentConfig.showImmediately) {
            ImageViewerActivity.onListener?.onStartAnim(fragmentConfig.position)
        }
        if (fragmentConfig.viewParams == null) {
            mBinding.imageView.showWithoutView(
                w,
                h,
                if (ImageViewerActivity.hasShowedAnimMap[fragmentConfig.position] == true) true else fragmentConfig.showImmediately
            )
        } else {
            mBinding.imageView.putData(
                fragmentConfig.viewParams!!.getLeft(), fragmentConfig.viewParams!!.getTop(),
                fragmentConfig.viewParams!!.getWidth(), fragmentConfig.viewParams!!.getHeight(),
                w, h
            )
            mBinding.imageView.show(if (ImageViewerActivity.hasShowedAnimMap[fragmentConfig.position] == true) true else fragmentConfig.showImmediately)
        }

        val targetEnable = if (ImageViewerActivity.multiContentLoader == null) {
            true
        } else {
            ImageViewerActivity.multiContentLoader?.providerEnableTargetLoad(fragmentConfig.position) ?: false
        }
        //查看原图的情况下  如果缩略图加载失败了  需要先加载缩略图  再根据条件判断是否要去加载原图
        if (originLoadFail && needLoadImageUrl.isNotEmpty()) {
            loadImageWithoutCache(needLoadImageUrl, fragmentConfig.targetUrl != null && targetEnable)
        } else if (fragmentConfig.targetUrl != null && targetEnable) {
            replaceImageUrl(fragmentConfig.targetUrl!!)
        } else if (needLoadImageUrl.isNotEmpty()) {
            loadImageWithoutCache(needLoadImageUrl)
        }
    }

    private fun replaceImageUrl(url: String, forceLoadTarget: Boolean = false) {
        /**
         * forceLoadTarget 查看原图功能
         * 如果打开了自动加载原图  则隐藏查看原图
         * 如果关闭了自动加载原图:
         * 1. 需要用户点击按钮 才进行加载  forceLoadTarget 为true  强制加载目标图
         * 2. 默认进入的时候 判断是否有缓存  有的话直接加载 隐藏查看原图按钮
         */
        val onlyRetrieveFromCache: Boolean = if (forceLoadTarget) {
            !forceLoadTarget
        } else {
            !mViewModel.fragmentConfig.autoLoadTarget
        }
        mImageLoader?.loadImage(
            showView.hashCode(),
            Uri.parse(url),
            onlyRetrieveFromCache,
            object : com.kanzhun.imageviewer.loader.DefaultImageCallback() {
                override fun onStart() {
                    handleImageOnStart()
                }

                override fun onProgress(progress: Int) {
                    handleImageOnProgress(progress)
                }

                override fun onFail(error: Exception?) {
                    loadImageFail(onlyRetrieveFromCache)
                }

                override fun onSuccess(image: File) {
                    mainHandler.post {
                        if (isDetached || context == null) {
                            return@post
                        }
                        handleImageOnSuccess(image)
                    }
                }
            })
    }

    /**
     *  如果图片还未加载出来  则加载图片  最后通知修改宽高
     */
    private fun loadImageWithoutCache(url: String, needHandleTarget: Boolean = false) {
        mImageLoader?.loadImage(
            showView.hashCode(),
            Uri.parse(url),
            false,
            object : com.kanzhun.imageviewer.loader.DefaultImageCallback() {
                override fun onStart() {
                    handleImageOnStart()
                }

                override fun onProgress(progress: Int) {
                    handleImageOnProgress(progress)
                }

                override fun onFail(error: Exception?) {
                    loadImageFail(false)
                }

                override fun onSuccess(image: File) {
                    mainHandler.post {
                        if (isDetached || context == null) {
                            return@post
                        }
                        handleImageOnSuccess(image)
                        val realSizes = getRealSizeFromFile(image)
                        mBinding.imageView.resetSize(realSizes[0], realSizes[1])
                        if (needHandleTarget) {
                            replaceImageUrl(mViewModel.fragmentConfig.targetUrl!!)
                        }
                    }
                }
            })
    }

    private fun handleImageOnStart() {
        mainHandler.post {
            if (isDetached || context == null) {
                return@post
            }
            if (mBinding.loadingLayout.visibility == View.GONE) {
                mBinding.loadingLayout.visibility = View.VISIBLE
            }
            iProgress?.onStart(mViewModel.fragmentConfig.position)
        }
    }

    private fun handleImageOnProgress(progress: Int) {
        mainHandler.post {
            if (isDetached || context == null) {
                return@post
            }
            if (mBinding.loadingLayout.visibility == View.GONE) {
                mBinding.loadingLayout.visibility = View.VISIBLE
            }
            iProgress?.onProgress(mViewModel.fragmentConfig.position, progress)
        }
    }

    private fun handleImageOnSuccess(image: File) {
        if (mBinding.loadingLayout.visibility == View.VISIBLE) {
            mBinding.loadingLayout.visibility = View.GONE
        }
        fragmentCoverLoader?.imageCacheHandle(isCache = true, hasTargetUrl = true)
        mViewLoadFactory?.loadSillContent(showView!!, Uri.fromFile(image))
    }

    override fun loadTargetUrl() {
        if (mViewModel.fragmentConfig.targetUrl == null) {
            fragmentCoverLoader?.imageCacheHandle(isCache = false, hasTargetUrl = false)
        } else {
            replaceImageUrl(mViewModel.fragmentConfig.targetUrl!!, true)
        }
    }

    private fun loadImageFail(onlyRetrieveFromCache: Boolean) {
        if (!onlyRetrieveFromCache) {
            val errorDrawableResId =
                if (mViewModel.fragmentConfig.errorDrawableResId != 0) mViewModel.fragmentConfig.errorDrawableResId else ImageViewer.config()
                    .errorDrawableResId()
            if (errorDrawableResId != 0) {
                mViewLoadFactory?.loadContentFail(showView!!, errorDrawableResId)
            }
        }
        mainHandler.post {
            if (isDetached || context == null) {
                return@post
            }
            if (mBinding.loadingLayout.visibility == View.GONE) {
                mBinding.loadingLayout.visibility = View.VISIBLE
            }
            iProgress?.onFailed(mViewModel.fragmentConfig.position)
            fragmentCoverLoader?.imageCacheHandle(isCache = false, hasTargetUrl = true)
        }
    }

    private fun getRealSizeFromFile(image: File): Array<Int> {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(image.absolutePath, options)
        val arr = BitmapUtil.getAdjustSize(image.path, options)
        var w = arr[0]
        var h = arr[1]
        val isLongImage = contentLoader?.isLongImage(w, h)
        if (isLongImage != null && isLongImage) {
            w = com.kanzhun.imageviewer.tools.ScreenUtils.getScreenWidth(context)
            h = com.kanzhun.imageviewer.tools.ScreenUtils.getScreenHeight(context)
        }
        return arrayOf(w, h)
    }


    override fun providerContext(): Fragment {
        return this
    }

    override fun backToMin() {
        if (isDetached || isRemoving || activity == null) {
            return
        }
        binding?.imageView?.backToMin()
    }

    override fun onResume() {
        contentLoader?.pageChange(false)
        super.onResume()
    }

    override fun onPause() {
        contentLoader?.pageChange(true)
        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mImageLoader?.cancel(showView.hashCode())
    }

    override fun getStateLayout() = null

    override fun onMojitoViewFinish() {
        ImageViewerActivity.onListener?.onMojitoViewFinish(mViewModel.fragmentConfig.position)
        if (context is ImageViewerActivity) {
            (context as ImageViewerActivity).finishView()
        }
    }

    override fun onDrag(view: ImageViewerView, moveX: Float, moveY: Float) {
        ImageViewerActivity.iIndicator?.move(moveX, moveY)
        ImageViewerActivity.activityCoverLoader?.move(moveX, moveY)
        fragmentCoverLoader?.move(moveX, moveY)
        ImageViewerActivity.onListener?.onDrag(view, moveX, moveY)
    }

    override fun onRelease(isToMax: Boolean, isToMin: Boolean) {
        ImageViewerActivity.iIndicator?.fingerRelease(isToMax, isToMin)
        fragmentCoverLoader?.fingerRelease(isToMax, isToMin)
        ImageViewerActivity.activityCoverLoader?.fingerRelease(isToMax, isToMin)
    }

    override fun showFinish(view: ImageViewerView, showImmediately: Boolean) {
        ImageViewerActivity.onListener?.onShowFinish(view, showImmediately)
        if (!showImmediately) {
            ImageViewerActivity.hasShowedAnimMap[mViewModel.fragmentConfig.position] = true
        }
    }

    override fun onLongImageMove(ratio: Float) {
        ImageViewerActivity.onListener?.onLongImageMove(ratio)
    }

    override fun onLock(isLock: Boolean) {
        if (context is ImageViewerActivity) {
            (context as ImageViewerActivity).setViewPagerLock(isLock)
        }
    }
}