package com.kanzhun.marry.parent.point

class ParentPointAction {
    companion object{
        const val PARENT_FIRSTCOMPLETE_PAGE_EXPO = "parent-firstcomplete-page-expo"
        const val PARENT_FIRSTCOMPLETE_PAGE_NEXTCLICK = "parent-firstcomplete-page-nextclick"
        const val PARENT_FIRSTCOMPLETE_PAGE_SKIP = "parent-firstcomplete-page-skip"
        const val PARENT_F4_BINDING_CLICK = "parent-F4-binding-click"
        const val PARENT_F4_BOUND_CHILD_CLICK = "parent-F4-bound-child-click"
        const val F4_DYNAMIC_CLICK = "F4-dynamic-click"
        const val PARENT_F4_PAGE_EXPO = "parent-F4-page-expo"
    }
}