package com.kanzhun.marry.parent.bean

import com.kanzhun.foundation.kotlin.ktx.toGenderString1

/**
 * 被转发孩子与其触达关系状态 1 看了对方 2 发送了喜欢 3 收到了喜欢 4 好友
 */
fun ChildUser.getShareProgressString():String{
    return when(childRelationStatus){
        1 -> "您的孩子已经看了${gender.toGenderString1()}的信息"
        2 -> "您的孩子已经向${gender.toGenderString1()}发送了好感"
        3 -> "${gender.toGenderString1()}向您的孩子发送了好感"
        4 -> "您的孩子已经和${gender.toGenderString1()}开始聊天"
        else -> ""
    }
}