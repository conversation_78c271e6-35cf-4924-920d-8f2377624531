package com.kanzhun.marry.parent.module.me.activity.preview.item

import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.views.VideoOrImageView
import com.kanzhun.imageviewer.ext.ImageViewerData
import com.kanzhun.imageviewer.ext.storyViewer
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.ParentPreviewUserItem
import com.kanzhun.marry.parent.databinding.ParentPreviewUserPicturesSectionBinding
import com.kanzhun.marry.parent.point.ParentPointReporter

/**
 * 父母查看个人信息：图片
 */
class ParentPreviewUserPicturesItemProvider : BaseItemProvider<ParentPreviewUserItem, ParentPreviewUserPicturesSectionBinding>() {

    override fun onBindItem(binding: ParentPreviewUserPicturesSectionBinding, item: ParentPreviewUserItem) {
        val size = item.userInfo.storyList?.size ?: 0
        binding.tvPictureInformation.text = R.string.parent_his_or_her_pictures.toResourceString()

    }

    fun play() {
    }

    fun stop() {

    }

    fun release(){
    }

    private fun List<ProfileInfoModel.Story>.toImageViewList(viewList: List<VideoOrImageView>): List<ImageViewerData> {
        return mapIndexed { index, it ->
            if (it.video.isNullOrBlank()) {
                ImageViewerData(it.tinyPhoto, it.photo, viewList[index], false, it.text)
            } else {
                ImageViewerData(it.tinyPhoto, StringUtil.getPlayVideoUrl(it.video), viewList[index], true, it.text)
            }
        }
    }
}