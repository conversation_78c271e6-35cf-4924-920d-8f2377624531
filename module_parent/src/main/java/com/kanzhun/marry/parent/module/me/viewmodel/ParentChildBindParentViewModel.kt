package com.kanzhun.marry.parent.module.me.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.parent.api.APIParent
import com.kanzhun.marry.parent.bean.ChildBindInfoResp

class ParentChildBindParentViewModel:BaseViewModel() {

    var bindInfo: MutableLiveData<ChildBindInfoResp?> = MutableLiveData()

    var hasInviteCard:Boolean = false


    fun loadData(){
        val baseResponseObservable = RetrofitManager.getInstance().createApi<APIParent>(APIParent::class.java).childBindParentInfo()
        HttpExecutor.execute<ChildBindInfoResp>(baseResponseObservable, object : BaseRequestCallback<ChildBindInfoResp?>(true) {
            override fun onSuccess(data: ChildBindInfoResp?) {
                if(data == null || data.parentList.isNullOrEmpty()){
                    showEmpty()
                    return
                }else{
                    bindInfo.value = data
                    showContent()
                    data.parentList.forEach {
                        if(!it.inviteId.isNullOrBlank()){
                            hasInviteCard = true
                            return@forEach
                        }
                    }
                }

            }

            override fun dealFail(reason: ErrorReason) {
                showError()
            }


            override fun onComplete() {}
        })
    }
}