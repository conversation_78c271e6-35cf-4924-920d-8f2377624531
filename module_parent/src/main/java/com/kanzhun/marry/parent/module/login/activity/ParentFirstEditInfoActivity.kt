package com.kanzhun.marry.parent.module.login.activity

import android.content.Intent
import androidx.navigation.NavController
import androidx.navigation.NavGraph
import androidx.navigation.findNavController
import com.sankuai.waimai.router.annotation.RouterUri
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.databinding.ActivityParentFirstEditInfoBinding
import com.kanzhun.marry.parent.module.login.viewmodel.ParentFirstEditInfoViewModel

@RouterUri(path = [LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY])
class ParentFirstEditInfoActivity :
    BaseBindingActivity<ActivityParentFirstEditInfoBinding, ParentFirstEditInfoViewModel>() {
    override fun preInit(intent: Intent) {
        mViewModel.type = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0)
        when (mViewModel.type) {
            1 -> {
                val navController: NavController = findNavController(R.id.fragment)
                val navGraph: NavGraph =
                    navController.navInflater.inflate(R.navigation.parent_first_edit_info_navigation)
                navGraph.setStartDestination(R.id.parentSelectSexFragment)
                navController.graph = navGraph

            }

            2 -> {
                val navController: NavController = findNavController(R.id.fragment)
                val navGraph: NavGraph =
                    navController.navInflater.inflate(R.navigation.parent_first_edit_info_navigation)
                navGraph.setStartDestination(R.id.yearFragment)
                navController.graph = navGraph

            }

            3 -> {
                val navController: NavController = findNavController(R.id.fragment)
                val navGraph: NavGraph =
                    navController.navInflater.inflate(R.navigation.parent_first_edit_info_navigation)
                navGraph.setStartDestination(R.id.livePlaceFragment)
                navController.graph = navGraph

                //            val array = intent.getIntegerArrayListExtra(BundleConstants.BUNDLE_DATA_INT_ARRAY)
                //            array?.forEach {
                //                mViewModel.array.add(it)
                //            }

            }

            else -> {
                mViewModel.getData()
            }
        }
    }

    override fun initView() {
    }

    override fun initData() {
        mViewModel.activateSuccessLivaData.observe(this) {
            if (it == -1) {
                gotoSkip()
            } else if (it == -2) {
//                LoginPageRouter.jumpToSkipActivity(this, true)
                AppUtil.finishActivityDelay(this)
            }
        }
    }

    private fun gotoSkip() {
        LoginPageRouter.jumpToSkipActivity(this, false)
        AppUtil.finishActivityDelay(this)

    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null

}