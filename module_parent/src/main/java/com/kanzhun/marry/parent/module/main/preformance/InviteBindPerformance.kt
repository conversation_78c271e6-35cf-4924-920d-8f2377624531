package com.kanzhun.marry.parent.module.main.preformance

import android.content.Context
import android.text.method.LinkMovementMethod
import android.view.Gravity
import androidx.core.text.buildSpannedString
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.dialog.canShowDialog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.phoneToParentName
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.common.util.toast.OToast
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.bean.AcceptBindResp
import com.kanzhun.foundation.api.bean.InviteBindBean
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.H5Model
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.databinding.ParentInviteBindDialogBinding
import com.kanzhun.marry.parent.point.ParentPointReporter
import com.kanzhun.utils.string.appendClickable
import com.techwolf.lib.tlog.TLog
import java.util.LinkedList

/**
 * 邀请绑定弹窗
 */
class InviteBindPerformance(val activity: FragmentActivity) : AbsPerformance() {

    private val mInviteList = LinkedList<InviteBindBean>()

    private val inviteSet = mutableSetOf<String>()

    private var mIsShowing: Boolean = false

    private val mViewModel: InviteBindViewModel by lazy {
        ViewModelProvider(activity)[InviteBindViewModel::class.java]
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        ServiceManager.getInstance().inviteBindService.getInviteBindList().observe(activity) {
            it?.run {
                //阻断消息
                ServiceManager.getInstance().inviteBindService.blockInviteEvent()
                this.result?.forEach { bean ->
                    if (!inviteSet.contains(bean.inviteId)) {
                        mInviteList.offer(bean)
                        inviteSet.add(bean.inviteId ?: "")
                    }
                }
                TLog.info("InviteBindPerformance", "mInviteList size = ${mInviteList.size}")
                showDialog()
            }
        }
        mViewModel.inviteResult.observe(activity) { success ->
            if (success) {
                OToast.show(activity,
                    R.string.parent_bind_success.toResourceString(),
                    R.drawable.common_icon_toast_success)
            }
        }
    }

    private fun showDialog() {
        if (mIsShowing) {
            return
        }
        if (mInviteList.isEmpty()) {
            return
        }
        mIsShowing = true
        mInviteList.poll()?.run {
            val context = BaseApplication.getApplication().topContext
            if(context is FragmentActivity){//在顶部activity弹出
                context.canShowDialog {
                    context.showBindDialog(this)
                }
            }
            TLog.info("InviteBindPerformance", "mInviteList 1 size = ${mInviteList.size}")

        }

    }

    private fun Context.showBindDialog(bean: InviteBindBean) {
        ParentPointReporter.reportInvitingReceiveExpose(bean.inviterId)
        CommonViewBindingDialog(this,
            mCancelable = false,
            mCanceledOnTouchOutside = false,
            mGravity = Gravity.CENTER, //这里可以设置对话框显示的位置
            mPaddingLeft = 32,
            mPaddingRight = 32,
            onInflateCallback = { inflater, dialog ->
                val binding = ParentInviteBindDialogBinding.inflate(inflater)
                binding.apply {
                    tvDesc.movementMethod = LinkMovementMethod.getInstance()
                    if(AccountHelper.getInstance().isParent){
                        tvUserName.text = bean.phone
                        ivAvatar.loadRoundUrl(bean.tinyAvatar, 40.dpI)
                        tvDesc.text =getProtocol("绑定后将用孩子资料作为展示，点击接受即表示您同意",this@showBindDialog)
                    }else{
                        tvUserName.text = bean.phone.phoneToParentName()
                        ivAvatar.loadResource(R.mipmap.parent_icon_invite_bind)
                        tvDesc.text = getProtocol("请求与您绑定亲属账号，点击接受即表示您同意",this@showBindDialog)
                    }
                    tvAccept.clickWithTrigger {
                        dialog.dismiss()
                        mIsShowing = false
                        mViewModel.acceptInviteBind(bean.inviteId)
                        showDialog()
                    }
                    tvRefuse.clickWithTrigger {
                        dialog.dismiss()
                        mIsShowing = false
                        mViewModel.refuseInviteBind(bean.inviteId)
                        showDialog()
                    }
                }
                binding
            }).show()
    }

    private fun getProtocol(desc:String,context: Context):CharSequence {
        return buildSpannedString {
            append(desc)
            appendClickable("账号绑定协议", R.color.common_color_0046BD.toResourceColor(), false) {
                H5Model.URL_BIND_PROTOCOL.openWithFullScreen(context)

            }
        }
    }


}

class InviteBindViewModel : BaseViewModel() {

    var inviteResult: MutableLiveData<Boolean> = MutableLiveData()


    fun acceptInviteBind(inviteId: String?) {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).acceptInviteBind(inviteId)
        HttpExecutor.execute<AcceptBindResp>(observable, object : BaseRequestCallback<AcceptBindResp?>(true) {
            override fun onSuccess(data: AcceptBindResp?) {
                inviteResult.postValue(true)
                LiveEventBus.get<Int>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_SUCCESS).post(data?.bindNum?:0)
            }

            override fun dealFail(reason: ErrorReason?) {

            }
        })
    }

    fun refuseInviteBind(inviteId: String?) {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).refuseInviteBind(inviteId)
        HttpExecutor.execute<Any>(observable, object : BaseRequestCallback<Any?>(true) {
            override fun onSuccess(data: Any?) {

            }

            override fun dealFail(reason: ErrorReason?) {

            }
        })
    }
}