package com.kanzhun.marry.parent.module.main.preformance

import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.common.kotlin.ext.getSimpleCurrentDate
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.kotlin.ktx.saveString
import com.kanzhun.foundation.kotlin.ktx.userSp
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.ParentHomeDividerCard
import com.kanzhun.marry.parent.databinding.ParentFragmentHomeBinding
import com.kanzhun.marry.parent.module.main.home.ParentHomeListCardType


const val PARENT_HOME_TOP_HINT_DATE = "parent_home_top_hint_date"
class ParentHomeTopHintPerformance(val mBinding: ParentFragmentHomeBinding) : AbsPerformance() {

    //明日预告的标题位置
    private var tomorrowTitlePosition: Int = -1

    private var canShow: Boolean = false

    private var isShowing = false


    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        //今天是否点击过
        canShow =  userSp().getString(PARENT_HOME_TOP_HINT_DATE,"") != getSimpleCurrentDate()
        mBinding.recyclerview.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    checkItemPositionAndShow()
                }
            }
        })
        mBinding.ivTomorrowHint.clickWithTrigger {
            hideTopHint()
        }
    }

    fun findTargetPosition(list: List<BaseListItem>) {
        tomorrowTitlePosition = -1
        if(!canShow || isShowing){
            return
        }
        for (item in list) {
            if (item.getLocalItemType() == ParentHomeListCardType.TOMORROW_TITLE.value) {
                tomorrowTitlePosition = list.indexOf(item)
                if(item is ParentHomeDividerCard){
                   mBinding.tvTomorrowHint.text = R.string.parent_tomorrow_recommend_top_hint.toResourceString(item.extraInfo?:"8:00")
                }
                break
            }
        }
        //如果在前两条，说明不用滚动就曝光了
        if(tomorrowTitlePosition in 0..1){
            checkItemPositionAndShow()
        }
    }

    private fun checkItemPositionAndShow() {
        if (tomorrowTitlePosition > 0 && canShow) {
            val layoutManager = mBinding.recyclerview.layoutManager as LinearLayoutManager
            val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
            val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
            //图片是否在可见范围内
            if (tomorrowTitlePosition in firstVisibleItemPosition..lastVisibleItemPosition) {
                showTopHint()
            }

        }
    }

    private fun showTopHint() {
        mBinding.llTomorrowHint.visible()
        isShowing = true
    }

    fun hideTopHint() {
        if(isShowing && canShow){
            mBinding.llTomorrowHint.gone()
            isShowing = false
            canShow = false
            userSp().saveString(PARENT_HOME_TOP_HINT_DATE,getSimpleCurrentDate())
        }

    }

}