package com.kanzhun.marry.parent.module.me

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.kanzhun.common.kotlin.ui.onClick
import com.sankuai.waimai.router.annotation.RouterUri
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.common.util.ActivityAnimType
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.marry.parent.databinding.ActivityParentSeeChildInfoBinding
import com.kanzhun.marry.parent.module.me.viewmodel.ParentInviteOtherBindViewModel

@RouterUri(path = [LoginPageRouter.LOGIN_PARENT_SEE_CHILD_INFO_ACTIVITY])
class ParentSeeChildInfoActivity :
    BaseBindingActivity<ActivityParentSeeChildInfoBinding, ParentInviteOtherBindViewModel>() {
    override fun preInit(intent: Intent) {
    }

    override fun initView() {
        mBinding.idTitle.asBackButton()
        mBinding.idTitle.setTitle("孩子资料")
        mBinding.apply {
            idSex.onClick {
                val bundle = Bundle()
                bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 1)
                AppUtil.startUriForResult(
                    this@ParentSeeChildInfoActivity,
                    LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY,
                    RequestCodeConstants.REQUEST_CODE_PARENT_SEX,
                    bundle,
                    ActivityAnimType.DEFAULT
                )
            }
            idBrithday.onClick {
                val bundle = Bundle()
                bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 2)
                AppUtil.startUriForResult(
                    this@ParentSeeChildInfoActivity,
                    LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY,
                    RequestCodeConstants.REQUEST_CODE_PARENT_BIRTHDAY,
                    bundle,
                    ActivityAnimType.DEFAULT
                )
            }
            idLivePlace.onClick {
                val bundle = Bundle()
                bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 3)
//                val arrayList = ArrayList<Int>()
//                if (bean != null && bean.size > 0) {
//                    for (i in bean.indices) {
//                        arrayList.add(bean.get(i).tagId)
//                    }
//                }
//                bundle.putIntegerArrayList(BundleConstants.BUNDLE_DATA_INT_ARRAY, arrayList)
                AppUtil.startUriForResult(
                    this@ParentSeeChildInfoActivity,
                    LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY,
                    RequestCodeConstants.REQUEST_CODE_PARENT_LIVE_PLACE,
                    bundle,
                    ActivityAnimType.DEFAULT
                )
            }
        }
        updateInfo()

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK ){
            when(requestCode){
                RequestCodeConstants.REQUEST_CODE_PARENT_SEX,  RequestCodeConstants.REQUEST_CODE_PARENT_BIRTHDAY,RequestCodeConstants.REQUEST_CODE_PARENT_LIVE_PLACE ->{
                    updateInfo()
                }
            }
        }
    }

    private fun updateInfo() {
        val user =
            ServiceManager.getInstance().databaseService.userDao.getUser(AccountHelper.getInstance().userId)
        //性别
        mBinding.tvHometown.text = if (user.childInfoGender == 1) "男" else "女"
        //出生年份
        mBinding.tvSmoke.text = user.childInfoBirthdayStr
        //性别
        mBinding.tvDrink.text = user.childInfoAddress
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null

    companion object {
        @JvmStatic
        fun createIntent(context: Context): Intent {
            val intent = Intent(context, ParentSeeChildInfoActivity::class.java)
            return intent
        }
    }
}