<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:visibility="invisible"
    tools:visibility="visible"
    android:layout_height="wrap_content">

    <com.coorchice.library.SuperTextView
        android:id="@+id/tvWeChatShare"
        android:layout_width="0dp"
        android:layout_height="46dp"
        android:gravity="center"
        android:paddingVertical="10dp"
        app:layout_constraintEnd_toStartOf="@id/tvAppShare"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="10dp"
        app:stv_corner="28dp"
        app:stv_stroke_color="@color/common_color_292929"
        app:stv_stroke_width="1dp" />

    <TextView
        android:id="@+id/tvWeChatShareText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/common_icon_wechat_share_button"
        android:gravity="center_vertical"
        android:text="@string/common_we_chat_share"
        android:drawablePadding="4dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintBottom_toBottomOf="@id/tvWeChatShare"
        app:layout_constraintEnd_toEndOf="@id/tvWeChatShare"
        app:layout_constraintStart_toStartOf="@id/tvWeChatShare"
        app:layout_constraintTop_toTopOf="@id/tvWeChatShare" />


    <com.coorchice.library.SuperTextView
        android:id="@+id/tvAppShare"
        android:layout_width="0dp"
        android:layout_height="46dp"

        android:gravity="center"
        android:text="@string/common_parent_share_to_child"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvWeChatShare"
        app:layout_constraintTop_toTopOf="parent"
        app:stv_corner="28dp"
        app:stv_solid="@color/common_color_292929" />
</androidx.constraintlayout.widget.ConstraintLayout>