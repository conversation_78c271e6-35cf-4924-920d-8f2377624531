<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_color_F5F5F5"
    android:orientation="vertical">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.kanzhun.common.kotlin.ui.statelayout.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:empty_layout="@layout/parent_activity_child_bind_parents_empty">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="2"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/parent_bind_parent_item" />

            <com.coorchice.library.SuperTextView
                android:id="@+id/tvAddParent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="40dp"
                android:gravity="center"
                android:paddingVertical="12dp"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recyclerview"
                app:stv_corner="27dp"
                app:stv_solid="@color/common_color_292929"
                tools:text="还可绑定1位家长" />

            <include
                android:id="@+id/tvBottomAccountHelp"
                layout="@layout/parent_account_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kanzhun.common.kotlin.ui.statelayout.StateLayout>
</LinearLayout>