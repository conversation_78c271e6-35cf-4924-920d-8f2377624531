<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUILinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idTopBottom"
    app:qmui_backgroundColor="@color/common_white"
    app:qmui_radius="12dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="8dp"
    android:layout_marginRight="12dp"
    android:background="@color/common_white"
    android:orientation="vertical"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idChildInfo"
        style="@style/Common_BaseInfoItem"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_living_place">

        <TextView
            android:id="@+id/tv_hometown_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_child_info_edit"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            android:text="孩子资料"
            android:textColor="@color/common_color_191919"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/tv_hometown"
            app:layout_constraintBottom_toBottomOf="@+id/tv_hometown_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_hometown_title"
            app:layout_constraintRight_toLeftOf="@+id/iv_arrow_hometown"
            app:layout_constraintTop_toTopOf="@+id/tv_hometown_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="4dp"
            android:gravity="right|center_vertical"
            android:singleLine="true"
            android:textColor="@color/common_color_707070"
            android:textColorHint="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_14" />

        <ImageView
            android:id="@+id/iv_arrow_hometown"
            app:layout_constraintBottom_toBottomOf="@+id/tv_hometown_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_hometown"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_hometown_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/common_ic_gray_right_arrow" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idSetting"
        style="@style/Common_BaseInfoItem"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_occupation">

        <TextView
            android:id="@+id/tv_smoke_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/common_ic_icon_setting"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            android:text="设置"
            android:textColor="@color/common_color_191919"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/tv_smoke_hide"
            app:layout_constraintBottom_toBottomOf="@+id/tv_smoke_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_smoke_title"
            app:layout_constraintTop_toTopOf="@+id/tv_smoke_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="@dimen/common_text_sp_11"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_smoke"
            app:layout_constraintBottom_toBottomOf="@+id/tv_smoke_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_smoke_hide"
            app:layout_constraintRight_toLeftOf="@+id/iv_smoke"
            app:layout_constraintTop_toTopOf="@+id/tv_smoke_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="4dp"
            android:gravity="right|center_vertical"
            android:singleLine="true"
            android:textColor="@color/common_color_707070"
            android:textColorHint="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_14" />

        <ImageView
            android:id="@+id/iv_smoke"
            app:layout_constraintBottom_toBottomOf="@+id/tv_smoke_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_smoke"
            app:layout_constraintRight_toLeftOf="@+id/iv_arrow_smoke"
            app:layout_constraintTop_toTopOf="@+id/tv_smoke_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_arrow_smoke"
            app:layout_constraintBottom_toBottomOf="@+id/tv_smoke_title"
            app:layout_constraintLeft_toRightOf="@+id/iv_smoke"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_smoke_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/common_ic_gray_right_arrow" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</com.qmuiteam.qmui.layout.QMUILinearLayout>