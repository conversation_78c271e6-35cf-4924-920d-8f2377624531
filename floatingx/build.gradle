plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
//    id 'org.jetbrains.kotlin.android'
//    id 'com.vanniktech.maven.publish'
//    id 'org.jetbrains.dokka'
}

apply from: "$rootDir/gradle/common_library.gradle"

android {
    namespace 'com.petterp.floatingx'

    defaultConfig {
//        resourcePrefix 'share_'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-floatingx.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation deps.androidx.app_compat
//    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
//    implementation 'androidx.appcompat:appcompat:1.3.0'
//    dokkaPlugin 'org.jetbrains.dokka:android-documentation-plugin:1.9.20'
}