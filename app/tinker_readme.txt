tinker-support.gradle中的tinkerId需要随着版本变更（每次发正式版的时候需要变更）

正常打release包后，tinker相关的备份存储在build/outputs/bakApk，需要单独保存，后续打热修复包的时候需要



打热修复包：
1、修改代码
2、build/outputs/bakApk目录下防止备份包
3、修改tinker-support.gradle中的baseApkDir路径和tinkerId
4、执行buildTinkerPatchRelease task
5、生成的补丁文件存储在build/outputs/patch下
6、将patch_signed_7zip.apk上传到bugly平台



新版流程：
1、修复代码
2、build/outputs/bakApk目录下放入对应版本的备份包，当做基准包
3、修改tinker-support.gradle中的baseApkDir路径
4、先执行./gradlew clean，然后在执行./gradlew buildTinkerPatchProdRelease
5、生成的补丁文件存储在build/outputs/patch下，(不要用build/outputs/apk的包，里面没有YAPATCH.MF)


FAQ：
Q1、执行buildTinkerPatchProdRelease任务时，为什么要先clean，然后在-x clean
A：项目中使用了沪江的AspectJx插件，里面的AJXProcedure.groovy，为preBuild依赖了clean；而Tinker 自*********开始
会在plugin配置阶段 在Intermediates目录生成一份空的public.txt，并加入了AAPT2，如果不-x clean，会导致该文件被删，
导致编译失败。

2021.04.02 更新：我们基于hujiang aspectjx 2.0.0版本，进行了修改，加入一个自动clean开关，配置为autoClean false时，
禁用了preBuild依赖clean，可以不必在-x clean了


Q2、不需要再手动指定tinkerId了吗
A：如果在tinker-support.gradle脚本中设置了tinkerId自动生成，就不必在手动设定tinkerId了

Q3、为什么要用build/outputs/patch下文件，build/outputs/apk里面的不可以吗？
A：build/outputs/apk是最原始的补丁文件。为了简化接入和分发，我们参考Bugly的做法，封装了一些业务逻辑信息，在补丁包中
插入了一些文件，例如YAPATCH.MF，这样就可以直接放到APM平台上进行补丁下发了。

常见指令
./gradlew clean
打基准包
./gradlew assembleProdRelease  | tee log-14-13-assemble.txt
打补丁包
./gradlew buildTinkerPatchProdRelease | tee log-14-42-patch.txt

其中| tee 文件名，可以将日志同时输出到文件与控制台。
通过修改hujiang的aspectjx-2.0.0代码，在配置中 autoClean false时，禁用preBuild依赖clean的逻辑

模拟器验证
adb push 文件路径 /storage/emulated/0/Android/data/com.kanzhun.marry/patch_signed.apk
adb shell
su
am startservice -n com.kanzhun.marry/com.tencent.tinker.lib.service.TinkerPatchService -e patch_path_extra /storage/emulated/0/Android/data/com.kanzhun.marry/patch_signed.apk -e patch_result_class com.tencent.tinker.lib.service.DefaultTinkerResultService

以下是旧版使用方式，可以忽略，仅供存档使用
tinker-support.gradle中的tinkerId需要随着版本变更（每次发正式版的时候需要变更）

正常打release包后，tinker相关的备份存储在build/outputs/bakApk，需要单独保存，后续打热修复包的时候需要



打热修复包：
1、修改代码
2、build/outputs/bakApk目录下防止备份包
3、修改tinker-support.gradle中的baseApkDir路径和tinkerId
4、执行buildTinkerPatchRelease task
5、生成的补丁文件存储在build/outputs/patch下
6、将patch_signed_7zip.apk上传到bugly平台
