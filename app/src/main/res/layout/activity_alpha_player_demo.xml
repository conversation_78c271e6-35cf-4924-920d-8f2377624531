<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

<!--        <Button-->
<!--            android:id="@+id/btn_attach"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="初始化"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"/>-->

<!--        <Button-->
<!--            android:id="@+id/btn_start"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="开始"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"-->
<!--            app:layout_constraintRight_toRightOf="parent"/>-->

<!--        <Button-->
<!--            android:id="@+id/btn_detach"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="释放"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintRight_toRightOf="parent"/>-->

<!--        <TextView-->
<!--            android:id="@+id/title_tv"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="Hello AlphaPlayer!"-->
<!--            android:textSize="20dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"-->
<!--            app:layout_constraintRight_toRightOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <kanzhunhun.orange.alphaplayerdemo.VideoGiftView-->
<!--            android:id="@+id/video_view"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"/>-->

<!--        <ImageView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:layout_constraintStart_toStartOf="@+id/title_tv"-->
<!--            app:layout_constraintEnd_toEndOf="@+id/title_tv"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/title_tv"/>-->
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>