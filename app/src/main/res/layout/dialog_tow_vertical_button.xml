<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_positive"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bg_corner_12_color_white"
        android:gravity="center"
        android:textColor="@color/common_color_4C4C4C"
        android:textSize="@dimen/common_text_sp_16"
        tools:text="@string/me_restart_test" />

    <TextView
        android:id="@+id/tv_negative"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:layout_marginBottom="34dp"
        android:background="@drawable/bg_corner_12_color_white"
        android:gravity="center"
        android:textColor="@color/common_color_B2B2B2"
        android:textSize="@dimen/common_text_sp_16"
        tools:text="@string/common_cancel" />
</LinearLayout>