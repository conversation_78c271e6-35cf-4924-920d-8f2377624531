<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idStateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/tv_phone_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_all_page="@string/login_title_section"
            app:title_icon="@drawable/login_ic_icon_activate_birthday"
            app:title_now_page="3"
            app:title_text="@string/login_activate_birthday_title" />


        <com.kanzhun.common.views.wheel.pick.TimePickView
            android:id="@+id/time_pick_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/app_layout_page_left_padding"
            android:layout_marginEnd="@dimen/app_layout_page_right_padding"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_title" />

        <com.kanzhun.marry.me.views.LoginNextPageView
            android:id="@+id/btn_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/app_layout_page_right_padding"
            android:layout_marginBottom="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


