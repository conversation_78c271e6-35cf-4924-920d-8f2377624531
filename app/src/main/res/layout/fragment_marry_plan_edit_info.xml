<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idSelect"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/idTitleView"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/idTitle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/idTitleView"
        android:layout_marginHorizontal="24dp"
        android:textColor="@color/common_black"
        android:layout_marginTop="20dp"
        android:text="申请进入"
        android:textSize="18dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/idTitleSub"
        app:layout_constraintLeft_toRightOf="@+id/idTitle"
        app:layout_constraintBottom_toBottomOf="@+id/idTitle"
        android:textColor="@color/common_color_7F7F7F"
        android:layout_marginTop="20dp"
        android:text="（必填）"
        android:textSize="14dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="@dimen/app_layout_page_left_padding"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/idWarn"
        android:background="@color/common_color_F5F5F5"
        app:qmui_radius="12dp"
        android:padding="12dp">

        <TextView
            android:id="@+id/tv_content_total_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/common_color_CCCCCC"
            android:textSize="@dimen/common_text_sp_12"
            android:text="/500"/>

        <TextView
            android:id="@+id/idCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toLeftOf="@+id/tv_content_total_count"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textSize="@dimen/common_text_sp_12"
            />

        <EditText
            android:id="@+id/edit_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="start"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textColorHint="@color/common_color_CCCCCC"
            android:hint="若您提交了多次申请，我们将以最新申请为准审核"
            android:background="@null"
            app:layout_constraintBottom_toTopOf="@+id/tv_content_total_count"
            android:layout_marginBottom="12dp"
            tools:text="我是回答文案"/>
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <TextView
        android:id="@+id/idWarn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:drawableLeft="@drawable/me_ic_fail_small"
        app:layout_constraintEnd_toEndOf="parent"
        android:drawablePadding="6dp"
        app:layout_constraintTop_toBottomOf="@+id/idTitle"
        android:layout_marginStart="18dp"
        android:layout_marginEnd="18dp"
        android:textSize="@dimen/common_text_sp_12"
        android:textColor="@color/common_color_FF3F4B"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="个人简介内容敏感，请修改个人简介内容敏感，请修改个人简介内容敏感，请修改个人简介内容敏感，"/>


    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/idBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/button_large_next_page_style"
        android:gravity="center"
        android:textStyle="normal"
        android:text="提交"
        android:enabled="false"
        android:layout_marginBottom="20dp"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>