<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.login.viewmodel.LoginInviteCodeViewModel" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.login.viewmodel.LoginViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.login.callback.LoginInviteCodeCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/tv_phone_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_icon="@drawable/login_ic_icon_login_invite"
            app:title_bottom_padding="@dimen/app_layout_page_content_margin"
            app:title_text_sub="@string/login_input_invite_code_complete_desc"
            app:title_text="@string/login_input_invite_code" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_title">


            <EditText
                android:id="@+id/edit_text"
                android:layout_width="0dp"
                android:layout_height="33dp"
                android:background="@null"
                android:digits="@string/login_alphabet_and_number"
                android:gravity="center_vertical"
                android:hint="@string/login_input_invite_code"
                android:maxLength="8"
                android:singleLine="true"
                android:textColor="@color/common_color_191919"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textCursorDrawable="@drawable/common_color_black_text_cursor"
                android:textSize="@dimen/common_text_sp_24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <View
                android:id="@+id/view_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="12dp"
                android:background="@{activityViewModel.inviteErrorObservable ? @color/common_color_FF3F4B : @color/common_color_191919}"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/edit_text"
                tools:background="@color/common_black" />

            <TextView
                android:id="@+id/tv_error_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@={activityViewModel.inviteErrorDescObservable}"
                android:textColor="@color/common_color_FF3F4B"
                android:textSize="12sp"
                android:visibility="@{activityViewModel.inviteErrorObservable ? View.VISIBLE : View.GONE}"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view_line" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/btn_complete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginBottom="28dp"
            android:background="@drawable/common_bg_selector_btn_next"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>