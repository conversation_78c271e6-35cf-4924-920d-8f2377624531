package com.kanzhun.marry.wxapi

import android.content.Intent
import android.os.Bundle
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon.Companion.EVENT_KEY_WECHAT_LOGIN_SUCCESS
import com.kanzhun.common.kotlin.ext.sendObjectLiveEvent
import com.kanzhun.common.kotlin.ui.activity.BaseActivity
import com.kanzhun.foundation.constant.ShareKey
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.MainPageRouter
import com.kanzhun.foundation.utils.MyClipboardManager
import com.kanzhun.utils.L
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory


class WXEntryActivity : BaseActivity(), IWXAPIEventHandler {
    val tag = this::class.java.simpleName
    var api: IWXAPI? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        api = WXAPIFactory.createWXAPI(this, ShareKey.getWxAppId(), true)
        api?.handleIntent(intent, this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        api?.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq?) {
        if(AccountHelper.getInstance().isLogin){
            if(req is ShowMessageFromWX.Req){//微信内H5跳转应用
                val message = req.message.messageExt
                L.e("MyClipboardManager:", "WXEntryActivity1 readClipboard")
                MyClipboardManager.readClipboardAndClear(this)
//                ProtocolHelper.parseProtocol(message)
                MainPageRouter.jumpToMainActivity(this, message)
            }
        }
        finish()
    }

    override fun onResp(resp: BaseResp?) {
        when(resp?.type){
            ConstantsAPI.COMMAND_SENDAUTH->{//微信登录授权
                if (resp is SendAuth.Resp) {//授权登录成功
                    if (resp.errCode == BaseResp.ErrCode.ERR_OK) {
                        sendObjectLiveEvent(EVENT_KEY_WECHAT_LOGIN_SUCCESS, resp)
                    }

                }
            }
            ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM->{//小程序打开客户端
                if(AccountHelper.getInstance().isLogin) {
                    val launchMiniProResp = resp as WXLaunchMiniProgram.Resp
                    val extraData = launchMiniProResp.extMsg
                    L.e("MyClipboardManager:", "WXEntryActivity2 readClipboard")
                    MyClipboardManager.readClipboardAndClear(this)
//                    ProtocolHelper.parseProtocol(extraData)
                    MainPageRouter.jumpToMainActivity(this, extraData)
                }
            }
        }
        finish()
    }
}