package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.router.MePageRouter

/**
 * 喜欢我的页面
 */
class AuthCenterProtocolAction:IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val from = params[ProtocolHelper.SOURCE]
       MePageRouter.jumpToMeAuthActivity(context,PageSource.PROTOCOL,from?:"")
    }
}