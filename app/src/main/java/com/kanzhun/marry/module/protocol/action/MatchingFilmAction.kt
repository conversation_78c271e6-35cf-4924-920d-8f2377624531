package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.ActivityPageRouter
import com.kanzhun.utils.T

/**
 * 冲洗房
 */
class MatchingFilmAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        if (AccountHelper.getInstance().isCanMatch) {
            ActivityPageRouter.jumpFilmFlushActivity(context)
        } else {
            T.ss("请先完成新手任务才能参与活动哦～")
        }
    }
}