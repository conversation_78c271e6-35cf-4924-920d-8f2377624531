package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.router.MePageRouter.jumpToUpdateIntroActivity

//关于我编辑页
class MeInfoAboutMeAction:IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val from = params[ProtocolHelper.SOURCE]
        val content = params[ProtocolHelper.CONTENT]
        val rejectReason = params[ProtocolHelper.REJECT_REASON]
        val certStatus = params[ProtocolHelper.CERT_STATUS]
        jumpToUpdateIntroActivity(context,content = content,
            certInfo = rejectReason, pageSource = PageSource.PROTOCOL,
            protocolFrom = from?:"", type = 0, certStatus = certStatus)
    }
}