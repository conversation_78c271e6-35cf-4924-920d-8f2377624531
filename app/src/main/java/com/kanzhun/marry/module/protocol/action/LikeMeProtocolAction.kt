package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.kotlin.constant.HomeTabType
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.sendObjectLiveEvent

/**
 * 喜欢我的页面
 */
class LikeMeProtocolAction:IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        sendObjectLiveEvent(LivedataKeyCommon.EVENT_KEY_MAIN_TAB, HomeTabType.LIKE_ME)
    }
}