package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.utils.base.LText

/**
 * 点赞列表
 */
class LikeListAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        ChatPageRouterKT.jumpToLikeListActivity(context)

    }
}