package com.kanzhun.marry;

import static com.kanzhun.foundation.watermark.WatermarkKt.registerWatermarkLifecycle;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.ForegroundListener;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.apm.PatchHelper;
import com.kanzhun.foundation.kotlin.ui.SmartRefreshLayoutConfig;
import com.kanzhun.foundation.logic.service.AppLifecycleService;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.MyClipboardManager;
import com.kanzhun.foundation.utils.NotifyUtil;
import com.kanzhun.foundation.utils.ProcessHelper;
import com.kanzhun.localNotify.SwipeNotificationManager;
import com.kanzhun.marry.main.MainActivity;
import com.kanzhun.marry.push.PushSdkManager;
import com.kanzhun.marry.start.StartPoint;
import com.kanzhun.utils.L;
import com.kanzhun.utils.platform.Utils;
import com.kanzhun.utils.process.ProcessUtil;
import com.kit.Kit;
import com.techwolf.lib.tlog.TLog;
import com.twl.startup.Startup;

import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.plugins.RxJavaPlugins;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/1/29
 */
public class App extends BaseApplication implements ForegroundListener {

    static {
        //设置全局的Header构建器
        SmartRefreshLayoutConfig.Companion.init();
    }

    public static final String PRIVACY_VERSION_KEY = "privacy_version_key";
    /**
     * 是否延迟初始化
     */
    private boolean mDelay;
    private static App mOApp;

    @Override
    public void onCreate() {
        super.onCreate();
        mOApp = this;
        Utils.init(this);
        mDelay = SpManager.get().global().getInt(PRIVACY_VERSION_KEY, 0) < 1;
        Startup.onAppCreate(!mDelay);
        mActivityLifecycleCallbacks.addForegroundListener(this);
        setRxJavaErrorHandler();

        if (isDebug()) {
            if (ProcessUtil.isMainProcess(this)) {
                Kit.getInstance().install(this);
            }
        }

        // ******* 客户端增加反诉码水印：https://zhishu.zhipin.com/wiki/gBfaEoZJDq9
        registerWatermarkLifecycle(this);

        LiveEventBus
                .config()
                .lifecycleObserverAlwaysActive(true);
    }

    private void setRxJavaErrorHandler() {
        RxJavaPlugins.setErrorHandler(new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Throwable {
                throwable.printStackTrace();
                TLog.error("RxJavaPlugins", "App setRxJavaErrorHandler = " + throwable.getMessage());
            }
        });
    }

    public boolean isDelay() {
        return mDelay;
    }

    public void resetDelay() {
        mDelay = false;
        SpManager.putGlobalInt(PRIVACY_VERSION_KEY, 1);
    }

    public static App getApp() {
        return mOApp;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        ProcessHelper.setContext(this, getPackageName());
        PatchHelper.init(base);
        OStartup.getInstance().init(this);
    }

    @Override
    public boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    /**
     * 获取application中指定的meta-data
     *
     * @return 如果没有获取成功(没有对应值 ， 或者异常)，则返回值为空
     */
    public static String getAppMetaData(Context ctx, String key) {
        if (ctx == null || TextUtils.isEmpty(key)) {
            return null;
        }
        String resultData = null;
        try {
            PackageManager packageManager = ctx.getPackageManager();
            if (packageManager != null) {
                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(ctx.getPackageName(), PackageManager.GET_META_DATA);
                if (applicationInfo != null) {
                    if (applicationInfo.metaData != null) {
                        resultData = applicationInfo.metaData.getString(key);
                    }
                }

            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return resultData;
    }

    @Override
    public Class getMainTabClass() {
        return MainActivity.class;
    }

    @Override
    public void onForeground() {
        NotifyUtil.clearNotifications();
        PushSdkManager.getInstance().setForeground(true);
        AppLifecycleService.setForeground(true);
        if (isLaunch) {
            L.e("MyClipboardManager:", "onForeground readClipboard");
            MyClipboardManager.Companion.readClipboard(mOApp, false, 1000);
            new StartPoint().point(getContext());
            SwipeNotificationManager.getInstance(this).reStart();
        }
        isLaunch = true;
    }

    private static boolean isLaunch = false;

    @Override
    public void onBackground() {
        PushSdkManager.getInstance().setForeground(false);
        AppLifecycleService.setForeground(false);
        if (isLaunch) {
            SwipeNotificationManager.getInstance(this).stop();
        }
    }
}
