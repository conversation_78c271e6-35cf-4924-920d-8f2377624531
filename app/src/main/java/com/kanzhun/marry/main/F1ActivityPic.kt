package com.kanzhun.marry.main

import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kotlin.ktx.globalSp
import com.kanzhun.foundation.logic.service.SecurityNoticeManager
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.R
import com.kanzhun.marry.matching.api.MatchingApi
import com.kanzhun.marry.matching.bean.UserActivityAlbumResponse
import com.kanzhun.marry.matching.fragment.home.performance.SP_KEY_HOME_USER_COVER_GUIDE
import com.techwolf.lib.tlog.TLog

const val SP_KEY_F1_ActivityPic_DIALOG_2024 = "SP_KEY_F1_ActivityPic_DIALOG_2024"

class F1ActivityPic(activity: AppCompatActivity) : AbsF1DialogHandler(activity) {
    var composeDialogFragment: ComposeDialogFragment? = null
    override fun handler() {
        if (SecurityNoticeManager.isBlock) return
        getData()
    }

    override fun tabChange(index: Int) {

    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        TLog.print("F1Dialog", "onStop")
        if (composeDialogFragment?.isShowing() == true) {
            composeDialogFragment?.dismissAllowingStateLoss()
        }
    }

    fun getData() {
        if (globalSp().getBoolean(SP_KEY_HOME_USER_COVER_GUIDE, true)) {
            //没有谈过蒙层 不出下面弹窗
            return
        }
        TLog.print("F1Dialog", "F1ActivityPic getData")
        if (indexLocal == 0 && !SpManager.get().user()
                .getBoolean(SP_KEY_F1_ActivityPic_DIALOG_2024, false)
        ) {

            HttpExecutor.execute(
                RetrofitManager.getInstance().createApi(MatchingApi::class.java)
                    .requestUserActivityAlbumAlert(),
                object : BaseRequestCallback<UserActivityAlbumResponse?>(false) {
                    override fun onSuccess(data: UserActivityAlbumResponse?) {
                        if((data?.photoList?.size ?: 0) == 0){
                            doNext()
                            return
                        }

                        while(data!!.photoList.size < 9){
                            data.photoList.addAll(data.photoList)
                        }
                        TLog.print("F1Dialog", "F1ActivityPic:" + composeDialogFragment)
                        TLog.print(
                            "F1Dialog",
                            "F1ActivityPic:" + composeDialogFragment?.isShowing()
                        )
                        if (composeDialogFragment?.isShowing() == true) {
                            composeDialogFragment?.dismissAllowingStateLoss()
                        }
                        reportPoint("activities-album-F1popup-expo")
                        composeDialogFragment = ComposeDialogFragment.shouldShow(
                            activity,
                            R.color.common_color_191919_90
                        ) {
                            val list:List<String> = data.photoList.map { it.photoUrl?:"" }
                            F1F1ActivityPicDialog(list,onClick = {
                                parseProtocol(data.jumpUrl)
                                composeDialogFragment?.dismissAllowingStateLoss()
                                SpManager.putUserBoolean(
                                    SP_KEY_F1_ActivityPic_DIALOG_2024,
                                    true
                                )
                                reportPoint("activities-album-F1popup-click"){
                                    type = "去看看"
                                }
                            },
                                onDismissed = {
                                    composeDialogFragment?.dismissAllowingStateLoss()
                                    SpManager.putUserBoolean(
                                        SP_KEY_F1_ActivityPic_DIALOG_2024,
                                        true
                                    )
                                    reportPoint("activities-album-F1popup-click"){
                                        type = "关闭弹窗"
                                    }
                                })
                        }
                    }

                    override fun dealFail(reason: ErrorReason?) {
                        doNext()
                    }
                }
            )
        } else {
            TLog.print("F1Dialog", "F1ActivityPic doNext")
            doNext()
        }
    }


}

@Composable
fun F1F1ActivityPicDialog(photoList: List<String>, onClick: () -> Unit = {}, onDismissed: () -> Unit = {}) {

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {

        ConstraintLayout(
            modifier = Modifier
                .width(311.dp)
                .clip(shape = RoundedCornerShape(32.dp))
                .background(R.color.color_white.colorResource())
                .padding(bottom = 28.dp)
                .noRippleClickable {
                    onClick()
                }
        ) {
            val (
                topCenterRef, bottomContent, image1, image0, image2, image3,
                image10, image11, image12, image13, image14, centerIcon, gRef,
            ) = createRefs()

            Spacer(modifier = Modifier
                .width(2.dp)
                .constrainAs(topCenterRef) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)

                })

            OImageView2(photoList.getOrNull(0), modifier = Modifier
                .constrainAs(image0) {
                    end.linkTo(image1.start, 2.dp)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            OImageView2(photoList.getOrNull(1), modifier = Modifier
                .constrainAs(image1) {
                    end.linkTo(topCenterRef.start)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            OImageView2(photoList.getOrNull(2), modifier = Modifier
                .constrainAs(image2) {
                    start.linkTo(topCenterRef.end)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }


            OImageView2(photoList.getOrNull(3), modifier = Modifier
                .constrainAs(image3) {
                    start.linkTo(image2.end, 2.dp)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            OImageView2(photoList.getOrNull(4),modifier = Modifier
                .size(width = 93.dp, height = 112.dp)

                .constrainAs(image12) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(image0.bottom, 2.dp)

                })


            OImageView2(photoList.getOrNull(5), modifier = Modifier
                .constrainAs(image10) {
                    end.linkTo(image11.start, 2.dp)
                    top.linkTo(image12.top)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            OImageView2(photoList.getOrNull(6), modifier = Modifier
                .constrainAs(image11) {
                    end.linkTo(image12.start, 2.dp)
                    top.linkTo(image12.top)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            OImageView2(photoList.getOrNull(7), modifier = Modifier
                .constrainAs(image13) {
                    start.linkTo(image12.end, 2.dp)
                    top.linkTo(image12.top)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            Box(modifier = Modifier
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            R.color.common_color_FFFFFF.colorResource(),
                            R.color.common_translate.colorResource(),
                        ), start = Offset(0f, Float.POSITIVE_INFINITY), end = Offset(0f, 0f)
                    )
                )
                .fillMaxWidth()
                .height(82.dp)
                .constrainAs(gRef) {
                    bottom.linkTo(image13.bottom)
                }
            ){

            }


            OImageView2(photoList.getOrNull(8), modifier = Modifier
                .constrainAs(image14) {
                    start.linkTo(image13.end, 2.dp)
                    top.linkTo(image12.top)
                }
                .size(width = 93.dp, height = 112.dp)
                ) {

            }

            Image(
                painter = painterResource(id = R.drawable.me_edit_activity_pic_open_left_icon),
                contentDescription = "image description",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .size(width = 70.dp, height = 69.dp)
                    .constrainAs(centerIcon) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(image12.top, 55.dp)
                    }
            )

            Column (horizontalAlignment = Alignment.CenterHorizontally,modifier = Modifier
                .padding(horizontal = 24.dp)
                .constrainAs(bottomContent) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(centerIcon.bottom, 19.dp)
                }){
                Text(
                    text = "电子活动相册已上线",
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF292929),
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "我们为你整理了过往参加活动的照片，可以前往查看哦",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF5E5E5E),
                        textAlign = TextAlign.Center,
                    )
                )

                Spacer(modifier = Modifier.height(28.dp))


                Box(contentAlignment = Alignment.Center,modifier = Modifier
                    .fillMaxWidth()
                    .height(46.dp)
                    .background(color = Color(0xFF191919), shape = RoundedCornerShape(size = 25.dp))
                ){
                    Text(
                        text = "去看看",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFFFFFFF),
                            textAlign = TextAlign.Center,
                        )
                    )

                }

            }


        }

        Spacer(modifier = Modifier.height(20.dp))

        Image(painter = R.mipmap.image_white_close_r.painterResource(),
            contentDescription = "",
            modifier = Modifier
                .size(24.dp)
                .noRippleClickable {
                    onDismissed()
                })
    }

}

@Preview
@Composable
fun PreviewF1ActivityPicDialog() {
    F1F1ActivityPicDialog(listOf<String>("","","","","","","","",""))
}