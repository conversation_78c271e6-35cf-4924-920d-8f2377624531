package com.kanzhun.marry.login.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.util.SecurityUtils
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.model.AreaBean
import com.kanzhun.foundation.api.model.CommonAddressResponse
import com.kanzhun.foundation.api.model.UserInfoModel
import com.kanzhun.foundation.api.response.GpsLocationResponse
import com.kanzhun.foundation.base.BirthYearRangeBean
import com.kanzhun.foundation.base.LoveGoalListBean
import com.kanzhun.foundation.bean.UpdateUserInfoResponse
import com.kanzhun.foundation.kernel.account.Account
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.User
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.login.api.LoginApi
import com.kanzhun.marry.login.api.model.OrangeDicProfileTagResponse
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.utils.SettingBuilder
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LList
import com.techwolf.lib.tlog.TLog

private const val TAG = "LoginActivateViewModel"

private const val SEPARATOR = "-"

private const val LEAVE_BLANK = "暂不填写"

class LoginActivateViewModel : BaseViewModel() {
    var type: Int = 0
    val array: ArrayList<Int> = arrayListOf()
    var activateSuccessLivaData = MutableLiveData<Int>()
    var nameErrorLiveData = MutableLiveData<Pair<Int, String?>>()

    var areaOptions1Items: MutableList<AreaBean> = ArrayList()
    var areaOptions2Items: MutableList<MutableList<AreaBean>> = ArrayList()

    var beanList: List<OrangeDicProfileTagResponse.Bean>? = null
    var keyMap: LinkedHashMap<Int, OrangeDicProfileTagResponse.Bean> = LinkedHashMap()
    val user: User? =
        ServiceManager.getInstance().databaseService.userDao.getUser(AccountHelper.getInstance().userId)

    var areaOptions1position = 0
    var areaOptions2position = 0

    var mLoveGoalListBean: MutableLiveData<LoveGoalListBean> = MutableLiveData()
    var mBirthYearRangeBean: MutableLiveData<BirthYearRangeBean> = MutableLiveData()

    private val _locationStr = MutableLiveData<String>()
    val locationStr get() = _locationStr

    fun setLocationStr() {
        _locationStr.value = generateLocationStr()
    }

    fun setLocationStr(str: String) {
        _locationStr.value = str
    }

    fun getData() {
        getAre()
        getProfile()
        getLoveGoalListBean()
        getBrith()
        getUserInfo()

    }

    val initStatus = MutableLiveData<Int>()
    val userInfoLiveData = MutableLiveData<UserInfoModel>()
    val planAB = MutableLiveData<Int>(0)

    fun isPlanA(): Boolean{
        return planAB.value == 1
    }

    fun isPlanB(): Boolean{
        return planAB.value == 2
    }

    fun isPlanC(): Boolean{
        return planAB.value == 3
    }

    private fun getUserInfo() {
        val responseObservable =
            RetrofitManager.getInstance().createApi<FoundationApi?>(FoundationApi::class.java)
                .queryUserInfo()
        HttpExecutor.execute<UserInfoModel?>(responseObservable, object : BaseRequestCallback<UserInfoModel>(true){
            override fun onSuccess(data: UserInfoModel?) {
                if (data != null){
                    userInfoLiveData.postValue(data)
                    initStatus.postValue(1)
                }else{
                    initStatus.postValue(2)
                }
            }

            override fun dealFail(reason: ErrorReason?) {
                initStatus.postValue(2)
            }

        })
    }

    fun getBrith(successRunnable: Runnable? = null, errorRunnable: Runnable? = null) {
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestCommonBrithYearRange()
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<BirthYearRangeBean>() {
                override fun handleInChildThread(data: BirthYearRangeBean) {
                    super.handleInChildThread(data)
                    mBirthYearRangeBean.postValue(data)
                }

                override fun onSuccess(data: BirthYearRangeBean) {
                    successRunnable?.run()
                }

                override fun dealFail(reason: ErrorReason?) {
                    errorRunnable?.run()
                }

            })
    }

    fun getLoveGoalListBean() {
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestSystemLoveGoalList()
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<LoveGoalListBean>() {
                override fun handleInChildThread(data: LoveGoalListBean) {
                    super.handleInChildThread(data)
                    mLoveGoalListBean.postValue(data)
                }

                override fun onSuccess(data: LoveGoalListBean) {
                }

                override fun dealFail(reason: ErrorReason?) {
                    showError(text = reason?.errReason)
                }

            })

    }

    fun getProfile(successRunnable: Runnable? = null, errorRunnable: Runnable? = null) {
        val mOrangeDicProfileTagResponse = RetrofitManager.getInstance().createApi(
            com.kanzhun.marry.login.api.LoginApi::class.java
        ).dicProfileTag(if (type == 2) "1" else "0")
        HttpExecutor.execute(mOrangeDicProfileTagResponse,
            object : BaseRequestCallback<OrangeDicProfileTagResponse?>() {
                override fun handleInChildThread(data: OrangeDicProfileTagResponse?) {
                    super.handleInChildThread(data)
                    if (data == null) return
                    if (LList.isEmpty(data.result)) {
                        return
                    }
                    beanList = data.result
                    if (LList.isEmpty(beanList)) return
                    array.forEach { i ->
                        beanList!!.forEach { j ->
                            j.subTag.forEach { k ->
                                if (i == k.id) {
                                    k.isLocalSelect = true
                                    keyMap[i] = k
                                }
                            }
                        }
                    }
                }

                override fun onSuccess(data: OrangeDicProfileTagResponse?) {
                    successRunnable?.run()
                }

                override fun dealFail(reason: ErrorReason) {
                    errorRunnable?.run()
                }
            })
    }

    fun getAre(successRunnable: Runnable? = null, errorRunnable: Runnable? = null) {

        val commonAddressObservable = RetrofitManager.getInstance().createApi(
            com.kanzhun.marry.login.api.LoginApi::class.java
        ).getCommonAddress(1)
        HttpExecutor.execute(
            commonAddressObservable,
            object : BaseRequestCallback<CommonAddressResponse?>() {
                override fun handleInChildThread(data: CommonAddressResponse?) {
                    super.handleInChildThread(data)
                    if (data == null) return
                    if (LList.isEmpty(data.result)) {
                        return
                    }

                    areaOptions1Items = data.result
                    areaOptions2Items.clear()

                    val areaBean = AreaBean()
                    areaBean.name = LEAVE_BLANK
                    areaBean.code = ""
                    areaBean.children = mutableListOf()
                    val areaBean2 = AreaBean()
                    areaBean2.name = SEPARATOR
                    areaBean2.code = ""
                    areaBean.children.add(areaBean2)
                    areaOptions1Items.add(areaBean)

                    if (!LList.isEmpty(areaOptions1Items)) {
                        for (i in areaOptions1Items.indices) {
                            areaOptions2Items.add(areaOptions1Items[i].children)
                        }
                    }
                    getCode()
                }

                override fun onSuccess(data: CommonAddressResponse?) {
                    successRunnable?.run()
                }

                override fun dealFail(reason: ErrorReason) {
                    errorRunnable?.run()
                }
            })
    }

    fun requestLocation(gpsInfo: String) { // 116.307490,39.984154
        val encGpsInfo = SecurityUtils.rc4Encrypt(
            /* source = */ gpsInfo,
            /* rc4Password = */ SettingBuilder.getInstance().apmUidPassword
        )

        HttpExecutor.execute(RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).getGpsLocation(encGpsInfo), object : BaseRequestCallback<GpsLocationResponse?>(false) {
            override fun onSuccess(data: GpsLocationResponse?) {
                val addressCode = data?.code
                queryOptionsPosition(addressCode)
            }

            private fun queryOptionsPosition(addressCode: String?) {
                addressCode?.let { ac ->
                    // 设置滑轮滑动位置
                    if (ac.isNotEmpty()) {
                        var i = 0
                        areaOptions2Items.forEach {
                            var j = 0
                            it.forEach { second: AreaBean ->
                                if (second.code.equals(ac)) {
                                    areaOptions1position = i
                                    areaOptions2position = j
                                }
                                j++
                            }
                            i++
                        }
                    }

                    // 北京市 朝阳区
                    _locationStr.postValue(generateLocationStr())
                }
            }

            override fun dealFail(reason: ErrorReason?) {

            }

//            override fun onComplete() {
//                super.onComplete()
//                if (BuildInfoUtils.isDebug()) {
//                    queryOptionsPosition("JpNnH93ZIUkaYePHgdG4yJR27GilqnWI79bpn92UMuU~")
//                }
//            }
        })
    }

    private fun generateLocationStr() = try {
        if (areaOptions1position < 0
            || areaOptions2position < 0
            || areaOptions1position >= areaOptions1Items.size
            || areaOptions2position >= areaOptions2Items[areaOptions1position].size
        ) {
            ""
        } else {
            val leftName = areaOptions1Items[areaOptions1position].name
            val rightName = areaOptions2Items[areaOptions1position][areaOptions2position].name
            StringBuilder().apply {
                append(leftName)
                if (leftName != LEAVE_BLANK && rightName != SEPARATOR) {
                    if ("吉林" == leftName || leftName != rightName) {
                        append(" $rightName")
                    }
                }
            }.toString()
        }
    } catch (e: Throwable) {
        TLog.error(TAG, "areaOptions: $e")
        ""
    }

    private fun getCode() {
        user?.apply {
            if (!this.addressCode.isNullOrEmpty()) {
                var i = 0
                areaOptions2Items.forEach {
                    var j = 0
                    it.forEach { second: AreaBean ->
                        if (second.code.equals(addressCode)) {
                            areaOptions1position = i
                            areaOptions2position = j
                        }
                        j++
                    }
                    i++
                }
            }
        }
    }

    fun saveName(name: String) {
        val params = mutableMapOf<String, Any?>()
        params["nickName"] = name

        val observable = RetrofitManager.getInstance().createApi(
            LoginApi::class.java
        ).updateUserInfo(params)

        HttpExecutor.execute(observable, object : BaseRequestCallback<UpdateUserInfoResponse>() {
            override fun handleInChildThread(data: UpdateUserInfoResponse?) {
                super.handleInChildThread(data)
                user?.nickName = name
                //更新数据库
                ServiceManager.getInstance().databaseService.userDao.insert(user)
            }

            override fun onSuccess(data: UpdateUserInfoResponse?) {
                activateSuccessLivaData.postValue(1)
            }

            override fun dealFail(reason: ErrorReason?) {
                if (reason != null) {
                    if (reason.errCode > 0) {
                        when (reason.errCode) {
                            1105, 1106 -> {
                                nameErrorLiveData.postValue(
                                    Pair(
                                        reason.errCode, reason.errReason
                                    )
                                )

                            }

                            else -> {
                                T.ss(reason.errReason)
                            }
                        }
                    } else {
                        T.ss(reason.errReason)
                    }
                }
            }

        })
    }

    fun saveGender(runnable: Runnable) {
        val params = mutableMapOf<String, Any?>()
        params["gender"] = user?.gender
        updateInfo(params, runnable)
    }

    fun saveBirthday(runnable: Runnable) {
        val params = mutableMapOf<String, Any?>()
        params["birthday"] = user?.birthday
        updateInfo(params, runnable)
    }

    fun saveAddressCode(code: String, runnable: Runnable) {
        val params = mutableMapOf<String, Any?>()
        params["addressCode"] = code
        updateInfo(params, runnable) {
            user?.addressCode = code
        }
    }

    fun saveDegree(degree: Int, schoolTime: Int, runnable: Runnable) {
        val params = mutableMapOf<String, Any?>()
        params["degree"] = degree
        params["schoolTime"] = schoolTime
        updateInfo(params, runnable) {
            user?.degree = degree
            user?.schoolTime = schoolTime
        }
    }

    fun saveLoveGoal(runnable: Runnable) {
        val params = mutableMapOf<String, Any?>()
        params["loveGoal"] = user?.loveGoal
        updateInfo(params, runnable)
    }

    private fun updateInfo(
        params: Map<String, Any?>,
        runnable: Runnable,
        beforeInsert: Runnable? = null,
    ) {
        val observable = RetrofitManager.getInstance().createApi(
            LoginApi::class.java
        ).updateUserInfo(params)

        HttpExecutor.execute(observable, object : BaseRequestCallback<UpdateUserInfoResponse>() {
            override fun handleInChildThread(data: UpdateUserInfoResponse?) {
                super.handleInChildThread(data)
                beforeInsert?.run()
                //更新数据库
                ServiceManager.getInstance().databaseService.userDao.insert(user)
            }

            override fun onSuccess(data: UpdateUserInfoResponse?) {
                runnable.run()
            }

            override fun dealFail(reason: ErrorReason?) {
                T.ss(reason?.errReason)
            }
        })
    }

    fun saveTagIdList() {
        val params = mutableMapOf<String, Any?>()
        val sb = StringBuilder()
        keyMap.forEach {
            sb.append(it.key).append(",")
        }
        params["tagIdList"] = sb.substring(0, sb.toString().length - 1)
        HttpExecutor.requestSimplePost(URLConfig.URL_REGISTER_ADD_INFO,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {
                    finishAllEdit()
                }

                override fun dealFail(reason: ErrorReason?) {
                    T.ss(reason?.errReason)
                }

            })

    }

    fun finishAllEdit() {
        HttpExecutor.requestSimplePost(URLConfig.URL_ORANGE_REGISTER_FINISH_COMPLETE,
            null,
            object : SimpleRequestCallback(true) {
                override fun onSuccess() {
                    loginActivateInit()
                    activateSuccessLivaData.postValue(-1)
                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })

    }

    fun editLabel(run: Runnable) {
        val params = mutableMapOf<String, Any?>()
        val sb = StringBuilder()
        if (keyMap.isEmpty()) {
            params["tagIds"] = ""
        } else {
            keyMap.forEach {
                sb.append(it.key).append(",")
            }
            params["tagIds"] = sb.substring(0, sb.toString().length - 1)
        }

        if (type == 2) {
            HttpExecutor.requestSimplePost(URLConfig.URL_ORANGE_USER_UPDATE_INTEREST_TAG,
                params,
                object : SimpleRequestCallback() {
                    override fun onSuccess() {
                        sendBooleanLiveEvent(LivedataKeyMe.LIKE_CONTENT_UPDATE_TAB, true)
                        run.run()
                    }

                    override fun dealFail(reason: ErrorReason?) {
                        T.ss(reason?.errReason)
                    }

                })
        } else {
            HttpExecutor.requestSimplePost(URLConfig.URL_ORANGE_USER_UPDATE_USER_TAG,
                params,
                object : SimpleRequestCallback() {
                    override fun onSuccess() {
                        sendBooleanLiveEvent(LivedataKeyMe.UPDATE_TAB, true)
                        run.run()
                    }

                    override fun dealFail(reason: ErrorReason?) {
                        T.ss(reason?.errReason)
                    }

                })
        }


    }

    private fun loginActivateInit() {
        AccountHelper.getInstance()
            .refreshActivateConfig(Account.ACCOUNT_STATUS_REGISTER_INVITE_ACTIVATE)
        val account = AccountHelper.getInstance().account
        AccountHelper.getInstance().activateLogin(account)
        val user = ServiceManager.getInstance().databaseService.userDao.getUser(account.userId)
        user.phase = account.phase
        ServiceManager.getInstance().databaseService.userDao.insert(user)
        AccountHelper.getInstance().completeUserInfoFirstTime()//首善完成
    }

    fun getLocalAddressCodeCode(): String {
        return areaOptions1Items[areaOptions1position].children[areaOptions2position].code
    }

}