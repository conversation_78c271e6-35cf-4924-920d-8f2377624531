package com.kanzhun.marry.login.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.marry.R;
import com.qmuiteam.qmui.layout.QMUIConstraintLayout;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;

public class LoveTargetItemBean extends FrameLayout {
    QMUIRoundButton qmuiRoundButton;
    SelectBottomBean mSelectBottomBean;
    QMUIConstraintLayout idQMUIConstraintLayout;

    public LoveTargetItemBean(@NonNull Context context) {
        super(context);
        initView(context);
    }

    public LoveTargetItemBean(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public LoveTargetItemBean(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.layout_love_target_item, this, true);
        qmuiRoundButton = view.findViewById(R.id.idSelect);
        idQMUIConstraintLayout = view.findViewById(R.id.idQMUIConstraintLayout);
    }

    public QMUIRoundButton getQmuiRoundButton() {
        return qmuiRoundButton;
    }

    public void setData(SelectBottomBean mSelectBottomBean) {
        this.mSelectBottomBean = mSelectBottomBean;
        qmuiRoundButton.setText(mSelectBottomBean.content);
        if (mSelectBottomBean.suggest) {
            qmuiRoundButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.mipmap.me_icon_love_target_item_recommend, 0);
        } else {
            qmuiRoundButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0);
        }
    }

    public SelectBottomBean getData() {
        return mSelectBottomBean;
    }

    public void setSelect(boolean b) {
        if (b) {
            idQMUIConstraintLayout.setBorderWidth(QMUIDisplayHelper.dp2px(getContext(), 1));
            idQMUIConstraintLayout.setBorderColor(getContext().getColor(R.color.common_color_FF292929));
            idQMUIConstraintLayout.setBackgroundColor(getContext().getColor(R.color.common_color_EEEEEE));
        } else {
            idQMUIConstraintLayout.setBorderWidth(0);
            idQMUIConstraintLayout.setBorderColor(getContext().getColor(R.color.common_color_FF292929));
            idQMUIConstraintLayout.setBackgroundColor(getContext().getColor(R.color.common_color_F5F5F5));
        }
    }
}
