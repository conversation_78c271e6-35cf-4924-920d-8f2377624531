package com.kanzhun.marry.login.fragment

import android.os.Bundle
import android.util.ArrayMap
import androidx.navigation.fragment.findNavController
import com.kanzhun.common.kotlin.ext.asBackButton
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statusbar.immersive
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.router.MainPageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentMarryPlanSelectBinding
import com.kanzhun.marry.login.api.LoginApi
import com.kanzhun.marry.login.api.model.AddMarryIntentBean
import com.kanzhun.marry.login.api.model.SubmitMarryIntentBean
import com.kanzhun.utils.T
import com.kanzhun.common.kotlin.ui.onClick

class LoginMarryPlanSelectFragment:
    LoginMarryIntentBaseFragment<FragmentMarryPlanSelectBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.tvPhoneTitle.asBackButton {  }
        if(activityViewModel.successLiveData.value?.type == 1){
            mBinding.idLayout1.visible()
            mBinding.idLayout2.gone()
            mBinding.tvPhoneTitle.setLeftIconGone()
        }else{
            activity?.immersive(darkMode = true)
            mBinding.idLayout1.gone()
            mBinding.idLayout2.visible()
        }
        reportPoint("marriageplan-page-expo"){
            actionp2 = activityViewModel.successLiveData.value?.type.toString()
        }
        mBinding.apply {
            tvPhoneTitle.setTitle(activityViewModel.successLiveData.value?.title)
            tvPhoneTitle.setSubTitle(activityViewModel.successLiveData.value?.subTitle)
            tvSelect1.text = activityViewModel.successLiveData.value?.acceptBtnText
            tvSelect2.text = activityViewModel.successLiveData.value?.rejectBtnText

            idTitle2.text = activityViewModel.successLiveData.value?.title
            idSubTitle2.text = activityViewModel.successLiveData.value?.subTitle
            tvSelect3.text = activityViewModel.successLiveData.value?.acceptBtnText
            tvSelect4.text = activityViewModel.successLiveData.value?.rejectBtnText
        }
        mBinding.tvSelect1.onClick {
            sure()
        }
        mBinding.tvSelect2.onClick {
            reportPoint("marriageplan-page-click"){
                actionp2 = activityViewModel.successLiveData.value?.type.toString()
                type = "无计划"
            }
            submitMarryIntent(2){
                findNavController().navigate(R.id.action_marryPlanSelectFragment_to_marryPlanRejectFragment)
            }
        }
        mBinding.tvSelect3.onClick {
            sure()
        }
        mBinding.tvSelect4.onClick {
            reportPoint("marriageplan-page-click"){
                actionp2 = activityViewModel.successLiveData.value?.type.toString()
                type = "无计划"
            }
            submitMarryIntent(2){
                findNavController().navigate(R.id.action_marryPlanSelectFragment_to_marryPlanResultFragment)
            }
        }
    }

    private fun sure() {
        reportPoint("marriageplan-page-click"){
            actionp2 = activityViewModel.successLiveData.value?.type.toString()
            type = "有计划"
        }
        submitMarryIntent(1){
            if (AccountHelper.getInstance().isInvite) {
                //首善
                LoginPageRouter.jumpToChildFirstEdit(context, false)
            } else {
                //去主页面
                MainPageRouter.jumpToMainActivity(context)
            }
            AppUtil.finishActivity(context)
        }
    }

    //@param status 1 有计划  2 无计划
    fun submitMarryIntent(status:Int, callback: ()->Unit = {}) {
        val map = ArrayMap<String, Any>()
        map["status"] = status
        val baseResponseObservable = RetrofitManager.getInstance().createApi(LoginApi::class.java).addMarryIntent(map)
        HttpExecutor.execute(baseResponseObservable, object : BaseRequestCallback<AddMarryIntentBean?>(true) {
            override fun handleInChildThread(response: AddMarryIntentBean?) {
            }

            override fun onSuccess(response: AddMarryIntentBean?) {
                callback()
            }

            override fun dealFail(reason: ErrorReason) {

            }

        })
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}