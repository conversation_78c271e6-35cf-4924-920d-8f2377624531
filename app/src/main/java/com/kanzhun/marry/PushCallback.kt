package com.kanzhun.marry

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.kanzhun.foundation.Constants
import com.kanzhun.marry.main.MainActivity
import com.kanzhun.marry.push.IPushCallback
import com.kanzhun.utils.rxbus.RxBus
import java.text.SimpleDateFormat

class PushCallback : IPushCallback {

    override fun onReceiveMessage(type: Int, message: String) {
        val context: Context = App.getApplication()
        val intent = Intent()
        intent.setClass(context, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        context.startActivity(intent)
    }

    override fun onSystemNotificationClicked(type: Int, message: Map<String, String>) {
        RxBus.getInstance().post(true, Constants.POST_TAG_NOTIFY_CLICKED_IN_MEETING)
        val context: Context = App.getApplication()
        val intent = Intent()
        val bundle = Bundle()
        for ((key, value) in message) {
            bundle.putString(key,value)
        }
        intent.putExtras(bundle)
        intent.setClass(context, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        context.startActivity(intent)
    }

    companion object {
        const val TAG: String = "PushCallback"
    }
}
