package com.balsikandar.crashreporter.test

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.kit.baselibrary.IExtraBean
import com.kit.extra.DialogExtraEntry
import com.kit.extra.ThemeExtraEntry
import com.kit.extra.WXExtraEntry
import com.kit.extra.ZhangzhengExtraEntry

class TestViewModel : ViewModel() {
    val kits: LiveData<List<IExtraBean>> = MutableLiveData(
        listOf(
            LocationSharingEntry(),

            YueYueEntry(),

            MeetingLocationEntry(),
            SafetyGuardEntry(),
            SelectLocationEntry(),
            MakeMeetingPlanEntry(),

            ReCertEntry(),

            ResetEmailEntry(),

            ConversationSettingsEntry(),

            MeetingPlanEntry(),
            MainTabEntry(),

            IntroSuggestEntry(),
            DialogExtraEntry(),
            ZhangzhengExtraEntry(),
            WXExtraEntry(),
            ThemeExtraEntry(),

            AgcGrantDialogEntry(),
            AgcDialogEntry(),
            AgcFeedbackDialogEntry(),
            ActivityPhotoEntry(),
            PhotoPreviewerEntry(),

            TriggerCrashEntry(),
            TriggerNewThreadCrashEntry(),

            WheelPickerEntry(),
        )
    )
}