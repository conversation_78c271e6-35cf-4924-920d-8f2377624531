package com.kit.extra

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.dialog.showSimpleBottomListDialog
import com.kanzhun.common.kotlin.entity.BottomListDialogItem
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.service.IMatchingRouterService
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.dialog.MatchingHomeBlockDialog
import com.kanzhun.marry.me.dialog.UserAuthBlockDialog
import com.kit.baselibrary.IExtraBean
import com.sankuai.waimai.router.Router

class DialogExtraEntry : IExtraBean {
    override fun getName(): String {
        return "弹框测试"
    }

    override fun onClick(context: FragmentActivity) {
        val options: MutableList<BottomListDialogItem> = ArrayList()
        options.add(BottomListDialogItem("想更了解你弹框") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showBaseInfoBlockDialog(
                context,
                UserGuideBlockInfoBean(),
                PageSource.NONE
            )
        })

        options.add(BottomListDialogItem("实名认证弹框") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showFaceBlockDialog(
                context,
                UserGuideBlockInfoBean(),
                PageSource.NONE
            )
        })

        options.add(BottomListDialogItem("头像认证弹框") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showAvatarBlockDialog(context, PageSource.NONE)

        })

        options.add(BottomListDialogItem("实名认证弹框2") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showMeFaceBlockDialog(
                context,
                UserGuideBlockInfoBean(),
                PageSource.NONE
            )

        })

        options.add(BottomListDialogItem("头像认证弹框2") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showMeAvatarBlockDialog(context, PageSource.NONE)

        })

        options.add(BottomListDialogItem("工作认证弹框") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showCompanyBlockDialog(context, PageSource.NONE)

        })

        options.add(BottomListDialogItem("学历认证弹框") { s: String?, integer: Int? ->
            UserAuthBlockDialog.showEduBlockDialog(context, PageSource.NONE)

        })

        options.add(BottomListDialogItem("审核中") { s: String?, integer: Int? ->
            val service = Router.getService(
                IMatchingRouterService::class.java, MatchingPageRouter.MATCHING_SERVICE
            )
            if(context is FragmentActivity){
                service.showUserGuideBlockDialogWhenSendLike(context, UserGuideBlockInfoBean.getDefault(), PageSource.NONE)
            }
        })

        options.add(BottomListDialogItem("解锁-填写个人信息") { s: String?, integer: Int? ->
            val b = RecommendUser()
            b.blockInfo = UserGuideBlockInfoBean()
            b.blockInfo?.baseInfoBlock = true
            MatchingHomeBlockDialog(context as FragmentActivity, b).show()
        })

        options.add(BottomListDialogItem("解锁-实名认证") { s: String?, integer: Int? ->
            val b = RecommendUser()
            b.blockInfo = UserGuideBlockInfoBean()
            b.blockInfo?.faceCertBlock = true
            MatchingHomeBlockDialog(context as FragmentActivity, b).show()
        })


        options.add(BottomListDialogItem("解锁-头像认证") { s: String?, integer: Int? ->
            val b = RecommendUser()
            b.blockInfo = UserGuideBlockInfoBean()
            b.blockInfo?.avatarCertBlock = true
            MatchingHomeBlockDialog(context as FragmentActivity, b).show()
        })

        options.add(BottomListDialogItem("解锁-学历认证") { s: String?, integer: Int? ->
            val b = RecommendUser()
            b.blockInfo = UserGuideBlockInfoBean()
            b.blockInfo?.eduCertBlock = true
            MatchingHomeBlockDialog(context as FragmentActivity, b).show()
        })

        options.add(BottomListDialogItem("解锁-工作认证") { s: String?, integer: Int? ->
            val b = RecommendUser()
            b.blockInfo = UserGuideBlockInfoBean()
            b.blockInfo?.companyCertBlock = true
            MatchingHomeBlockDialog(context as FragmentActivity, b).show()
        })
        context.showSimpleBottomListDialog(options)


    }


}
