package com.kit.extra

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.media.MediaPlayer
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.Gravity
import android.view.View
import androidx.core.text.buildSpannedString
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.dialog.CommonLayoutDialog
import com.kanzhun.common.dialog.CommonSystemCenterDialog
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.dialog.showBottomListDialog
import com.kanzhun.common.dialog.showOneButtonDialog
import com.kanzhun.common.dialog.showTwoButtonDialogStyle2
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.constract.LivedataKeyTask
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.joinNotNull
import com.kanzhun.common.kotlin.ext.phoneToParentName
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.kotlin.ext.sendStringLiveEvent
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.base.H5URL
import com.kanzhun.foundation.api.bean.AcceptBindResp
import com.kanzhun.foundation.api.bean.InviteBindBean
import com.kanzhun.foundation.api.bean.InviteBindResp
import com.kanzhun.foundation.api.model.MatchingPageInfo
import com.kanzhun.foundation.api.model.MatchingPageUserInfo
import com.kanzhun.foundation.bean.MapNavigationLocation
import com.kanzhun.foundation.dialog.DateModeDescriptionDialog
import com.kanzhun.foundation.dialog.MePreviewCharacterMatchingDialog
import com.kanzhun.foundation.dialog.ProtocolConfirmDialog
import com.kanzhun.foundation.facade.SyncDispatch
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.kotlin.common.performance.impl.GeeTestPerformance
import com.kanzhun.foundation.kotlin.ktx.getShowLength
import com.kanzhun.foundation.kotlin.ktx.toCompleteH5Link
import com.kanzhun.foundation.kotlin.ui.dialog.LocationSelectDialog
import com.kanzhun.foundation.kotlin.ui.dialog.LocationSelectType
import com.kanzhun.foundation.map.MapNavigationBottomDialog
import com.kanzhun.foundation.model.H5Model
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.foundation.model.WebViewBean
import com.kanzhun.foundation.model.matching.LikeEachOtherBean
import com.kanzhun.foundation.model.matching.LikeInfo
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.model.profile.CompareContent
import com.kanzhun.foundation.model.profile.LoveAttitude
import com.kanzhun.foundation.model.profile.MatchInfo
import com.kanzhun.foundation.model.profile.MatchUserInfo
import com.kanzhun.foundation.model.profile.RomanticMode
import com.kanzhun.foundation.model.profile.RomanticModeType
import com.kanzhun.foundation.model.profile.Type
import com.kanzhun.foundation.model.profile.TypeScore
import com.kanzhun.foundation.router.AppPageRouter
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.service.OInstallApkService
import com.kanzhun.foundation.utils.ProtocolStringProvider
import com.kanzhun.foundation.utils.SendLikeResultHandler
import com.kanzhun.foundation.zxing.activity.CommonScanActivity
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.R
import com.kanzhun.marry.chat.activity.TopicGameActivity
import com.kanzhun.marry.matching.dialog.MatchingSendLikeExaminingBlockDialog
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeUserGuidePerformance
import com.kanzhun.marry.me.databinding.MeCarAuthFailBottomDialogBinding
import com.kanzhun.marry.me.dialog.MePreviewLoveAttitudeDialog
import com.kanzhun.marry.me.dialog.MePreviewLovePattenDialog
import com.kanzhun.marry.me.identify.activity.AvatarCropActivity
import com.kanzhun.marry.me.info.activity.MeQuestionSelectActivity
import com.kanzhun.marry.me.setting.activity.VerifyCodeActivity
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorManager
import com.kanzhun.marry.parent.databinding.ParentInviteBindDialogBinding
import com.kanzhun.utils.T
import com.kanzhun.utils.rxbus.RxBus
import com.kanzhun.utils.string.appendClickable
import com.kanzhun.webview.dialog.BottomWebViewDialog
import com.kit.baselibrary.IExtraBean
import com.techwolf.lib.tlog.TLog
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale


class QinSanjieExtraEntry : IExtraBean {
    val TAG = this.javaClass.simpleName
    override fun getName(): String {
        return "覃三杰"
    }

    override fun onClick(context: FragmentActivity) {
        val activity = context
        //        ActivityUtils.jump2Url(context, "https://lengjing.weizhipin.com/h5/testing.html?noHead=1", false)
        //                Intent(context, HouseAuthActivity::class.java).apply {
        //                    context.startActivity(this)
        //                }
        //        ProtocolManager.parseProtocol("og://lengjing.weizhipin.com?type=202")
        //        ProtocolHelper.parseProtocol("og://lengjing.weizhipin.com?type=201")
        //        ChatPageRouter.jumpToSingleChatActivity(context,"rbI6j0uwUx7c6XmBi1mn8-JV9jgyCoxCXfbHF45nGr8~")
        //        showAvatarSelected(context)
        //                CarAuthActivity.intent(context, CAR_AUTH_STATUS_NONE)
        //        showSheetDialog(context)
        //        MePageRouter.jumpToAvatarUploadActivity(context,0)
        //                showAuthFailDialog(context)
        //        showLocationSelectDialog(context)
        //        HouseAuthActivity.intent(context, 2)
        //        (context as FragmentActivity).showAvatarProtocolDialog()
        //        AvatarAuthActivity.intent(context, CerBean(status = PhotoCertStatus.TYPE_APPROVE_PHOTO_FAILED, rejectCode = 200, rejectReason = "测试",value = "https://lmg.jj20.com/up/allimg/1112/112GPR626/1Q12FR626-9-1200.jpg"))
        //        showEducationSelectDialog(context)
        //        Intent(context, ParentPreviewUserInfoActivity::class.java).apply {
        //            context.startActivity(this)
        //        }
        //        T.ss(getSimpleCurrentDate())
        //        printTime()
        //        MatchingPageRouter.jumpToReviewListActivity(context)
        //        testViewBindingDialog(context)
        //        getInviteBindList(context)
        //        LiveEventBus.get<String?>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE).post(AccountHelper.getInstance().userId )
        //        showHomeGuideCover(context)
        //        ServiceManager.getInstance().noviceTaskService.send()
        //        (context as FragmentActivity).showAvatarProtocolDialog{
        //
        //        }
        //        NoviceTaskApproveSuccessDialog(context as FragmentActivity).show()
        //                showLikeEachOtherDialog(context)
        //        bindProtocolDialog(context)
        //        sendInfo()
        //        AppUtil.startActivity(context, MeInfoPreviewActivity.createIntent(context, AccountHelper.getInstance().userId))
        //        testStringShowLength()
        //        showCover(context)
        //        ProtocolHelper.userProtocolCoreByLocal(Constants.TO_AVATAR_CERT_401, emptyMap())
        //        testPoint()
        //        Intent(context,LoginEntryActivity::class.java).apply {
        //            context.startActivity(this)
        //        }
//        Intent(context, TestDemoActivity::class.java).apply {
//            context.startActivity(this)
//        }
        //        showInviteBindDialog(context)
        //       context.unbindParentDialog("123",null)
        //        bindProtocolDialog(context)
        //        testVideoChat(context)
        //        testGeeTest(context)
        //        testLovePattenDialog(context)
        //        testLoveAttitudeString()
        //        testSendTopicDirect(context)
        //        testSingleEmojiText()
        //        showSendLikeExamineBlockDialog(context)
        //        testJsonString()
        //        testIcon()
        //        testBottomWebViewDialog(context)
        //        testMoneyNumber()
        openWebViewActivity("https://orange-qa.weizhipin.com/h5/active.html#/active/home/<USER>",1, context)
    }

    private fun openWebViewActivity(url:String, noHead:Int, context:Activity){
        val webViewBean = WebViewBean()
        webViewBean.url = url
        webViewBean.style = if (noHead == 1) WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT else WebViewBean.STYLE_HAS_NORMAL
        val bundle = Bundle()
        bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean)
        AppUtil.startUri(context, AppPageRouter.WEB_VIEW_ACTIVITY, bundle)
    }

    private fun testMapNavigation(context: FragmentActivity){
        val target = MapNavigationLocation(39.949249, 116.539559, "北京朝阳区北京环卫集团北清分公司2","B0FFGZB4BB")
        MapNavigationBottomDialog(context).show(null, target, 3)
    }

    private fun testBottomWebViewDialog(context: Activity) {
        BottomWebViewDialog(context as FragmentActivity).show(H5URL.URL_H5_Five_Insurances_And_One_Fund.toCompleteH5Link())
    }

    private fun testScanActivity(context: Activity){
        Intent(context, CommonScanActivity::class.java).apply {
            context.startActivity(this)
        }
    }

    private fun testMoneyNumber() {
        //        installApk(context)
    }


    private fun testIcon() {
        val id = "8YpupBqyxd6rFBviwAozPmZsSvmSGC-MR3DmDdzDR2k~"
        val icon = "https://lengjing-cdn.zhipin.com/system/public/wbHbEqKtJ0j2YDex9ookMm3WV8ELuc-MhGqSDQdpjVD7U_cExpsMnHcoE7v2oprbjhXUKFAV_uXE6xanw7CB2Q~~_s40.webp"
        val json = JSONObject()
        json.put("userId", id)
        json.put("icon", icon)
        SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CHAT_ICON_SYNC, json.toString())

    }


    private fun showSendLikeExamineBlockDialog(context: Activity) {
        MatchingSendLikeExaminingBlockDialog(context as FragmentActivity, UserGuideBlockInfoBean(
            block = true,
            baseInfoBlock = false,
            faceCertBlock = true,
            avatarCertBlock = true,
            eduCertBlock = true,
            companyCertBlock = true
        ),PageSource.CHAT).show()
    }

    private fun testSingleEmojiText() {
        // 定义正则表达式
        val regex = Regex("""^\[([\u4e00-\u9fa5a-zA-Z]+)\]$""")
        // 测试字符串
        val testStr1 = "[这是一段中文]"
        val testStr2 = "[This is a piece of English]"
        val testStr3 = "[这是一段中文和English混合的文本]"
        val testStr4 = "[这是一段中文，但是没有关闭的中括号"
        val testStr5 = "[[这是一段中文]]"
        val testStr6 = "[[这是一段中文][这是另一段中文]]"

        // 执行匹配并打印结果
        for (testStr in listOf(testStr1, testStr2, testStr3, testStr4, testStr5, testStr6)) {
            val matcher = regex.matches(testStr)
            if (matcher) {
                println("'$testStr' matches the pattern")
            } else {
                println("'$testStr' does not match the pattern")
            }
        }
    }

    private fun testSendTopicDirect(context: Activity) {
        TopicGameActivity.newIntentForDirectSend(context as FragmentActivity, "BCIY67sA8ZS7ZbcUlIgYwQrsu1OyUrfCbZ16zbcWRrg~", "dTS2VeavV-nB6ov8DVOEog5Uic_Ump1mJouAxoXlR28~")
        testLovePattenDialog(context)
    }

    private fun testLoveAttitudeString() {
        val attitiude = LoveAttitude(
            loginUserStatus = 1,
            matchScore = 80,
            status = 1,
            testUrl = "https://www.baidu.com",
            resultUrl = "https://www.baidu.com",
            typeScore = listOf(
                TypeScore(5.0, 4.0, "浪漫型", 3.5),
                TypeScore(5.0, 4.0, "游戏性", 4.9),
                TypeScore(5.0, 3.2, "同伴型", 4.1),
                TypeScore(5.0, 1.3, "现实型", 3.5),
                TypeScore(5.0, 2.3, "占有型", 0.3),
                TypeScore(5.0, 2.8, "奉献型", 3.5)
            ), types = listOf(
                Type("内倾"),
                Type("外倾"),
                Type("理性"),
                Type("直觉")
            )
        )
        Log.i(TAG, "testLoveAttitudeString: ${attitiude.getTypeShowString()}")
        val attitiude1 = LoveAttitude(
            loginUserStatus = 1,
            matchScore = 80,
            status = 1,
            testUrl = "https://www.baidu.com",
            resultUrl = "https://www.baidu.com",
            typeScore = listOf(
                TypeScore(5.0, 4.0, "浪漫型", 4.9),
                TypeScore(5.0, 4.0, "游戏性", 4.9),
                TypeScore(5.0, 3.2, "同伴型", 4.1),
                TypeScore(5.0, 1.3, "现实型", 3.5),
                TypeScore(5.0, 2.3, "占有型", 0.3),
                TypeScore(5.0, 2.8, "奉献型", 3.5)
            ), types = listOf(
                Type("内倾"),
                Type("外倾"),
                Type("理性"),
                Type("直觉")
            )
        )
        Log.i(TAG, "testLoveAttitudeString: ${attitiude1.getTypeShowString()}")
        val attitiude2 = LoveAttitude(
            loginUserStatus = 1,
            matchScore = 80,
            status = 1,
            testUrl = "https://www.baidu.com",
            resultUrl = "https://www.baidu.com",
            typeScore = listOf(
                TypeScore(5.0, 4.0, "浪漫型", 4.9),
                TypeScore(5.0, 4.0, "游戏性", 4.9),
                TypeScore(5.0, 3.2, "同伴型", 4.9),
                TypeScore(5.0, 1.3, "现实型", 4.9),
                TypeScore(5.0, 2.3, "占有型", 4.9),
                TypeScore(5.0, 2.8, "奉献型", 4.9)
            ), types = listOf(
                Type("内倾"),
                Type("外倾"),
                Type("理性"),
                Type("直觉")
            )
        )
        Log.i(TAG, "testLoveAttitudeString: ${attitiude2.getTypeShowString()}")

    }

    private fun testCharacterDialog(context: Activity) {
        MePreviewCharacterMatchingDialog(context as FragmentActivity).show(MatchInfo(true).also {
            it.score = 80
            it.matchContent = "堪称天作之合的搭配，留一分怕你们骄傲"
            it.loginUser = MatchUserInfo().also {
                it.mbtiCode = "ISTJ"
                it.tinyAvatar = "https://img2.baidu.com/it/u=**********,437091952&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
                it.mbtiName = "老司机"
            }
            it.viewUser = MatchUserInfo().also {
                it.mbtiCode = "ISTJ"
                it.tinyAvatar = "https://inews.gtimg.com/newsapp_bt/0/13602448407/1000"
                it.mbtiName = "检查员"
            }
            it.compareContent = listOf(CompareContent("内倾", "外倾", "你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动"),
                CompareContent("理性", "直觉", "心若向阳，无畏悲伤。让阳光洒满每个角落，点亮我们的人生之旅。你们会用类似的方式去为人处事和与世界互动v你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动"),
                CompareContent("理性", "直觉", "心若向阳，无畏悲伤。让阳光洒满每个角落，点亮我们的人生之旅。你们会用类似的方式去为人处事和与世界互动v你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动"),
                CompareContent("理性", "直觉", "心若向阳，无畏悲伤。让阳光洒满每个角落，点亮我们的人生之旅。你们会用类似的方式去为人处事和与世界互动v你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动"),
                CompareContent("情感",
                    "情感",
                    "梦想是一盏明灯，照亮我们前行的路，无论风雨多大，我们都要坚持不懈。你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动你们会用类似的方式去为人处事和与世界互动"))
        })
    }

    private fun testLoveAttitudeDialog(context: Activity) {
        MePreviewLoveAttitudeDialog(context as FragmentActivity).show(loveAttitude = LoveAttitude(
            loginUserStatus = 1,
            matchScore = 80,
            status = 1,
            testUrl = "https://www.baidu.com",
            resultUrl = "https://www.baidu.com",
            typeScore = listOf(
                TypeScore(5.0, 4.0, "浪漫型", 1.0),
                TypeScore(5.0, 4.0, "游戏性", 2.0),
                TypeScore(5.0, 3.2, "同伴型", 4.1),
                TypeScore(5.0, 1.3, "现实型", 5.0),
                TypeScore(5.0, 2.3, "占有型", 0.3),
                TypeScore(5.0, 2.8, "奉献型", 3.5)
            ), types = listOf(
                Type("内倾"),
                Type("外倾"),
                Type("理性"),
                Type("直觉")
            )
        ))
    }

    private fun testLovePattenDialog(context: Activity) {
        MePreviewLovePattenDialog(context as FragmentActivity).show(RomanticMode(
            loginUserPositive = true,
            loginUserProactive = true,
            loginUserStatus = 1,
            matchContent = listOf("你们的恋爱模式倾向相似，是一对天作之合的搭配，留一分怕你们骄傲", R.string.common_long_placeholder.toResourceString()),
            positive = false,
            proactive = true,
            status = 1,
            testUrl = "https://www.baidu.com",
            resultUrl = "https://www.baidu.com",
            types = listOf(
                RomanticModeType("内倾"),
                RomanticModeType("外倾"),
                RomanticModeType("理性"),
                RomanticModeType("直觉")
            )
        ),AccountHelper.getInstance().userId)
    }

    private fun testGeeTest(context: Activity) {
        if (context is FragmentActivity) {
            val performManager = PerformManager(context)
            val performance = GeeTestPerformance(context) {
                T.ss("challenge = ${it.challenge}, validate = ${it.validate}, seccode = ${it.seccode}")
                VerifyCodeActivity.start(context, it)
            }
            performManager.addPerformance(performance)
            performance.startGeeTest()
        }
    }

    private fun testSelectQuestion(context: Activity) {
        Intent(context, MeQuestionSelectActivity::class.java).apply {
            this.putExtra(BundleConstants.BUNDLE_TYPE, 2)
            context.startActivity(this)
        }

    }

    private var mediaWaitingPlayer: MediaPlayer? = null
    private fun testVideoChat(context: Activity) {
        if (mediaWaitingPlayer?.isPlaying == true) {
            mediaWaitingPlayer?.stop()
            return
        }
        mediaWaitingPlayer = MediaPlayer.create(context, R.raw.video_chat_ring)
        mediaWaitingPlayer?.setLooping(true)
        mediaWaitingPlayer?.start()
    }

    private fun testBlockDialog(context: Activity) {

    }


    private fun printTime() {
        Log.i("QinSanjieExtraEntry", "onClick: ${System.currentTimeMillis()}")
        val time = SimpleDateFormat("yyyy-MM-dd hh:mm", Locale.CHINA).parse("2022-12-01 12:30").time
        Log.i("QinSanjieExtraEntry", "onClick1: ${time}")
    }

    private fun showSheetDialog(context: Activity) {
        // 显示相识模式说明页
        DateModeDescriptionDialog().show((context as FragmentActivity).supportFragmentManager,
            DateModeDescriptionDialog.TAG)
    }

    private fun testDialog(context: Activity) {
        val builder = CommonLayoutDialog.Builder(context)
            .setLayoutId(R.layout.matching_dialog_empathy_instruct)
            .setCancelable(true)
            .setGravity(Gravity.BOTTOM)
            .setAnimationStyle(R.style.common_window_bottom_to_top_anim)
            .setCanceledOnTouchOutside(true)
            .add(R.id.tv_positive)
            .setPadding(0, 0)
            .setOnItemClickListener { dialog, view -> dialog.dismiss() }
        builder.create().show()
    }

    private fun testCenterDialog(context: Activity) {
        val builder = CommonSystemCenterDialog.Builder(context)
            .setTitle(context.getString(R.string.social_un_formal_user_title))
            .setPositiveText(context.getResources().getString(R.string.social_jump_to_finish))
            .setNegativeText(context.getResources().getString(R.string.social_just_have_a_look))
            .setButtonClickListener(object : CommonSystemCenterDialog.OnClickListener {
                override fun onPositiveClick(dialog: Dialog, view: View) {
                    // 跳转到我的tab
                    RxBus.getInstance().post("switch", Constants.POST_TAG_MATCHING_REQUIREMENT)
                }

                override fun onNegativeClick(dialog: Dialog, view: View) {}
            })
        builder.create().show()
    }

    private fun testViewBindingDialog(context: Activity) {
        context.showOneButtonDialog("温馨提示", "【喜欢我】列表展示向你发送了喜欢的用户，回复消息即可开始聊天。你已回复了的用户，以及已进入情侣模式、注销或是被封禁用户将不在此展示。", buttonText = "老子晓得了") {
            T.ss("点击了确定")
        }


    }

    private fun testViewBindingDialog1(context: Activity) {
        val text = R.string.common_long_placeholder.toResourceString()
        context.showTwoButtonDialogStyle2("温馨提示", listOf<String>(text, text, text, text, text, text, text, text, text, text).joinNotNull("\n"),
            positiveText = "确定", positiveButtonClick = {
                T.ss("点击了确定")
            }, negativeText = "取消", negativeButtonClick = {
                T.ss("点击了取消")
            }, cancelable = true, canceledOnTouchOutside = true)

    }

    private fun showAvatarSelected(context: Activity) {
        PictureSelectorManager.startSelect(context as FragmentActivity, PictureSelectorManager.SelectType.AVATAR) { cameraFileUri, isGallery ->
            AvatarCropActivity.intent(context, cameraFileUri, isGallery)
        }
    }

    private fun showAuthFailDialog(context: Activity) {
        CommonViewBindingDialog(context,
            mCancelable = true,
            mCanceledOnTouchOutside = true,
            mGravity = Gravity.BOTTOM,
            mPaddingLeft = 0,
            mPaddingRight = 0,
            mAnimationStyle = com.kanzhun.marry.me.R.style.common_window_bottom_to_top_anim,
            onInflateCallback = { inflater, dialog ->
                val binding = MeCarAuthFailBottomDialogBinding.inflate(inflater)
                binding.apply {
                    ivCarAuthPositive.load("https://img2.baidu.com/it/u=2312383180,3750420672&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800")
                    ivCarAuthNegative.load("https://lmg.jj20.com/up/allimg/1112/112GPR626/1Q12FR626-9-1200.jpg")
                    tvReason.text = "人太帅，该打击一下"
                    btnNext.setOnClickListener {
                        dialog.dismiss()
                    }
                }
                binding
            })
            .show()
    }

    private fun showLocationSelectDialog(context: Activity) {
        LocationSelectDialog(context as FragmentActivity, LocationSelectType.ONLY_AREA).show {

        }
    }

    fun getInviteBindList(context: Activity) {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).requestInviteBindList()
        HttpExecutor.execute<InviteBindResp>(observable, object : BaseRequestCallback<InviteBindResp?>(false) {
            override fun onSuccess(data: InviteBindResp?) {
                val inviteList = data?.result ?: listOf()
                val list = inviteList.filter { !it.inviteId.isNullOrBlank() }.map { it.inviteId ?: "" } ?: listOf()
                if (list.isNotEmpty()) {
                    context.showBottomListDialog(list) {
                        accept(list[it])
                    }
                } else {
                    T.ss("暂无邀请")
                }
            }

            override fun dealFail(reason: ErrorReason?) {

            }
        })

    }

    private fun accept(inviteId: String) {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).acceptInviteBind(inviteId)
        HttpExecutor.execute<AcceptBindResp>(observable, object : BaseRequestCallback<AcceptBindResp?>(false) {
            override fun onSuccess(data: AcceptBindResp?) {
                T.ss("接受邀请成功")

            }

            override fun dealFail(reason: ErrorReason?) {
                T.ss("接受邀请失败，${reason}")

            }
        })
    }

    private fun showHomeGuideCover(context: Activity) {
        MatchingHomeUserGuidePerformance.sendShowCoverEvent()
    }

    private fun showLikeEachOtherDialog(context: Activity) {
        SendLikeResultHandler().handleResult(MatchingLikeModel().also {
            it.likeEachOther = LikeEachOtherBean().also { o ->
                o.otherInfo = LikeInfo().also { info -> info.tinyPhoto = "https://img2.baidu.com/it/u=**********,437091952&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" }
                o.myInfo = LikeInfo().also { info -> info.tinyPhoto = "https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" }
            }
        })

    }

    private fun bindProtocolDialog(context: Activity) {
        ProtocolConfirmDialog(context as FragmentActivity, "要与此家长解绑吗？", ProtocolStringProvider.getChildUnBindProtocolString(context),
            {
                T.ss("点击了确定")
            }, {
                T.ss("点击了取消")
            }).show()
    }

    var index = 0
    private fun sendInfo() {
        when (index % 5) {
            0 -> {
                sendStringLiveEvent(LivedataKeyTask.NEW_USER_TASK_FINISH, "1")
            }

            1 -> {
                sendStringLiveEvent(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH, "1")

            }

            2 -> {
                sendBooleanLiveEvent(LivedataKeyMe.AUTH_AVATAR_COMMIT_SUCCESS, true)

            }

            3 -> {

            }

            4 -> {

            }
        }
        index++
    }

    private fun testStringShowLength() {
        TLog.info(TAG, "测试字符串长度:" + "测试字符串长度".getShowLength())
        TLog.info(TAG, "测试字符串长度1:" + "".getShowLength())
        TLog.info(TAG, "测试字符串长度2:" + "娱乐&主播·主编".getShowLength())
        TLog.info(TAG, "176cm.100kg:" + "176cm.100kg".getShowLength())
        TLog.info(TAG, "现居地新疆乌鲁木齐" + "现居地新疆乌鲁木齐".getShowLength())
    }

    private fun showRelationshipDialog(activity: Activity) {
        ChatPageRouter.jumpToMatchingCardActivity(activity, MatchingPageInfo().also {
            it.matchStatus = 2
            it.friend = MatchingPageUserInfo().also { f ->
                f.nickName = "太平洋海王"
            }
        })
    }

    private fun showAppUpdateDialog(activity: Activity) {

    }

    private fun installApk(activity: Activity) {
        activity.startService(OInstallApkService.getIntent(
            activity,
            "https://lengjing-cdn.zhipin.com/system/public/SeVdc_emHDYgFxoUtH04J8QrRh5LZuceqDJzj-3lqjyzvR8UmCD83Vnw2C4GHNyp4xqiePe5JTT8OwO-BgDl_g~~.apk"))

    }


    private fun showInviteBindDialog(context: Activity) {
        val bean = InviteBindBean("1111", "22222", "14000001050", "https://img2.baidu.com/it/u=**********,437091952&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500")
        CommonViewBindingDialog(context,
            mCancelable = false,
            mCanceledOnTouchOutside = false,
            mGravity = Gravity.CENTER, //这里可以设置对话框显示的位置
            mPaddingLeft = 32,
            mPaddingRight = 32,
            onInflateCallback = { inflater, dialog ->
                val binding = ParentInviteBindDialogBinding.inflate(inflater)
                binding.apply {
                    tvDesc.movementMethod = LinkMovementMethod.getInstance()
                    if (AccountHelper.getInstance().isParent) {
                        tvUserName.text = bean.phone
                        ivAvatar.loadRoundUrl(bean.tinyAvatar, 40.dpI)
                        tvDesc.text = getProtocol("绑定后将用孩子资料作为展示，点击接受即表示您同意")
                    } else {
                        tvUserName.text = bean.phone.phoneToParentName()
                        ivAvatar.loadResource(com.kanzhun.marry.parent.R.mipmap.parent_icon_invite_bind)
                        tvDesc.text = getProtocol("请求与您绑定亲属账号，点击接受即表示您同意")
                    }
                    tvAccept.clickWithTrigger {
                        dialog.dismiss()
                    }
                    tvRefuse.clickWithTrigger {
                        dialog.dismiss()
                    }
                }
                binding
            }).show()
    }

    private fun getProtocol(desc: String): CharSequence {
        return buildSpannedString {
            append(desc)
            appendClickable("账号绑定协议", com.kanzhun.marry.parent.R.color.common_color_0046BD.toResourceColor(), false) {
                H5Model.URL_BIND_PROTOCOL.open()

            }
        }
    }


}