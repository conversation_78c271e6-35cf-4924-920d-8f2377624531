<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundColor">

    <Button
        android:id="@+id/btnViewActivity"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_margin="20dp"
        android:text="@string/title_activity_view"
        app:layout_constraintBottom_toTopOf="@id/btnComposeActivity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btnComposeActivity"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_margin="20dp"
        android:text="@string/title_activity_compose"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnViewActivity" />

</androidx.constraintlayout.widget.ConstraintLayout>